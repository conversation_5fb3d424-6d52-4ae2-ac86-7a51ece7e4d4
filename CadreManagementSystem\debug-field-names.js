/**
 * 调试字段名问题
 */
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, 'cadre_management.db');

console.log('🔍 检查数据库字段名...');

const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ 连接数据库失败:', err.message);
    return;
  }
  console.log('✅ 已连接到数据库');
});

// 检查表结构
db.all("PRAGMA table_info(cadres)", [], (err, columns) => {
  if (err) {
    console.error('❌ 获取表结构失败:', err.message);
    return;
  }
  
  console.log('\n📋 cadres表的字段结构:');
  columns.forEach(col => {
    console.log(`  ${col.name} (${col.type})`);
  });
  
  // 检查出生日期相关字段
  const birthDateFields = columns.filter(col => 
    col.name.includes('出生') || col.name.includes('birth')
  );
  
  console.log('\n🎯 出生日期相关字段:');
  birthDateFields.forEach(field => {
    console.log(`  - ${field.name}`);
  });
  
  // 查看实际数据
  db.all("SELECT 姓名, 性别, 出生日期 FROM cadres WHERE 姓名 = '武家明' LIMIT 1", [], (err, rows) => {
    if (err) {
      console.error('❌ 查询数据失败:', err.message);
      return;
    }
    
    if (rows.length > 0) {
      const row = rows[0];
      console.log('\n📊 武家明的数据:');
      console.log(`  姓名: ${row.姓名}`);
      console.log(`  性别: ${row.性别}`);
      console.log(`  出生日期: ${row.出生日期}`);
      
      // 计算年龄
      if (row.出生日期) {
        const birthDate = new Date(row.出生日期);
        const today = new Date();
        const age = today.getFullYear() - birthDate.getFullYear();
        const monthDiff = today.getMonth() - birthDate.getMonth();
        let currentAge = age;
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
          currentAge--;
        }
        
        console.log(`  当前年龄: ${currentAge}岁`);
        console.log(`  退休年龄: 60岁 (男性)`);
        console.log(`  距离退休: ${60 - currentAge}年`);
        
        if (currentAge >= 55) {
          console.log('  ⚠️ 应该有退休预警！');
        }
      }
    } else {
      console.log('\n❌ 未找到武家明的数据');
    }
    
    db.close();
  });
});
