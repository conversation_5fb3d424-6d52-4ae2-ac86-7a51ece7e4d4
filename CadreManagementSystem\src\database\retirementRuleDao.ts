import { getDatabase } from './database';
import { RetirementRule } from '../types';

export class RetirementRuleDao {
  // 获取所有退休规则
  static async getAllRetirementRules(): Promise<RetirementRule[]> {
    const db = getDatabase();
    
    try {
      const result = await db.getAllAsync(`
        SELECT rr.*, pl.name as position_level_name 
        FROM retirement_rules rr
        LEFT JOIN position_levels pl ON rr.position_level_id = pl.id
        WHERE rr.is_active = 1
        ORDER BY rr.position_level_id, rr.gender
      `);
      
      return result as RetirementRule[];
    } catch (error) {
      console.error('获取退休规则失败:', error);
      throw new Error(`获取退休规则失败: ${error}`);
    }
  }

  // 根据职级和性别获取退休规则
  static async getRetirementRule(positionLevelId: number | null, gender: string): Promise<RetirementRule | null> {
    const db = getDatabase();
    
    try {
      // 优先查找特定职级的规则
      let result = null;
      if (positionLevelId) {
        result = await db.getFirstAsync(`
          SELECT * FROM retirement_rules 
          WHERE position_level_id = ? AND (gender = ? OR gender = 'all') AND is_active = 1
          ORDER BY gender DESC
        `, [positionLevelId, gender]);
      }
      
      // 如果没有找到特定职级规则，查找通用规则
      if (!result) {
        result = await db.getFirstAsync(`
          SELECT * FROM retirement_rules 
          WHERE position_level_id IS NULL AND (gender = ? OR gender = 'all') AND is_active = 1
          ORDER BY gender DESC
        `, [gender]);
      }
      
      return result as RetirementRule || null;
    } catch (error) {
      console.error('获取退休规则失败:', error);
      throw new Error(`获取退休规则失败: ${error}`);
    }
  }

  // 添加退休规则
  static async addRetirementRule(rule: RetirementRule): Promise<number> {
    const db = getDatabase();
    
    try {
      const result = await db.runAsync(`
        INSERT INTO retirement_rules (
          position_level_id, gender, base_retirement_age, delay_start_year,
          delay_months_per_year, max_retirement_age, is_active, description
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        rule.position_level_id || null,
        rule.gender,
        rule.base_retirement_age,
        rule.delay_start_year,
        rule.delay_months_per_year,
        rule.max_retirement_age,
        rule.is_active ? 1 : 0,
        rule.description || ''
      ]);
      
      console.log('退休规则添加成功, ID:', result.lastInsertRowId);
      return result.lastInsertRowId;
    } catch (error) {
      console.error('添加退休规则失败:', error);
      throw new Error(`添加退休规则失败: ${error}`);
    }
  }

  // 更新退休规则
  static async updateRetirementRule(rule: RetirementRule): Promise<void> {
    const db = getDatabase();
    
    try {
      await db.runAsync(`
        UPDATE retirement_rules SET
          position_level_id = ?, gender = ?, base_retirement_age = ?,
          delay_start_year = ?, delay_months_per_year = ?, max_retirement_age = ?,
          is_active = ?, description = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `, [
        rule.position_level_id || null,
        rule.gender,
        rule.base_retirement_age,
        rule.delay_start_year,
        rule.delay_months_per_year,
        rule.max_retirement_age,
        rule.is_active ? 1 : 0,
        rule.description || '',
        rule.id
      ]);
      
      console.log('退休规则更新成功:', rule.id);
    } catch (error) {
      console.error('更新退休规则失败:', error);
      throw new Error(`更新退休规则失败: ${error}`);
    }
  }

  // 删除退休规则
  static async deleteRetirementRule(id: number): Promise<void> {
    const db = getDatabase();
    
    try {
      await db.runAsync(`
        UPDATE retirement_rules SET is_active = 0, updated_at = CURRENT_TIMESTAMP 
        WHERE id = ?
      `, [id]);
      
      console.log('退休规则删除成功:', id);
    } catch (error) {
      console.error('删除退休规则失败:', error);
      throw new Error(`删除退休规则失败: ${error}`);
    }
  }

  // 计算延迟退休年龄（简化版本）
  static calculateDelayedRetirementAge(
    birthYear: number,
    gender: string,
    positionLevelId?: number
  ): { retirementAge: number; retirementYear: number; description: string } {
    // 简化的延迟退休计算逻辑
    const baseRetirementAge = gender === '女' ? 55 : 60;

    let delayMonths = 0;
    if (birthYear >= 1965) {
      // 基础延迟3个月
      delayMonths = 3;

      if (birthYear >= 1966) {
        // 1966年及以后的额外延迟
        const extraYears = birthYear - 1965;

        if (birthYear <= 1970) {
          // 1966-1970: 每年额外2个月，最多1年
          delayMonths += Math.min(extraYears * 2, 12);
        } else if (birthYear <= 1980) {
          // 1971-1980: 前5年每年2个月，后续每年3个月，最多3年
          const firstPhase = Math.min(5, extraYears) * 2;
          const secondPhase = Math.max(0, extraYears - 5) * 3;
          delayMonths += Math.min(firstPhase + secondPhase, 36);
        } else {
          // 1981+: 前5年每年2个月，6-10年每年3个月，后续每年4个月，最多5年
          const firstPhase = Math.min(5, extraYears) * 2;
          const secondPhase = Math.min(5, Math.max(0, extraYears - 5)) * 3;
          const thirdPhase = Math.max(0, extraYears - 10) * 4;
          delayMonths += Math.min(firstPhase + secondPhase + thirdPhase, 60);
        }
      }
    }

    const actualRetirementAge = baseRetirementAge + (delayMonths / 12);
    const delayYears = Math.floor(delayMonths / 12);
    const remainingMonths = delayMonths % 12;

    const delayText = delayYears > 0 || remainingMonths > 0
      ? `延迟${delayYears}年${remainingMonths}个月，`
      : '';

    const description = `${delayText}实际退休年龄${actualRetirementAge.toFixed(1)}岁`;
    const retirementYear = birthYear + Math.ceil(actualRetirementAge);

    return {
      retirementAge: actualRetirementAge,
      retirementYear,
      description
    };
  }

  // 初始化默认退休规则
  static async initializeDefaultRetirementRules(): Promise<void> {
    const db = getDatabase();
    
    const defaultRules = [
      // 通用规则
      {
        position_level_id: null,
        gender: 'male',
        base_retirement_age: 60,
        delay_start_year: 2025,
        delay_months_per_year: 3,
        max_retirement_age: 65,
        description: '男性通用延迟退休规则'
      },
      {
        position_level_id: null,
        gender: 'female',
        base_retirement_age: 55,
        delay_start_year: 2025,
        delay_months_per_year: 3,
        max_retirement_age: 60,
        description: '女性通用延迟退休规则'
      }
    ];

    try {
      await db.withTransactionAsync(async () => {
        for (const rule of defaultRules) {
          // 检查是否已存在
          const existing = await db.getFirstAsync(`
            SELECT id FROM retirement_rules 
            WHERE position_level_id IS NULL AND gender = ?
          `, [rule.gender]);
          
          if (!existing) {
            await db.runAsync(`
              INSERT INTO retirement_rules (
                position_level_id, gender, base_retirement_age, delay_start_year,
                delay_months_per_year, max_retirement_age, is_active, description
              ) VALUES (?, ?, ?, ?, ?, ?, 1, ?)
            `, [
              rule.position_level_id,
              rule.gender,
              rule.base_retirement_age,
              rule.delay_start_year,
              rule.delay_months_per_year,
              rule.max_retirement_age,
              rule.description
            ]);
          }
        }
      });
      
      console.log('默认退休规则初始化成功');
    } catch (error) {
      console.error('初始化默认退休规则失败:', error);
      throw new Error(`初始化默认退休规则失败: ${error}`);
    }
  }
}
