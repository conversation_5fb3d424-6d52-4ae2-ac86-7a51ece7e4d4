/**
 * 退休配置数据访问层
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { 
  RetirementConfig, 
  SecondLineRule, 
  RetirementRule, 
  RetiredRule, 
  DelayRetirementConfig,
  DEFAULT_RETIREMENT_CONFIG 
} from '../types/retirementConfig';

const STORAGE_KEY = 'retirement_config';

export class RetirementConfigDao {
  
  /**
   * 获取退休配置
   */
  static async getConfig(): Promise<RetirementConfig> {
    try {
      const configStr = await AsyncStorage.getItem(STORAGE_KEY);
      if (configStr) {
        const config = JSON.parse(configStr);
        console.log('📋 获取退休配置成功');
        return config;
      } else {
        console.log('📋 使用默认退休配置');
        await this.saveConfig(DEFAULT_RETIREMENT_CONFIG);
        return DEFAULT_RETIREMENT_CONFIG;
      }
    } catch (error) {
      console.error('❌ 获取退休配置失败:', error);
      return DEFAULT_RETIREMENT_CONFIG;
    }
  }

  /**
   * 保存退休配置
   */
  static async saveConfig(config: RetirementConfig): Promise<boolean> {
    try {
      config.lastUpdated = new Date().toISOString();
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(config));
      console.log('✅ 保存退休配置成功');
      return true;
    } catch (error) {
      console.error('❌ 保存退休配置失败:', error);
      return false;
    }
  }

  /**
   * 获取启用的退居二线规则
   */
  static async getEnabledSecondLineRules(): Promise<SecondLineRule[]> {
    const config = await this.getConfig();
    return config.secondLineRules.filter(rule => rule.enabled);
  }

  /**
   * 获取启用的退休预警规则
   */
  static async getEnabledRetirementRules(): Promise<RetirementRule[]> {
    const config = await this.getConfig();
    return config.retirementRules.filter(rule => rule.enabled);
  }

  /**
   * 获取启用的已退休规则
   */
  static async getEnabledRetiredRules(): Promise<RetiredRule[]> {
    const config = await this.getConfig();
    return config.retiredRules.filter(rule => rule.enabled);
  }

  /**
   * 获取启用的延迟退休配置
   */
  static async getEnabledDelayConfigs(): Promise<DelayRetirementConfig[]> {
    const config = await this.getConfig();
    return config.delayConfigs.filter(config => config.enabled);
  }

  /**
   * 添加退居二线规则
   */
  static async addSecondLineRule(rule: Omit<SecondLineRule, 'id' | 'createdAt' | 'updatedAt'>): Promise<boolean> {
    try {
      const config = await this.getConfig();
      const newRule: SecondLineRule = {
        ...rule,
        id: `second_line_${Date.now()}`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      config.secondLineRules.push(newRule);
      return await this.saveConfig(config);
    } catch (error) {
      console.error('❌ 添加退居二线规则失败:', error);
      return false;
    }
  }

  /**
   * 更新退居二线规则
   */
  static async updateSecondLineRule(ruleId: string, updates: Partial<SecondLineRule>): Promise<boolean> {
    try {
      const config = await this.getConfig();
      const ruleIndex = config.secondLineRules.findIndex(rule => rule.id === ruleId);
      if (ruleIndex === -1) return false;

      config.secondLineRules[ruleIndex] = {
        ...config.secondLineRules[ruleIndex],
        ...updates,
        updatedAt: new Date().toISOString()
      };
      return await this.saveConfig(config);
    } catch (error) {
      console.error('❌ 更新退居二线规则失败:', error);
      return false;
    }
  }

  /**
   * 删除退居二线规则
   */
  static async deleteSecondLineRule(ruleId: string): Promise<boolean> {
    try {
      const config = await this.getConfig();
      config.secondLineRules = config.secondLineRules.filter(rule => rule.id !== ruleId);
      return await this.saveConfig(config);
    } catch (error) {
      console.error('❌ 删除退居二线规则失败:', error);
      return false;
    }
  }

  /**
   * 添加退休预警规则
   */
  static async addRetirementRule(rule: Omit<RetirementRule, 'id' | 'createdAt' | 'updatedAt'>): Promise<boolean> {
    try {
      const config = await this.getConfig();
      const newRule: RetirementRule = {
        ...rule,
        id: `retirement_${Date.now()}`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      config.retirementRules.push(newRule);
      return await this.saveConfig(config);
    } catch (error) {
      console.error('❌ 添加退休预警规则失败:', error);
      return false;
    }
  }

  /**
   * 更新退休预警规则
   */
  static async updateRetirementRule(ruleId: string, updates: Partial<RetirementRule>): Promise<boolean> {
    try {
      const config = await this.getConfig();
      const ruleIndex = config.retirementRules.findIndex(rule => rule.id === ruleId);
      if (ruleIndex === -1) return false;

      config.retirementRules[ruleIndex] = {
        ...config.retirementRules[ruleIndex],
        ...updates,
        updatedAt: new Date().toISOString()
      };
      return await this.saveConfig(config);
    } catch (error) {
      console.error('❌ 更新退休预警规则失败:', error);
      return false;
    }
  }

  /**
   * 添加延迟退休配置
   */
  static async addDelayConfig(config: Omit<DelayRetirementConfig, 'id' | 'createdAt' | 'updatedAt'>): Promise<boolean> {
    try {
      const retirementConfig = await this.getConfig();
      const newConfig: DelayRetirementConfig = {
        ...config,
        id: `delay_${Date.now()}`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      retirementConfig.delayConfigs.push(newConfig);
      return await this.saveConfig(retirementConfig);
    } catch (error) {
      console.error('❌ 添加延迟退休配置失败:', error);
      return false;
    }
  }

  /**
   * 重置为默认配置
   */
  static async resetToDefault(): Promise<boolean> {
    try {
      await AsyncStorage.removeItem(STORAGE_KEY);
      console.log('✅ 重置退休配置为默认值');
      return true;
    } catch (error) {
      console.error('❌ 重置退休配置失败:', error);
      return false;
    }
  }
}
