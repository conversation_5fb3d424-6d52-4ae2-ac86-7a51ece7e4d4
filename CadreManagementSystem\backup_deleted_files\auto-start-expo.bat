@echo off
echo === EXPO自动启动脚本 ===
echo.

REM 设置Node.js路径
set PATH=C:\Program Files\nodejs;%PATH%

REM 切换到项目目录
cd /d "%~dp0"

echo 当前目录: %CD%
echo.

REM 获取本机IP地址
echo 正在获取本机IP地址...
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /i "IPv4"') do (
    for /f "tokens=1" %%b in ("%%a") do (
        set LOCAL_IP=%%b
        goto :found_ip
    )
)
:found_ip
echo 本机IP地址: %LOCAL_IP%
echo.

echo 尝试启动EXPO开发服务器...
echo.

echo === 方法1: 隧道模式 ===
echo 命令: npx expo start --tunnel --port 8081
echo 这将创建一个公网可访问的URL
echo.
npx expo start --tunnel --port 8081

REM 如果上面失败，尝试其他方法
if %errorlevel% neq 0 (
    echo.
    echo === 方法1失败，尝试方法2: 局域网模式 ===
    echo 命令: npx expo start --lan --port 8081
    echo.
    npx expo start --lan --port 8081
)

if %errorlevel% neq 0 (
    echo.
    echo === 方法2失败，尝试方法3: 默认模式 ===
    echo 命令: npx expo start --port 8081
    echo.
    npx expo start --port 8081
)

if %errorlevel% neq 0 (
    echo.
    echo === 所有方法失败，显示手动连接信息 ===
    echo.
    echo 请在雷电模拟器的Expo Go中手动输入以下地址之一:
    echo.
    echo 1. exp://********:8081
    echo 2. exp://%LOCAL_IP%:8081
    echo 3. http://localhost:8081
    echo.
    echo 或者尝试直接运行Android版本:
    echo npx expo run:android
    echo.
)

pause
