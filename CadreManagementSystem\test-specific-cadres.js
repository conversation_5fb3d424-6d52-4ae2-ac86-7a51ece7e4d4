/**
 * 测试特定干部的退休预警计算
 */
console.log('🔍 测试特定干部的退休预警计算...\n');

// 从EXPO日志中提取的真实数据
const realCadres = [
  {
    姓名: '张学亮',
    性别: '男',
    出生日期: '1975-04-22',
    职务: '某职务'
  },
  {
    姓名: '史兵兵',
    性别: '男',
    出生日期: '1970-03-18',
    职务: '某职务'
  },
  {
    姓名: '焦智全',
    性别: '男',
    出生日期: '1972-11-07',
    职务: '某职务'
  },
  {
    姓名: '杨军虎',
    性别: '男',
    出生日期: '1968-02-14',
    职务: '某职务'
  },
  {
    姓名: '车永军',
    性别: '男',
    出生日期: '1970-01-18',
    职务: '某职务'
  },
  {
    姓名: '张培峰',
    性别: '男',
    出生日期: '1981-02-26',
    职务: '生活服务中心一级主管业务员'
  },
  {
    姓名: '张松霞',
    性别: '女',
    出生日期: '1974-04-28',
    职务: '生活服务中心一级主管业务员'
  },
  {
    姓名: '卫鹏',
    性别: '男',
    出生日期: '1983-03-02',
    职务: '生活服务中心一级主管业务员'
  },
  {
    姓名: '武家明',
    性别: '男',
    出生日期: '1969-09-28',
    职务: '机运队副队长'
  },
  {
    姓名: '王永雷',
    性别: '男',
    出生日期: '1981-08-13',
    职务: '机运队副队长'
  }
];

// 模拟ConfigurableRetirementCalculator的逻辑
function parseBirthDate(birthDateStr) {
  if (!birthDateStr) return null;
  
  try {
    const cleanStr = birthDateStr.replace(/[年月日\-\.\/]/g, "-");
    const parts = cleanStr.split("-").filter(p => p.length > 0);
    
    if (parts.length >= 2) {
      const year = parseInt(parts[0]);
      const month = parseInt(parts[1]) - 1;
      const day = parts.length >= 3 ? parseInt(parts[2]) : 1;
      
      if (year > 1900 && year < 2100 && month >= 0 && month < 12) {
        return new Date(year, month, day);
      }
    }
  } catch (error) {
    console.error("解析出生日期失败:", birthDateStr, error);
  }
  
  return null;
}

function calculateAge(birthDate, currentDate) {
  const age = currentDate.getFullYear() - birthDate.getFullYear();
  const monthDiff = currentDate.getMonth() - birthDate.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && currentDate.getDate() < birthDate.getDate())) {
    return age - 1;
  }
  
  return age;
}

function getBaseRetirementAge(cadre) {
  const gender = cadre.性别;
  const position = cadre.现职级 || cadre.职务 || "";
  
  if (gender === "男") {
    return 60;
  } else if (gender === "女") {
    const isCadre = position.includes("职") || position.includes("长") || 
                    position.includes("主任") || position.includes("副主任") ||
                    position.includes("正职") || position.includes("副职");
    return isCadre ? 55 : 50;
  }
  
  return 60;
}

function calculateDelayRetirement(birthYear) {
  let totalDelayMonths = 0;
  
  if (birthYear <= 1964) {
    totalDelayMonths = 0;
  } else if (birthYear === 1965) {
    totalDelayMonths = 3;
  } else if (birthYear >= 1966 && birthYear <= 1970) {
    totalDelayMonths = 3;
    const extraYears = birthYear - 1965;
    const extraMonths = Math.min(extraYears * 2, 12);
    totalDelayMonths += extraMonths;
  } else if (birthYear >= 1971 && birthYear <= 1980) {
    totalDelayMonths = 3 + 12;
    const extraYears = birthYear - 1970;
    const extraMonths = Math.min(extraYears * 3, 24);
    totalDelayMonths += extraMonths;
  } else if (birthYear >= 1981) {
    totalDelayMonths = 3 + 12 + 24;
    const extraYears = birthYear - 1980;
    const extraMonths = Math.min(extraYears * 4, 24);
    totalDelayMonths += extraMonths;
  }

  return {
    delayYears: Math.floor(totalDelayMonths / 12),
    delayMonths: totalDelayMonths % 12,
    totalDelayMonths
  };
}

function analyzeSingleCadre(cadre, today) {
  console.log(`\n🔍 分析干部: ${cadre.姓名} (性别:${cadre.性别}, 出生日期:${cadre.出生日期})`);
  
  const birthDate = parseBirthDate(cadre.出生日期);
  if (!birthDate) {
    console.log(`❌ ${cadre.姓名}: 出生日期解析失败 '${cadre.出生日期}'`);
    return null;
  }

  const age = calculateAge(birthDate, today);
  const birthYear = birthDate.getFullYear();
  const baseRetirementAge = getBaseRetirementAge(cadre);
  const delayInfo = calculateDelayRetirement(birthYear);
  const actualRetirementAge = baseRetirementAge + (delayInfo.totalDelayMonths / 12);

  const retirementDate = new Date(birthDate);
  retirementDate.setFullYear(retirementDate.getFullYear() + Math.floor(actualRetirementAge));
  retirementDate.setMonth(retirementDate.getMonth() + Math.round((actualRetirementAge % 1) * 12));

  const daysRemaining = Math.ceil((retirementDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

  console.log(`📊 ${cadre.姓名}: 年龄${age}岁, 基础退休${baseRetirementAge}岁, 延迟${delayInfo.totalDelayMonths}个月, 实际退休${actualRetirementAge.toFixed(1)}岁, 距离${daysRemaining}天`);

  // 检查预警条件
  if (daysRemaining <= 0) {
    console.log(`⚠️ ${cadre.姓名}: 已退休预警`);
    return { warningType: "retired", daysRemaining: Math.abs(daysRemaining) };
  }

  if (daysRemaining <= 730) {
    console.log(`⚠️ ${cadre.姓名}: 近两年退休预警 (${daysRemaining}天 <= 730天)`);
    return { warningType: "near_retirement", daysRemaining };
  }

  console.log(`✅ ${cadre.姓名}: 无预警 (${daysRemaining}天 > 730天)`);
  return null;
}

// 执行测试
console.log('🚀 开始分析真实干部数据...\n');

const today = new Date();
const warnings = [];

realCadres.forEach(cadre => {
  const warning = analyzeSingleCadre(cadre, today);
  if (warning) {
    warnings.push({ cadre, ...warning });
  }
});

console.log(`\n📊 分析结果:`);
console.log(`总人数: ${realCadres.length}`);
console.log(`预警人数: ${warnings.length}`);

if (warnings.length > 0) {
  console.log(`\n⚠️ 预警详情:`);
  warnings.forEach((warning, index) => {
    console.log(`${index + 1}. ${warning.cadre.姓名} - ${warning.warningType} - ${warning.daysRemaining}天`);
  });
} else {
  console.log(`\n❌ 没有生成任何预警！`);
  console.log(`这说明所有人距离退休都超过730天（2年）`);
}

console.log(`\n🎯 结论:`);
if (warnings.length === 0) {
  console.log(`1. 延迟退休政策导致所有人退休时间推迟`);
  console.log(`2. 预警条件设置为730天（2年内）是正确的`);
  console.log(`3. 需要检查是否有更老的干部数据`);
} else {
  console.log(`1. 退休预警计算逻辑正常`);
  console.log(`2. 字段名修复生效`);
  console.log(`3. 应用应该能正常显示预警`);
}
