console.log('🔍 重新验证申丽丽的退休计算...\n');

// 申丽丽的信息
const shenLiLi = {
  姓名: '申丽丽',
  出生日期: '1969/03/15',  // 注意：这里是1969年，不是1971年
  性别: '女'
};

console.log(`📋 申丽丽基本信息：`);
console.log(`   姓名: ${shenLiLi.姓名}`);
console.log(`   出生日期: ${shenLiLi.出生日期}`);
console.log(`   性别: ${shenLiLi.性别}`);

// 解析出生日期
function parseBirthDate(dateStr) {
  const cleanStr = dateStr.replace(/[年月日\-\.]/g, '/');
  return new Date(cleanStr);
}

const birthDate = parseBirthDate(shenLiLi.出生日期);
const birthYear = birthDate.getFullYear();

console.log(`\n📅 出生年份分析：`);
console.log(`   出生年份: ${birthYear}年`);
console.log(`   出生月日: ${birthDate.getMonth() + 1}月${birthDate.getDate()}日`);

// 计算当前年龄
function calculateAge(birthDate) {
  const today = new Date();
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  
  return age;
}

const currentAge = calculateAge(birthDate);
console.log(`\n👤 年龄计算：`);
console.log(`   当前年龄: ${currentAge}岁`);
console.log(`   法定退休年龄: 55岁（女性）`);
console.log(`   距离法定退休: ${55 - currentAge}年`);

// 计算延迟退休
function calculateDelayRetirement(birthYear) {
  let totalDelayMonths = 3; // 基础延迟3个月
  
  if (birthYear >= 1966 && birthYear <= 1970) {
    const extraYears = birthYear - 1965;
    const extraMonths = Math.min(extraYears * 2, 12); // 每年2个月，最多1年
    totalDelayMonths += extraMonths;
  }
  
  return {
    totalDelayMonths,
    delayYears: Math.floor(totalDelayMonths / 12),
    delayMonths: totalDelayMonths % 12
  };
}

const delayInfo = calculateDelayRetirement(birthYear);
console.log(`\n⏰ 延迟退休计算：`);
console.log(`   出生年份: ${birthYear}年`);
console.log(`   适用规则: 1966-1970年（基础3个月 + 每年2个月）`);
console.log(`   计算过程: 基础3个月 + (${birthYear} - 1965) × 2个月 = 3 + ${birthYear - 1965} × 2 = 3 + ${(birthYear - 1965) * 2} = ${delayInfo.totalDelayMonths}个月`);
console.log(`   延迟月数: ${delayInfo.totalDelayMonths}个月`);
console.log(`   延迟格式: ${delayInfo.delayYears > 0 ? `${delayInfo.delayYears}年` : ''}${delayInfo.delayMonths}个月`);

// 计算实际退休年龄和日期
const actualRetirementAge = 55 + (delayInfo.totalDelayMonths / 12);
console.log(`\n🎯 实际退休计算：`);
console.log(`   法定退休年龄: 55岁`);
console.log(`   延迟月数: ${delayInfo.totalDelayMonths}个月`);
console.log(`   实际退休年龄: 55 + ${delayInfo.totalDelayMonths}/12 = ${actualRetirementAge.toFixed(2)}岁`);

// 计算实际退休日期
const legalRetirementDate = new Date(birthDate);
legalRetirementDate.setFullYear(birthDate.getFullYear() + 55);

const actualRetirementDate = new Date(legalRetirementDate);
actualRetirementDate.setMonth(actualRetirementDate.getMonth() + delayInfo.totalDelayMonths);

console.log(`\n📅 退休日期计算：`);
console.log(`   法定退休日期: ${legalRetirementDate.getFullYear()}年${legalRetirementDate.getMonth() + 1}月${legalRetirementDate.getDate()}日`);
console.log(`   实际退休日期: ${actualRetirementDate.getFullYear()}年${actualRetirementDate.getMonth() + 1}月${actualRetirementDate.getDate()}日`);

// 计算距离退休的天数
const today = new Date();
const daysToLegal = Math.ceil((legalRetirementDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
const daysToActual = Math.ceil((actualRetirementDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

console.log(`\n⏳ 倒计时计算：`);
console.log(`   距离法定退休: ${daysToLegal}天`);
console.log(`   距离实际退休: ${daysToActual}天`);

// 判断退休状态
console.log(`\n📊 退休状态判断：`);
if (currentAge < 55) {
  console.log(`   ✅ 未到法定退休年龄（${currentAge}岁 < 55岁）`);
  if (daysToActual > 0) {
    console.log(`   ✅ 未到实际退休日期（还剩${daysToActual}天）`);
    console.log(`   📋 应归类为: 近两年退休预警`);
  } else {
    console.log(`   ❌ 已超过实际退休日期（超过${Math.abs(daysToActual)}天）`);
    console.log(`   📋 应归类为: 已退休`);
  }
} else {
  console.log(`   ⚠️ 已达到法定退休年龄（${currentAge}岁 >= 55岁）`);
  if (daysToActual > 0) {
    console.log(`   ✅ 但未到实际退休日期（还剩${daysToActual}天）`);
    console.log(`   📋 应归类为: 已超过法定退休年龄，但未到实际退休`);
  } else {
    console.log(`   ❌ 已超过实际退休日期（超过${Math.abs(daysToActual)}天）`);
    console.log(`   📋 应归类为: 已退休`);
  }
}

console.log(`\n🎯 正确的预期结果：`);
console.log(`   申丽丽（1969年3月15日）：`);
console.log(`   - 当前年龄: ${currentAge}岁`);
console.log(`   - 延迟月数: ${delayInfo.totalDelayMonths}个月`);
console.log(`   - 距离法定退休: ${daysToLegal}天`);
console.log(`   - 距离实际退休: ${daysToActual}天`);
if (currentAge < 55) {
  console.log(`   - 状态: 未到法定退休年龄，在近两年退休预警列表`);
} else {
  console.log(`   - 状态: 已超过法定退休年龄，但${daysToActual > 0 ? '未到实际退休日期' : '已超过实际退休日期'}`);
}

console.log('\n🔍 申丽丽退休计算验证完成！');
