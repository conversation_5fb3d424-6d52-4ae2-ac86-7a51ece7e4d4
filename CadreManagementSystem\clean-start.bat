@echo off
echo ========================================
echo 清理并启动干净的EXPO开发环境
echo ========================================
echo.

REM 设置Node.js路径
set PATH=C:\Program Files\nodejs;%PATH%

REM 切换到项目目录
cd /d "%~dp0"

echo 1. 清理数据库文件...
if exist cadre_management.db (
    del /f cadre_management.db
    echo    ✓ 数据库文件已删除
) else (
    echo    ✓ 数据库文件不存在
)

echo.
echo 2. 清理EXPO缓存...
if exist .expo (
    rmdir /s /q .expo
    echo    ✓ .expo 缓存已清理
) else (
    echo    ✓ .expo 缓存不存在
)

if exist node_modules\.cache (
    rmdir /s /q node_modules\.cache
    echo    ✓ node_modules缓存已清理
) else (
    echo    ✓ node_modules缓存不存在
)

echo.
echo 3. 启动EXPO开发服务器...
echo    请等待服务器启动完成...
echo.

npx expo start --clear

pause
