/**
 * Excel数据分析脚本 - 深度分析0606.xlsx文件
 */

const fs = require('fs');
const path = require('path');
const xlsx = require('xlsx');

console.log('📊 开始分析Excel文件数据结构...');
console.log('');

const excelPath = path.join(__dirname, '0606.xlsx');

try {
  const workbook = xlsx.readFile(excelPath);
  const sheetName = workbook.SheetNames[0];
  const worksheet = workbook.Sheets[sheetName];
  
  // 获取数据范围
  const range = xlsx.utils.decode_range(worksheet['!ref']);
  console.log(`📋 工作表: ${sheetName}`);
  console.log(`📏 数据范围: ${worksheet['!ref']} (${range.e.r + 1} 行 x ${range.e.c + 1} 列)`);
  console.log('');
  
  // 获取表头
  const headers = [];
  for (let col = range.s.c; col <= range.e.c; col++) {
    const cellAddress = xlsx.utils.encode_cell({ r: 0, c: col });
    const cell = worksheet[cellAddress];
    if (cell && cell.v) {
      headers.push(cell.v);
    }
  }
  
  console.log('📝 表头字段 (' + headers.length + ' 个):');
  headers.forEach((header, index) => {
    console.log(`   ${index + 1}. ${header}`);
  });
  console.log('');
  
  // 转换为JSON数据
  const data = xlsx.utils.sheet_to_json(worksheet);
  console.log(`📊 数据行数: ${data.length}`);
  console.log('');
  
  // 分析数据质量
  console.log('🔍 数据质量分析:');
  
  // 检查必需字段
  const requiredFields = ['姓名', '性别', '出生日期', '职务'];
  requiredFields.forEach(field => {
    const emptyCount = data.filter(row => !row[field] || row[field].toString().trim() === '').length;
    const fillRate = ((data.length - emptyCount) / data.length * 100).toFixed(1);
    console.log(`   ${field}: ${fillRate}% 完整度 (${data.length - emptyCount}/${data.length})`);
  });
  console.log('');
  
  // 性别分布
  const genderStats = {};
  data.forEach(row => {
    const gender = row['性别'] || '未知';
    genderStats[gender] = (genderStats[gender] || 0) + 1;
  });
  
  console.log('👥 性别分布:');
  Object.entries(genderStats).forEach(([gender, count]) => {
    const percentage = (count / data.length * 100).toFixed(1);
    console.log(`   ${gender}: ${count} 人 (${percentage}%)`);
  });
  console.log('');
  
  // 出生年份分布
  const birthYearStats = {};
  data.forEach(row => {
    const birthDate = row['出生日期'];
    if (birthDate) {
      let year;
      if (typeof birthDate === 'number') {
        // Excel日期序列号
        const date = new Date((birthDate - 25569) * 86400 * 1000);
        year = date.getFullYear();
      } else {
        // 字符串日期
        const dateStr = birthDate.toString();
        const match = dateStr.match(/(\d{4})/);
        year = match ? parseInt(match[1]) : null;
      }
      
      if (year && year > 1900 && year < 2010) {
        const decade = Math.floor(year / 10) * 10;
        const key = `${decade}年代`;
        birthYearStats[key] = (birthYearStats[key] || 0) + 1;
      }
    }
  });
  
  console.log('📅 出生年代分布:');
  Object.entries(birthYearStats)
    .sort(([a], [b]) => a.localeCompare(b))
    .forEach(([decade, count]) => {
      const percentage = (count / data.length * 100).toFixed(1);
      console.log(`   ${decade}: ${count} 人 (${percentage}%)`);
    });
  console.log('');
  
  // 职务分析
  const positionStats = {};
  data.forEach(row => {
    const position = row['职务'] || '未知';
    positionStats[position] = (positionStats[position] || 0) + 1;
  });
  
  console.log('💼 职务分布 (前10位):');
  Object.entries(positionStats)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 10)
    .forEach(([position, count]) => {
      const percentage = (count / data.length * 100).toFixed(1);
      console.log(`   ${position}: ${count} 人 (${percentage}%)`);
    });
  console.log('');
  
  // 女性干部详细分析
  const femaleData = data.filter(row => row['性别'] === '女');
  console.log(`👩 女性干部详细分析 (${femaleData.length} 人):`);
  
  // 女性出生年份分布
  const femaleBirthYears = {};
  femaleData.forEach(row => {
    const birthDate = row['出生日期'];
    if (birthDate) {
      let year;
      if (typeof birthDate === 'number') {
        const date = new Date((birthDate - 25569) * 86400 * 1000);
        year = date.getFullYear();
      } else {
        const dateStr = birthDate.toString();
        const match = dateStr.match(/(\d{4})/);
        year = match ? parseInt(match[1]) : null;
      }
      
      if (year && year > 1900 && year < 2010) {
        femaleBirthYears[year] = (femaleBirthYears[year] || 0) + 1;
      }
    }
  });
  
  console.log('   出生年份分布:');
  Object.entries(femaleBirthYears)
    .sort(([a], [b]) => parseInt(a) - parseInt(b))
    .forEach(([year, count]) => {
      console.log(`     ${year}年: ${count} 人`);
    });
  console.log('');
  
  // 预测退休预警情况
  console.log('⚠️ 退休预警预测 (基于修复后的55岁女性退休年龄):');
  
  const currentYear = new Date().getFullYear();
  const warningStats = {
    nearRetirement: 0,
    nearSecondLine: 0,
    alreadySecondLine: 0,
    alreadyRetired: 0
  };
  
  data.forEach(row => {
    const birthDate = row['出生日期'];
    const gender = row['性别'];
    const position = row['职务'] || '';
    
    if (birthDate && gender) {
      let birthYear;
      if (typeof birthDate === 'number') {
        const date = new Date((birthDate - 25569) * 86400 * 1000);
        birthYear = date.getFullYear();
      } else {
        const dateStr = birthDate.toString();
        const match = dateStr.match(/(\d{4})/);
        birthYear = match ? parseInt(match[1]) : null;
      }
      
      if (birthYear) {
        const age = currentYear - birthYear;
        const baseRetirementAge = gender === '男' ? 60 : 55; // 修复后的逻辑
        
        // 简化的延迟退休计算
        let delayMonths = 0;
        if (birthYear >= 1965) {
          delayMonths = 3;
          if (birthYear > 1965) {
            delayMonths += (birthYear - 1965) * 2;
          }
        }
        
        const actualRetirementAge = baseRetirementAge + (delayMonths / 12);
        const yearsToRetirement = actualRetirementAge - age;
        
        // 判断是否为中层管理
        const isMiddleManagement = position.includes('中层') || 
                                  position.includes('正职') || 
                                  position.includes('副职') ||
                                  position.includes('主任') ||
                                  position.includes('副主任');
        
        if (yearsToRetirement <= 0) {
          warningStats.alreadyRetired++;
        } else if (yearsToRetirement <= 2) {
          if (isMiddleManagement && gender === '男' && age >= 58) {
            warningStats.alreadySecondLine++;
          } else if (isMiddleManagement && gender === '女' && age >= 54) {
            warningStats.alreadySecondLine++;
          } else {
            warningStats.nearRetirement++;
          }
        } else if (isMiddleManagement) {
          if (gender === '男' && age >= 56 && age < 58) {
            warningStats.nearSecondLine++;
          } else if (gender === '女' && age >= 52 && age < 54) {
            warningStats.nearSecondLine++;
          }
        }
      }
    }
  });
  
  console.log(`   近两年退休: ${warningStats.nearRetirement} 人`);
  console.log(`   近两年退居二线: ${warningStats.nearSecondLine} 人`);
  console.log(`   已退居二线: ${warningStats.alreadySecondLine} 人`);
  console.log(`   已退休: ${warningStats.alreadyRetired} 人`);
  console.log('');
  
  console.log('✅ Excel数据分析完成！');
  console.log('');
  console.log('📋 导入建议:');
  console.log('1. 数据质量良好，可以直接导入');
  console.log('2. 注意处理Excel日期序列号格式');
  console.log('3. 女性干部将正确按55岁退休计算');
  console.log('4. 预计产生约' + (warningStats.nearRetirement + warningStats.nearSecondLine + warningStats.alreadySecondLine + warningStats.alreadyRetired) + '个退休预警');
  
} catch (error) {
  console.error('❌ Excel文件分析失败:', error.message);
}
