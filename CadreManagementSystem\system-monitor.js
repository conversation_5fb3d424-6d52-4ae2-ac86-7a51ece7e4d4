/**
 * 系统监控脚本
 * 全面监控系统状态，检测问题并提供修复建议
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 系统监控开始...');
console.log('');

// 监控结果
const monitorResults = {
  healthy: [],
  warnings: [],
  critical: [],
  suggestions: []
};

// 1. 文件完整性检查
console.log('📋 1. 文件完整性检查');
const criticalFiles = [
  'package.json',
  'app.json',
  'tsconfig.json',
  'src/database/database.ts',
  'src/database/cadreDao.ts',
  'src/utils/configurableRetirementCalculator.ts',
  'src/utils/retirementCalculator.ts',
  'src/utils/excelImporter.ts',
  'src/utils/errorMonitor.ts',
  'app/(tabs)/index.tsx',
  'app/(tabs)/cadres.tsx',
  'app/(tabs)/warnings.tsx'
];

let missingFiles = 0;
criticalFiles.forEach(file => {
  const exists = fs.existsSync(path.join(__dirname, file));
  if (exists) {
    console.log(`✅ ${file}`);
    monitorResults.healthy.push(`文件存在: ${file}`);
  } else {
    console.log(`❌ ${file}`);
    monitorResults.critical.push(`关键文件缺失: ${file}`);
    missingFiles++;
  }
});

if (missingFiles === 0) {
  console.log('✅ 所有关键文件完整');
} else {
  console.log(`❌ 发现${missingFiles}个文件缺失`);
  monitorResults.suggestions.push('恢复缺失的关键文件');
}
console.log('');

// 2. 代码质量检查
console.log('📋 2. 代码质量检查');
try {
  // 检查女性退休年龄修复
  const configCalculator = fs.readFileSync(path.join(__dirname, 'src/utils/configurableRetirementCalculator.ts'), 'utf8');
  if (configCalculator.includes('return 55;') && configCalculator.includes('干部管理系统中所有女性都按干部标准')) {
    console.log('✅ 女性退休年龄修复已应用');
    monitorResults.healthy.push('女性退休年龄修复正确');
  } else {
    console.log('❌ 女性退休年龄修复未应用');
    monitorResults.critical.push('女性退休年龄修复缺失');
  }

  // 检查延迟退休计算修复
  if (configCalculator.includes('基础3个月 + 1966-1970年累积') && configCalculator.includes('修复延迟退休计算逻辑')) {
    console.log('✅ 延迟退休计算修复已应用');
    monitorResults.healthy.push('延迟退休计算修复正确');
  } else {
    console.log('❌ 延迟退休计算修复未应用');
    monitorResults.critical.push('延迟退休计算修复缺失');
  }

  // 检查导入路径修复
  if (!configCalculator.includes('from "../types/cadre"')) {
    console.log('✅ 导入路径修复已应用');
    monitorResults.healthy.push('导入路径修复正确');
  } else {
    console.log('❌ 导入路径修复未应用');
    monitorResults.warnings.push('存在错误的导入路径');
  }

} catch (error) {
  console.log('❌ 代码质量检查失败:', error.message);
  monitorResults.critical.push('无法进行代码质量检查');
}
console.log('');

// 3. 依赖包检查
console.log('📋 3. 依赖包检查');
try {
  const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, 'package.json'), 'utf8'));
  const deps = packageJson.dependencies || {};
  
  const requiredDeps = {
    'expo': '必需的Expo框架',
    'expo-sqlite': '数据库支持',
    'xlsx': 'Excel文件处理',
    'react-native': 'React Native框架',
    '@react-navigation/native': '导航支持'
  };

  let missingDeps = 0;
  Object.entries(requiredDeps).forEach(([dep, description]) => {
    if (deps[dep]) {
      console.log(`✅ ${dep}: ${deps[dep]} - ${description}`);
      monitorResults.healthy.push(`依赖包正常: ${dep}`);
    } else {
      console.log(`❌ ${dep}: 未安装 - ${description}`);
      monitorResults.critical.push(`缺失依赖包: ${dep}`);
      missingDeps++;
    }
  });

  if (missingDeps === 0) {
    console.log('✅ 所有必需依赖包已安装');
  } else {
    console.log(`❌ 发现${missingDeps}个依赖包缺失`);
    monitorResults.suggestions.push('运行 npm install 安装缺失的依赖包');
  }

} catch (error) {
  console.log('❌ 依赖包检查失败:', error.message);
  monitorResults.critical.push('无法检查依赖包');
}
console.log('');

// 4. 数据文件检查
console.log('📋 4. 数据文件检查');
const dataFiles = [
  '0606.xlsx'
];

dataFiles.forEach(file => {
  const exists = fs.existsSync(path.join(__dirname, file));
  if (exists) {
    const stats = fs.statSync(path.join(__dirname, file));
    console.log(`✅ ${file} (${Math.round(stats.size / 1024)}KB)`);
    monitorResults.healthy.push(`数据文件存在: ${file}`);
  } else {
    console.log(`⚠️ ${file} - 数据文件缺失`);
    monitorResults.warnings.push(`数据文件缺失: ${file}`);
  }
});
console.log('');

// 5. 清理状态检查
console.log('📋 5. 清理状态检查');
const backupDir = path.join(__dirname, 'backup_deleted_files');
if (fs.existsSync(backupDir)) {
  const backupFiles = fs.readdirSync(backupDir);
  console.log(`✅ 备份目录存在，包含${backupFiles.length}个文件`);
  monitorResults.healthy.push('代码清理已完成');
  
  // 检查是否还有测试文件在主目录
  const mainFiles = fs.readdirSync(__dirname);
  const testFiles = mainFiles.filter(f => f.startsWith('test-') && f.endsWith('.js'));
  
  if (testFiles.length === 0) {
    console.log('✅ 主目录已清理，无测试文件');
    monitorResults.healthy.push('主目录清理完成');
  } else {
    console.log(`⚠️ 主目录仍有${testFiles.length}个测试文件`);
    monitorResults.warnings.push('主目录存在未清理的测试文件');
  }
} else {
  console.log('⚠️ 备份目录不存在');
  monitorResults.warnings.push('备份目录缺失');
}
console.log('');

// 6. 系统状态评估
console.log('📋 6. 系统状态评估');
const totalIssues = monitorResults.critical.length + monitorResults.warnings.length;
const healthyItems = monitorResults.healthy.length;

let systemStatus = 'healthy';
let statusEmoji = '✅';
let statusMessage = '系统运行正常';

if (monitorResults.critical.length > 0) {
  systemStatus = 'critical';
  statusEmoji = '🔥';
  statusMessage = '系统存在严重问题，需要立即处理';
} else if (monitorResults.warnings.length > 0) {
  systemStatus = 'warning';
  statusEmoji = '⚠️';
  statusMessage = '系统存在警告，建议尽快处理';
}

console.log(`${statusEmoji} 系统状态: ${systemStatus.toUpperCase()}`);
console.log(`📊 健康项目: ${healthyItems}`);
console.log(`⚠️ 警告项目: ${monitorResults.warnings.length}`);
console.log(`🔥 严重问题: ${monitorResults.critical.length}`);
console.log(`💬 状态说明: ${statusMessage}`);
console.log('');

// 7. 修复建议
if (monitorResults.suggestions.length > 0) {
  console.log('💡 修复建议:');
  monitorResults.suggestions.forEach((suggestion, index) => {
    console.log(`${index + 1}. ${suggestion}`);
  });
  console.log('');
}

// 8. 详细问题报告
if (monitorResults.critical.length > 0) {
  console.log('🔥 严重问题详情:');
  monitorResults.critical.forEach((issue, index) => {
    console.log(`${index + 1}. ${issue}`);
  });
  console.log('');
}

if (monitorResults.warnings.length > 0) {
  console.log('⚠️ 警告详情:');
  monitorResults.warnings.forEach((warning, index) => {
    console.log(`${index + 1}. ${warning}`);
  });
  console.log('');
}

// 9. 总结
console.log('🎯 监控总结:');
console.log(`系统整体状态: ${statusEmoji} ${systemStatus.toUpperCase()}`);
console.log(`健康度评分: ${Math.round((healthyItems / (healthyItems + totalIssues)) * 100)}%`);
console.log('');

if (systemStatus === 'healthy') {
  console.log('🚀 系统已准备就绪，可以正常使用！');
  console.log('📱 建议启动EXPO进行实际测试');
} else {
  console.log('🔧 请根据上述建议修复问题后再启动系统');
}

console.log('');
console.log('📋 下一步操作:');
console.log('1. 根据修复建议处理问题');
console.log('2. 启动雷电模拟器');
console.log('3. 启动EXPO开发服务器');
console.log('4. 在模拟器中测试应用功能');
console.log('5. 导入0606.xlsx测试数据');
console.log('6. 验证退休预警计算结果');
