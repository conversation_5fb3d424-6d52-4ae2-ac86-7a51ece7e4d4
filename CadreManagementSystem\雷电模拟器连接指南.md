# 雷电模拟器连接EXPO应用指南

## 当前状态
✅ EXPO服务器已成功启动
✅ 服务器运行在端口8081
✅ 雷电模拟器已安装Expo Go

## 连接步骤

### 1. 确保EXPO服务器正在运行
当前服务器已启动，显示信息：
```
› Metro waiting on exp://*************:8081
› Web is waiting on http://localhost:8081
```

### 2. 在雷电模拟器中连接

#### 方法一：使用模拟器专用地址
1. 打开雷电模拟器中的Expo Go应用
2. 点击"Enter URL manually"
3. 输入地址：`exp://********:8081`
4. 点击连接

#### 方法二：使用局域网地址
1. 打开雷电模拟器中的Expo Go应用
2. 点击"Enter URL manually"
3. 输入地址：`exp://*************:8081`
4. 点击连接

### 3. 如果连接失败

#### 检查网络设置
1. 确保雷电模拟器网络模式设置正确
2. 在模拟器设置中检查网络配置

#### 重启服务器
如果连接问题持续，可以：
1. 在EXPO终端按 `Ctrl+C` 停止服务器
2. 运行 `start-expo-lan.bat` 重新启动

#### 使用离线模式
当前服务器使用离线模式启动，避免网络连接问题：
```bash
npx expo start --offline --port 8081
```

## 故障排除

### 常见问题
1. **连接超时**：检查防火墙设置
2. **地址无法访问**：尝试不同的地址格式
3. **应用无法加载**：检查项目依赖是否完整

### 调试命令
```bash
# 检查端口占用
netstat -an | findstr ":8081"

# 重新安装依赖
npm install

# 清除缓存重启
npx expo start --clear
```

## 成功连接后
- 应用将在模拟器中加载
- 可以进行实时开发和调试
- 修改代码后应用会自动刷新
