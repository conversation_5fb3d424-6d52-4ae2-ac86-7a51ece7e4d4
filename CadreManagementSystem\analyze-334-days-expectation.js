console.log('🔍 分析334天期望值的来源...\n');

// 从今天开始计算334天后的日期
const today = new Date();
const expectedRetirementDate = new Date(today);
expectedRetirementDate.setDate(today.getDate() + 334);

console.log(`今天日期: ${today.toLocaleDateString()}`);
console.log(`334天后日期: ${expectedRetirementDate.toLocaleDateString()}`);

// 申丽丽的出生日期
const birthDate = new Date('1971-01-01');
console.log(`申丽丽出生日期: ${birthDate.toLocaleDateString()}`);

// 计算334天后申丽丽的年龄
const ageAtExpectedRetirement = expectedRetirementDate.getFullYear() - birthDate.getFullYear();
const monthDiff = expectedRetirementDate.getMonth() - birthDate.getMonth();
const dayDiff = expectedRetirementDate.getDate() - birthDate.getDate();

let exactAgeAtRetirement = ageAtExpectedRetirement;
if (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)) {
  exactAgeAtRetirement--;
}

console.log(`334天后申丽丽的年龄: ${exactAgeAtRetirement}岁`);

// 分析可能的退休年龄
console.log('\n🎯 可能的退休年龄分析：');

// 情况1：55岁无延迟
const retirement55 = new Date(birthDate);
retirement55.setFullYear(birthDate.getFullYear() + 55);
const days55 = Math.ceil((retirement55.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
console.log(`55岁无延迟退休 (${retirement55.toLocaleDateString()}): ${days55}天`);

// 情况2：55岁延迟3个月
const retirement55Plus3 = new Date(retirement55);
retirement55Plus3.setMonth(retirement55Plus3.getMonth() + 3);
const days55Plus3 = Math.ceil((retirement55Plus3.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
console.log(`55岁延迟3个月 (${retirement55Plus3.toLocaleDateString()}): ${days55Plus3}天`);

// 情况3：55岁延迟21个月（1971年的正确延迟）
const retirement55Plus21 = new Date(retirement55);
retirement55Plus21.setMonth(retirement55Plus21.getMonth() + 21);
const days55Plus21 = Math.ceil((retirement55Plus21.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
console.log(`55岁延迟21个月 (${retirement55Plus21.toLocaleDateString()}): ${days55Plus21}天`);

// 情况4：50岁退休（工人身份）
const retirement50 = new Date(birthDate);
retirement50.setFullYear(birthDate.getFullYear() + 50);
const days50 = Math.ceil((retirement50.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
console.log(`50岁退休（工人身份，${retirement50.toLocaleDateString()}): ${days50}天`);

// 情况5：50岁延迟退休
const retirement50Plus21 = new Date(retirement50);
retirement50Plus21.setMonth(retirement50Plus21.getMonth() + 21);
const days50Plus21 = Math.ceil((retirement50Plus21.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
console.log(`50岁延迟21个月 (${retirement50Plus21.toLocaleDateString()}): ${days50Plus21}天`);

console.log('\n📊 与334天期望值的对比：');
const scenarios = [
  { name: '55岁无延迟', days: days55 },
  { name: '55岁延迟3个月', days: days55Plus3 },
  { name: '55岁延迟21个月', days: days55Plus21 },
  { name: '50岁无延迟', days: days50 },
  { name: '50岁延迟21个月', days: days50Plus21 }
];

scenarios.forEach(scenario => {
  const diff = Math.abs(scenario.days - 334);
  const match = diff < 30 ? '✅ 接近' : '❌ 差距大';
  console.log(`${scenario.name}: ${scenario.days}天 (差距${diff}天) ${match}`);
});

console.log('\n🔍 可能的原因分析：');

// 找到最接近334天的情况
const closest = scenarios.reduce((prev, curr) => 
  Math.abs(curr.days - 334) < Math.abs(prev.days - 334) ? curr : prev
);

console.log(`最接近334天的情况: ${closest.name} (${closest.days}天)`);

if (Math.abs(closest.days - 334) < 30) {
  console.log('✅ 找到了接近的情况，可能的原因：');
  if (closest.name.includes('50岁')) {
    console.log('- 申丽丽可能是工人身份，应该50岁退休而不是55岁');
    console.log('- 需要检查申丽丽的职级或身份类别');
  } else if (closest.name.includes('无延迟')) {
    console.log('- 可能延迟退休政策理解有误');
    console.log('- 或者申丽丽有特殊情况不适用延迟退休');
  }
} else {
  console.log('❌ 没有找到接近334天的标准情况，可能的原因：');
  console.log('- 计算起始日期不同');
  console.log('- 有其他特殊的退休政策');
  console.log('- 申丽丽有特殊的个人情况');
}

console.log('\n🎯 建议检查：');
console.log('1. 确认申丽丽的职级类别（干部/工人）');
console.log('2. 检查申丽丽是否有特殊的退休状态标记');
console.log('3. 确认延迟退休政策的具体实施细节');
console.log('4. 检查计算的起始日期是否正确');

console.log('\n✅ 334天期望值分析完成！');
