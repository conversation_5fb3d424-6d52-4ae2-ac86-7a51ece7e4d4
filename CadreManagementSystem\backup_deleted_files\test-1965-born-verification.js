console.log('🔍 验证1965年出生人员的退休计算...\n');

// 统一的延迟退休计算函数
function calculateDelayRetirement(birthYear) {
  let delayMonths = 0;
  
  if (birthYear >= 1965) {
    // 基础延迟3个月
    delayMonths = 3;
    
    if (birthYear >= 1966) {
      // 1966年及以后的额外延迟
      const extraYears = birthYear - 1965;
      
      if (birthYear <= 1970) {
        // 1966-1970: 每年额外2个月，最多1年
        delayMonths += Math.min(extraYears * 2, 12);
      } else if (birthYear <= 1980) {
        // 1971-1980: 前5年每年2个月，后续每年3个月，最多3年
        const firstPhase = Math.min(5, extraYears) * 2;
        const secondPhase = Math.max(0, extraYears - 5) * 3;
        delayMonths += Math.min(firstPhase + secondPhase, 36);
      } else {
        // 1981+: 前5年每年2个月，6-10年每年3个月，后续每年4个月，最多5年
        const firstPhase = Math.min(5, extraYears) * 2;
        const secondPhase = Math.min(5, Math.max(0, extraYears - 5)) * 3;
        const thirdPhase = Math.max(0, extraYears - 10) * 4;
        delayMonths += Math.min(firstPhase + secondPhase + thirdPhase, 60);
      }
    }
  }
  
  return {
    delayYears: Math.floor(delayMonths / 12),
    delayMonths: delayMonths % 12,
    totalDelayMonths: delayMonths
  };
}

// 计算距离退休的天数
function calculateDaysUntilRetirement(birthDate, actualRetirementAge) {
  const retirementDate = new Date(birthDate);
  retirementDate.setFullYear(birthDate.getFullYear() + Math.floor(actualRetirementAge));
  
  // 处理延迟的月数
  const delayMonths = Math.round((actualRetirementAge - Math.floor(actualRetirementAge)) * 12);
  retirementDate.setMonth(retirementDate.getMonth() + delayMonths);

  const today = new Date();
  const diffTime = retirementDate.getTime() - today.getTime();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

// 计算当前年龄
function calculateAge(birthDate) {
  const today = new Date();
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  
  return age;
}

// 从图片中看到的1965年出生人员
const people1965 = [
  {
    name: '郭晋瑞',
    birthDate: new Date(1965, 1, 17), // 1965年2月17日
    currentDisplay: '已超过退休年龄 46天'
  },
  {
    name: '宋晨君',
    birthDate: new Date(1965, 4, 1), // 1965年5月1日
    currentDisplay: '已超过退休年龄 0天'
  },
  {
    name: '王四顿',
    birthDate: new Date(1965, 3, 21), // 1965年4月21日
    currentDisplay: '已超过退休年龄 0天'
  },
  {
    name: '吴五雷',
    birthDate: new Date(1965, 5, 4), // 1965年6月4日
    currentDisplay: '已超过退休年龄 0天'
  }
];

console.log('═══════════════════════════════════════════════════════════════');
console.log('🧮 1965年出生人员退休计算验证');
console.log('═══════════════════════════════════════════════════════════════\n');

console.log('🎯 1965年延迟退休政策：');
console.log('- 基础延迟：3个月');
console.log('- 额外延迟：0个月（1965年不额外延迟）');
console.log('- 总延迟：3个月');
console.log('- 实际退休年龄：60.25岁\n');

people1965.forEach((person, index) => {
  console.log(`${index + 1}. ${person.name}（${person.birthDate.toLocaleDateString()}）：`);
  
  const currentAge = calculateAge(person.birthDate);
  const baseRetirementAge = 60; // 男性
  const delayInfo = calculateDelayRetirement(1965);
  const actualRetirementAge = baseRetirementAge + (delayInfo.totalDelayMonths / 12);
  
  // 计算退休日期
  const retirementDate = new Date(person.birthDate);
  retirementDate.setFullYear(person.birthDate.getFullYear() + Math.floor(actualRetirementAge));
  const delayMonths = Math.round((actualRetirementAge - Math.floor(actualRetirementAge)) * 12);
  retirementDate.setMonth(retirementDate.getMonth() + delayMonths);
  
  const daysUntilRetirement = calculateDaysUntilRetirement(person.birthDate, actualRetirementAge);
  
  console.log(`   📊 基础信息：`);
  console.log(`      当前年龄：${currentAge}岁`);
  console.log(`      实际退休年龄：${actualRetirementAge.toFixed(2)}岁`);
  console.log(`      退休日期：${retirementDate.toLocaleDateString()}`);
  
  console.log(`   ⏰ 倒计时计算：`);
  console.log(`      距离实际退休：${daysUntilRetirement}天`);
  
  console.log(`   📱 当前显示：${person.currentDisplay}`);
  
  console.log(`   🎯 正确状态：`);
  if (daysUntilRetirement <= 0) {
    console.log(`      ✅ 已退休（超过实际退休日期${Math.abs(daysUntilRetirement)}天）`);
    console.log(`      ✅ 应该出现在"已退休"列表中`);
  } else {
    console.log(`      ❌ 未退休（还有${daysUntilRetirement}天到达实际退休日期）`);
    console.log(`      ❌ 不应该出现在"已退休"列表中`);
    console.log(`      ✅ 应该出现在"近两年退休"列表中`);
  }
  
  console.log('');
});

console.log('═══════════════════════════════════════════════════════════════');
console.log('🔧 修复总结');
console.log('═══════════════════════════════════════════════════════════════\n');

console.log('✅ 已修复的问题：');
console.log('1. matchRetiredRule方法现在考虑延迟退休');
console.log('2. 只有当daysUntilRetirement <= 0时才判断为已退休');
console.log('3. 退休状态判断基于实际退休日期，而非法定退休年龄\n');

console.log('🚀 预期修复效果：');
console.log('1. 郭晋瑞：已超过实际退休日期，保留在"已退休"列表');
console.log('2. 宋晨君：还未到实际退休日期，移至"近两年退休"列表');
console.log('3. 王四顿：还未到实际退休日期，移至"近两年退休"列表');
console.log('4. 吴五雷：还未到实际退休日期，移至"近两年退休"列表\n');

console.log('📋 验证步骤：');
console.log('1. 重新启动应用');
console.log('2. 进入退休预警页面');
console.log('3. 查看"已退休"选项卡：应该只有真正超过实际退休日期的人员');
console.log('4. 查看"近两年退休"选项卡：应该包含还未到实际退休日期的1965年出生人员');

console.log('\n🎉 1965年出生人员退休计算验证完成！');
