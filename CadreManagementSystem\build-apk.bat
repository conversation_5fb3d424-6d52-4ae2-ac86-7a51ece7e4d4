@echo off
chcp 65001 >nul
echo 🔨 构建APK文件
echo ================================
echo.

echo 📋 这将构建一个可以直接安装在雷电模拟器上的APK文件
echo 📋 构建完成后，您可以直接安装测试，无需EXPO连接
echo.

echo 📋 步骤1: 预构建Android项目
call npx expo prebuild --platform android

echo.
echo 📋 步骤2: 构建APK
call npx expo run:android --variant release

echo.
echo ✅ APK构建完成！
echo 📱 APK文件位置: android\app\build\outputs\apk\release\
echo.
echo 📋 安装步骤:
echo 1. 将APK文件拖拽到雷电模拟器中
echo 2. 或者使用adb install命令安装
echo 3. 直接运行应用测试功能
echo.

pause
