# Set working directory
Set-Location "F:\APPapk\CadreManagementSystem"

# Set Node.js path
$env:PATH = "C:\Program Files\nodejs;" + $env:PATH

# Display startup info
Write-Host "========================================" -ForegroundColor Green
Write-Host "Starting EXPO Development Server" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# Check if Node.js is available
try {
    $nodeVersion = & node --version
    Write-Host "Node.js Version: $nodeVersion" -ForegroundColor Yellow
} catch {
    Write-Host "Error: Cannot find Node.js, please ensure it is properly installed" -ForegroundColor Red
    Read-Host "Press any key to exit"
    exit 1
}

# Clean cache
Write-Host "Cleaning cache..." -ForegroundColor Yellow
if (Test-Path ".expo") {
    Remove-Item -Recurse -Force ".expo"
    Write-Host "✓ .expo cache cleaned" -ForegroundColor Green
}

if (Test-Path "node_modules\.cache") {
    Remove-Item -Recurse -Force "node_modules\.cache"
    Write-Host "✓ node_modules cache cleaned" -ForegroundColor Green
}

# Start EXPO server
Write-Host ""
Write-Host "Starting EXPO development server..." -ForegroundColor Yellow
Write-Host "Please wait for server to start..." -ForegroundColor Yellow
Write-Host ""

try {
    & npx expo start --clear
} catch {
    Write-Host "Error: Failed to start EXPO server" -ForegroundColor Red
    Write-Host "Error details: $_" -ForegroundColor Red
    Read-Host "Press any key to exit"
    exit 1
}

Read-Host "Press any key to exit"
