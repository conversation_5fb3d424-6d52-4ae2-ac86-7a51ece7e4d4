@echo off
chcp 65001 >nul
echo 🔧 修复EXPO连接问题
echo ================================
echo.

echo 📋 步骤1: 停止所有EXPO进程
taskkill /f /im node.exe 2>nul
taskkill /f /im expo.exe 2>nul
echo ✅ 已停止现有进程
echo.

echo 📋 步骤2: 清理缓存
if exist .expo (
    rmdir /s /q .expo
    echo ✅ 已清理.expo缓存
)

if exist node_modules\.cache (
    rmdir /s /q node_modules\.cache
    echo ✅ 已清理node_modules缓存
)

echo.
echo 📋 步骤3: 重新安装依赖
echo 正在重新安装依赖包...
call npm install --force
echo ✅ 依赖包安装完成
echo.

echo 📋 步骤4: 启动EXPO开发服务器
echo 正在启动EXPO服务器...
echo 请等待二维码出现，然后在雷电模拟器中扫描
echo.
echo 💡 如果仍然出现问题，请尝试：
echo    1. 重启雷电模拟器
echo    2. 确保电脑和模拟器在同一网络
echo    3. 在Expo Go中手动输入URL
echo.

call npx expo start --clear --lan

pause
