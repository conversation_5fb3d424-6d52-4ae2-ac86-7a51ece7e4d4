import React from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Dimensions
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { CadreInfo } from '../types';

const screenWidth = Dimensions.get('window').width;

interface CadreDetailModalProps {
  visible: boolean;
  cadre: CadreInfo | null;
  onClose: () => void;
}

export const CadreDetailModal: React.FC<CadreDetailModalProps> = ({
  visible,
  cadre,
  onClose
}) => {
  if (!cadre) return null;

  // 解析出生日期
  const parseBirthDate = (birthDateStr: string | number): string => {
    if (typeof birthDateStr === 'number') {
      try {
        const excelDate = new Date((birthDateStr - 25569) * 86400 * 1000);
        if (!isNaN(excelDate.getTime()) && excelDate.getFullYear() > 1900 && excelDate.getFullYear() < 2100) {
          return excelDate.toLocaleDateString('zh-CN');
        }
      } catch (e) {
        // 忽略错误
      }
    }
    
    if (typeof birthDateStr === 'string') {
      try {
        const date = new Date(birthDateStr);
        if (!isNaN(date.getTime())) {
          return date.toLocaleDateString('zh-CN');
        }
      } catch (e) {
        // 忽略错误
      }
    }
    
    return birthDateStr?.toString() || '未知';
  };

  // 计算年龄
  const calculateAge = (birthDateStr: string | number): string => {
    if (typeof birthDateStr === 'number') {
      try {
        const excelDate = new Date((birthDateStr - 25569) * 86400 * 1000);
        if (!isNaN(excelDate.getTime())) {
          const today = new Date();
          let age = today.getFullYear() - excelDate.getFullYear();
          const monthDiff = today.getMonth() - excelDate.getMonth();
          if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < excelDate.getDate())) {
            age--;
          }
          return `${age}岁`;
        }
      } catch (e) {
        // 忽略错误
      }
    }
    return '未知';
  };

  const detailItems = [
    { label: '姓名', value: cadre.姓名 },
    { label: '性别', value: cadre.性别 },
    { label: '出生日期', value: parseBirthDate(cadre.出生日期 || '') },
    { label: '年龄', value: calculateAge(cadre.出生日期 || '') },
    { label: '单位', value: cadre.单位 },
    { label: '职务', value: cadre.职务 },
    { label: '现职级', value: cadre.现职级 },
    { label: '民族', value: cadre.民族 },
    { label: '籍贯', value: cadre.籍贯 },
    { label: '政治面貌', value: cadre.政治面貌 },
    { label: '入党时间', value: cadre.入党时间 },
    { label: '参加工作时间', value: cadre.参加工作时间 },
    { label: '全日制教育', value: cadre.全日制教育 },
    { label: '毕业院校系及专业', value: cadre.毕业院校系及专业 },
    { label: '在职教育', value: cadre.在职教育 },
    { label: '毕业院校系及专业(在职)', value: cadre.毕业院校系及专业_在职 },
    { label: '专业技术职务', value: cadre.专业技术职务 },
    { label: '任现职时间', value: cadre.任现职时间 },
    { label: '任现职级时间', value: cadre.任现职级时间 },
    { label: '身份证号', value: cadre.身份证号 },
    { label: '联系方式', value: cadre.联系方式 },
    { label: '获得奖励荣誉情况', value: cadre.获得奖励荣誉情况 },
    { label: '党纪政纪处分情况', value: cadre.党纪政纪处分情况 },
    { label: '工作简历', value: cadre.工作简历 },
    { label: '备注', value: cadre.备注 }
  ];

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        {/* 头部 */}
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color="#007AFF" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>干部详情</Text>
          <View style={styles.placeholder} />
        </View>

        {/* 内容 */}
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <View style={styles.profileSection}>
            <Text style={styles.profileName}>{cadre.姓名}</Text>
            <Text style={styles.profileSubtitle}>
              {cadre.单位} · {cadre.职务}
            </Text>
          </View>

          <View style={styles.detailsContainer}>
            {detailItems.map((item, index) => (
              <View key={index} style={styles.detailItem}>
                <Text style={styles.detailLabel}>{item.label}</Text>
                <Text style={styles.detailValue}>{item.value || '未填写'}</Text>
              </View>
            ))}
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: '#FFF',
    borderBottomWidth: 0.5,
    borderBottomColor: '#E5E5EA',
  },
  closeButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000',
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  profileSection: {
    backgroundColor: '#FFF',
    padding: 20,
    alignItems: 'center',
    marginBottom: 20,
  },
  profileName: {
    fontSize: 24,
    fontWeight: '700',
    color: '#000',
    marginBottom: 8,
  },
  profileSubtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  detailsContainer: {
    backgroundColor: '#FFF',
    marginHorizontal: 15,
    borderRadius: 12,
    overflow: 'hidden',
  },
  detailItem: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 0.5,
    borderBottomColor: '#E5E5EA',
  },
  detailLabel: {
    fontSize: 16,
    color: '#000',
    fontWeight: '500',
    width: 120,
    flexShrink: 0,
  },
  detailValue: {
    fontSize: 16,
    color: '#666',
    flex: 1,
    textAlign: 'right',
  },
});
