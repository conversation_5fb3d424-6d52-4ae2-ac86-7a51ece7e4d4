@echo off
chcp 65001 >nul
echo ========================================
echo 智慧干部信息管理系统 - 自动APK构建器
echo ========================================
echo.

echo 检查构建环境...
echo.

REM 检查Android SDK
if not exist "C:\Users\<USER>\AppData\Local\Android\Sdk" (
    echo ❌ Android SDK未找到
    echo 请确保Android Studio已正确安装
    pause
    exit /b 1
)
echo ✅ Android SDK已找到

REM 检查项目文件
if not exist "android\gradlew.bat" (
    echo ❌ Gradle Wrapper未找到
    echo 请确保在项目根目录运行此脚本
    pause
    exit /b 1
)
echo ✅ Gradle Wrapper已找到

echo.
echo 选择构建方案:
echo 1. 标准构建 (需要网络连接)
echo 2. 离线构建 (需要之前下载过依赖)
echo 3. 清理后构建 (清理缓存重新构建)
echo 4. 使用Android Studio构建 (推荐)
echo 5. 检查网络连接
echo 6. 退出
echo.

set /p choice=请选择 (1-6): 

if "%choice%"=="1" goto standard_build
if "%choice%"=="2" goto offline_build
if "%choice%"=="3" goto clean_build
if "%choice%"=="4" goto android_studio
if "%choice%"=="5" goto check_network
if "%choice%"=="6" goto exit

echo 无效选择，请重新运行脚本
pause
exit /b 1

:standard_build
echo.
echo 开始标准构建...
cd android
echo 正在构建APK，这可能需要几分钟...
.\gradlew.bat assembleRelease
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ 构建成功！
    echo APK位置: android\app\build\outputs\apk\release\app-release.apk
) else (
    echo.
    echo ❌ 构建失败，可能是网络问题
    echo 建议尝试选项4使用Android Studio构建
)
goto end

:offline_build
echo.
echo 开始离线构建...
cd android
echo 正在离线构建APK...
.\gradlew.bat assembleRelease --offline
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ 离线构建成功！
    echo APK位置: android\app\build\outputs\apk\release\app-release.apk
) else (
    echo.
    echo ❌ 离线构建失败，可能缺少依赖
    echo 请先在有网络的环境中运行标准构建
)
goto end

:clean_build
echo.
echo 清理项目缓存...
cd android
.\gradlew.bat clean
echo.
echo 开始清理后构建...
.\gradlew.bat assembleRelease
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ 清理构建成功！
    echo APK位置: android\app\build\outputs\apk\release\app-release.apk
) else (
    echo.
    echo ❌ 清理构建失败
)
goto end

:android_studio
echo.
echo 启动Android Studio...
echo 请在Android Studio中：
echo 1. 打开项目: File → Open → 选择 android 文件夹
echo 2. 同步项目: 点击 "Sync Project with Gradle Files"
echo 3. 构建APK: Build → Generate Signed Bundle / APK → APK → release
echo.
start "" "C:\Program Files\Android\Android Studio\bin\studio64.exe"
echo Android Studio已启动
goto end

:check_network
echo.
echo 检查网络连接...
ping -n 1 google.com >nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ 网络连接正常
) else (
    echo ❌ 网络连接异常
    echo 建议：
    echo 1. 检查网络连接
    echo 2. 配置VPN
    echo 3. 使用Android Studio构建
)
echo.
ping -n 1 plugins.gradle.org >nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ Gradle仓库可访问
) else (
    echo ❌ Gradle仓库无法访问
    echo 这是构建失败的主要原因
)
goto end

:end
echo.
echo ========================================
echo 构建完成
echo ========================================
echo.
echo 如果构建成功，APK文件位于:
echo android\app\build\outputs\apk\release\app-release.apk
echo.
echo 如果构建失败，建议：
echo 1. 使用Android Studio图形界面构建
echo 2. 配置网络代理或VPN
echo 3. 查看详细错误日志
echo.
pause

:exit
exit /b 0
