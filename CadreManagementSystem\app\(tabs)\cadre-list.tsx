import React from 'react';
import { View, StyleSheet } from 'react-native';
import { router } from 'expo-router';
import { CadreList } from '../../src/components/CadreList';
import { CadreInfo } from '../../src/types';

export default function CadreListScreen() {
  const handleCadrePress = (cadre: CadreInfo) => {
    router.push(`/cadre-detail?id=${cadre.id}&mode=view`);
  };

  const handleCadreEdit = (cadre: CadreInfo) => {
    router.push(`/cadre-detail?id=${cadre.id}&mode=edit`);
  };

  const handleAddPress = () => {
    router.push('/cadre-detail?mode=add');
  };

  return (
    <View style={styles.container}>
      <CadreList
        onCadrePress={handleCadrePress}
        onCadreEdit={handleCadreEdit}
        onAddPress={handleAddPress}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7'
  }
});
