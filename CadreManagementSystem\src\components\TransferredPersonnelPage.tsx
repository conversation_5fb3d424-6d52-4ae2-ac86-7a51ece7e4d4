import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { CadreInfo } from '../types';
import { CadreStatusDao } from '../database/cadreStatusDao';
import { CadreDetailModal } from './CadreDetailModal';
import { CadreStatusModal } from './CadreStatusModal';
import { ConfigurableRetirementCalculator } from '../utils/configurableRetirementCalculator';

const TransferredPersonnelPage: React.FC = () => {
  const navigation = useNavigation();
  const [transferredCadres, setTransferredCadres] = useState<CadreInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [statusModalVisible, setStatusModalVisible] = useState(false);
  const [selectedCadre, setSelectedCadre] = useState<CadreInfo | null>(null);
  // 新增：存储每个干部的调动备注
  const [transferReasons, setTransferReasons] = useState<Record<number, string>>({});

  // 计算年龄
  const calculateAge = (birthDate: string | number | undefined): number => {
    if (!birthDate) return 0;
    
    try {
      let date: Date;
      if (typeof birthDate === 'number') {
        date = new Date((birthDate - 25569) * 86400 * 1000);
      } else {
        date = new Date(birthDate);
      }
      
      if (isNaN(date.getTime())) return 0;
      
      const today = new Date();
      let age = today.getFullYear() - date.getFullYear();
      const monthDiff = today.getMonth() - date.getMonth();
      
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < date.getDate())) {
        age--;
      }
      
      return age;
    } catch (error) {
      return 0;
    }
  };

  // 加载调动干部数据
  const loadTransferredCadres = async () => {
    try {
      setLoading(true);
      console.log('📋 调动干部页面：开始加载数据...');

      // 获取所有标记为"已调动"状态的干部
      const transferred = await CadreStatusDao.getCadresByStatus('transferred');

      console.log(`📋 调动干部页面：找到${transferred.length}名调动干部`);
      setTransferredCadres(transferred);

      // 批量获取调动备注
      const reasons: Record<number, string> = {};
      for (const cadre of transferred) {
        if (cadre.id) {
          const reason = await CadreStatusDao.getCadreLatestStatusReason(cadre.id, 'transferred');
          reasons[cadre.id] = reason || '';
        }
      }
      setTransferReasons(reasons);
      console.log('📋 调动干部页面：调动备注加载完成');
    } catch (error) {
      console.error('加载调动干部数据失败:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    loadTransferredCadres();
  }, []);

  const onRefresh = () => {
    setRefreshing(true);
    loadTransferredCadres();
  };

  // 处理干部点击
  const handleCadrePress = (cadre: CadreInfo) => {
    setSelectedCadre(cadre);
    setDetailModalVisible(true);
  };

  // 处理干部长按
  const handleCadreLongPress = (cadre: CadreInfo) => {
    setSelectedCadre(cadre);
    setStatusModalVisible(true);
  };

  // 处理状态更新
  const handleStatusUpdated = () => {
    loadTransferredCadres();
  };

  // 格式化出生日期显示
  const formatBirthDate = (birthDate: string | number | undefined): string => {
    if (!birthDate) return '';
    
    try {
      let date: Date;
      if (typeof birthDate === 'number') {
        date = new Date((birthDate - 25569) * 86400 * 1000);
      } else {
        date = new Date(birthDate);
      }
      
      if (isNaN(date.getTime())) return '';
      
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    } catch (error) {
      return '';
    }
  };

  // 计算延迟退休信息（统一逻辑）
  const calculateDelayRetirement = (birthYear: number) => {
    // 使用统一的延迟退休计算逻辑
    let delayMonths = 0;

    if (birthYear >= 1965) {
      // 基础延迟3个月
      delayMonths = 3;

      if (birthYear >= 1966) {
        // 1966年及以后的额外延迟
        const extraYears = birthYear - 1965;

        if (birthYear <= 1970) {
          // 1966-1970: 每年额外2个月，最多1年
          delayMonths += Math.min(extraYears * 2, 12);
        } else if (birthYear <= 1980) {
          // 1971-1980: 前5年每年2个月，后续每年3个月，最多3年
          const firstPhase = Math.min(5, extraYears) * 2;
          const secondPhase = Math.max(0, extraYears - 5) * 3;
          delayMonths += Math.min(firstPhase + secondPhase, 36);
        } else {
          // 1981+: 前5年每年2个月，6-10年每年3个月，后续每年4个月，最多5年
          const firstPhase = Math.min(5, extraYears) * 2;
          const secondPhase = Math.min(5, Math.max(0, extraYears - 5)) * 3;
          const thirdPhase = Math.max(0, extraYears - 10) * 4;
          delayMonths += Math.min(firstPhase + secondPhase + thirdPhase, 60);
        }
      }
    }

    return {
      delayYears: Math.floor(delayMonths / 12),
      delayMonths: delayMonths % 12,
      totalDelayMonths: delayMonths
    };
  };

  // 计算距离退休的天数（统一逻辑）
  const calculateDaysUntilRetirement = (birthDate: Date, retirementAge: number): number => {
    const retirementDate = new Date(birthDate);
    retirementDate.setFullYear(birthDate.getFullYear() + Math.floor(retirementAge));

    // 处理延迟的月数
    const delayMonths = Math.round((retirementAge - Math.floor(retirementAge)) * 12);
    retirementDate.setMonth(retirementDate.getMonth() + delayMonths);

    const today = new Date();
    const diffTime = retirementDate.getTime() - today.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  // 检查是否符合退居二线条件
  const checkSecondLineCondition = (cadre: CadreInfo, age: number): boolean => {
    const position = cadre.现职级 || cadre.职务 || '';
    const isMiddleManagement = ['中层正职', '中层副职', '正处', '副处'].some(pos => position.includes(pos));
    return isMiddleManagement && age >= 56 && age <= 58;
  };

  // 渲染干部卡片 - 参考延迟退休页面的详细显示
  const renderCadreCard = (cadre: CadreInfo) => {
    const age = calculateAge(cadre.出生日期);
    const birthDate = new Date(cadre.出生日期 || '');
    const birthYear = birthDate.getFullYear();
    const gender = cadre.性别 || '男';

    // 计算退休相关信息
    const delayInfo = calculateDelayRetirement(birthYear);
    const baseRetirementAge = gender === '女' ? 55 : 60;
    const actualRetirementAge = baseRetirementAge + (delayInfo.totalDelayMonths / 12);

    // 计算距离退休的天数
    const legalRetirementDays = calculateDaysUntilRetirement(birthDate, baseRetirementAge);

    // 检查是否符合退居二线条件
    const meetsSecondLineCondition = checkSecondLineCondition(cadre, age);

    // 格式化时间显示
    const formatTimeDisplay = (days: number): { years: number; months: number; remainingDays: number } => {
      if (days <= 0) return { years: 0, months: 0, remainingDays: Math.abs(days) };

      const years = Math.floor(days / 365);
      const months = Math.floor((days % 365) / 30);
      const remainingDays = days % 30;

      return { years, months, remainingDays };
    };

    const legalTimeDisplay = formatTimeDisplay(legalRetirementDays);

    // 获取调动备注（从已加载的状态中获取）
    const transferReason = cadre.id ? (transferReasons[cadre.id] || '') : '';

    return (
      <TouchableOpacity
        key={cadre.id}
        style={styles.cadreCard}
        onPress={() => handleCadrePress(cadre)}
        onLongPress={() => handleCadreLongPress(cadre)}
        activeOpacity={0.7}
      >
        <View style={styles.cardHeader}>
          <View style={styles.nameSection}>
            <View style={styles.nameRow}>
              <Text style={styles.cadreName}>{cadre.姓名}</Text>
              <Text style={styles.birthDate}>
                {formatBirthDate(cadre.出生日期)}
              </Text>
            </View>
            <Text style={styles.unitPositionText} numberOfLines={1} ellipsizeMode="tail">
              {`${cadre.单位 || '未知单位'}|${cadre.职务 || '未知职务'}`}
            </Text>
          </View>
          <View style={styles.statusBadge}>
            <Text style={styles.statusText}>已调动</Text>
          </View>
        </View>

        <View style={styles.cardContent}>
          {/* 退休状态描述 */}
          <Text style={styles.statusInfoText}>
            {meetsSecondLineCondition ? '已达退居二线条件，现已调动' : '已调动状态'}
          </Text>

          {/* 实际退休年龄 */}
          <Text style={styles.retirementAgeText}>
            实际退休年龄: {actualRetirementAge.toFixed(1)}岁
          </Text>

          {/* 预计延迟退休信息 */}
          {delayInfo.delayYears > 0 || delayInfo.delayMonths > 0 ? (
            <Text style={styles.delayInfoText}>
              预计延迟{delayInfo.delayYears > 0 ? `${delayInfo.delayYears}年${delayInfo.delayMonths}个月` : `${delayInfo.delayMonths}个月`}退休
            </Text>
          ) : (
            <Text style={styles.delayInfoText}>无延迟退休</Text>
          )}

          {/* 距离法定退休时间 */}
          {legalRetirementDays > 0 ? (
            <View style={styles.timeInfoRow}>
              <Text style={styles.timeInfoText}>
                距离法定退休时间剩余{legalTimeDisplay.years > 0 ? `${legalTimeDisplay.years}年` : ''}
                {legalTimeDisplay.months > 0 ? `${legalTimeDisplay.months}个月` : ''}
                （剩余
              </Text>
              <Text style={styles.daysHighlight}>
                {legalRetirementDays}天
              </Text>
              <Text style={styles.timeInfoText}>）</Text>
            </View>
          ) : (
            <Text style={styles.timeInfoText}>已达到法定退休年龄</Text>
          )}

          {/* 调动备注信息 */}
          <View style={styles.transferReasonContainer}>
            <Text style={styles.transferReasonLabel}>调动情况:</Text>
            <Text style={styles.transferReasonText}>
              {transferReason || '未注明调离时间及调入单位'}
            </Text>
          </View>
        </View>

        <View style={styles.cardFooter}>
          <Text style={styles.footerText}>点击查看详情 · 长按修改状态</Text>
        </View>
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>正在加载调动干部数据...</Text>
      </View>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {/* 统计卡片 */}
      <View style={styles.statsContainer}>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{transferredCadres.length}</Text>
          <Text style={styles.statLabel}>调动干部</Text>
        </View>
      </View>

      {/* 干部列表 */}
      <View style={styles.cardContainer}>
        {transferredCadres.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>暂无调动干部</Text>
          </View>
        ) : (
          transferredCadres.map((cadre) => renderCadreCard(cadre))
        )}
      </View>

      {/* 弹窗组件 */}
      <CadreDetailModal
        visible={detailModalVisible}
        cadre={selectedCadre}
        onClose={() => setDetailModalVisible(false)}
      />

      <CadreStatusModal
        visible={statusModalVisible}
        cadre={selectedCadre}
        onClose={() => setStatusModalVisible(false)}
        onStatusUpdated={handleStatusUpdated}
      />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#8E8E93',
  },
  statsContainer: {
    padding: 16,
    backgroundColor: '#FFFFFF',
    marginBottom: 8,
  },
  statCard: {
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#34C759',
    borderRadius: 12,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
    color: '#FFFFFF',
    fontWeight: '500',
  },
  cardContainer: {
    padding: 16,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  emptyText: {
    fontSize: 16,
    color: '#8E8E93',
  },
  cadreCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 14,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 10,
  },
  nameSection: {
    flex: 1,
  },
  nameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  cadreName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    marginRight: 8,
  },
  birthDate: {
    fontSize: 14,
    color: '#34C759',
    fontWeight: '500',
  },
  unitPositionText: {
    fontSize: 13,
    color: '#8E8E93',
    fontWeight: '500',
  },
  statusBadge: {
    backgroundColor: '#34C759',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  statusText: {
    fontSize: 12,
    color: '#FFFFFF',
    fontWeight: '500',
  },
  cardContent: {
    marginBottom: 8,
  },
  statusInfoText: {
    fontSize: 13,
    color: '#34C759',
    fontWeight: '600',
    marginBottom: 3,
    lineHeight: 18,
  },
  ageText: {
    fontSize: 13,
    color: '#007AFF',
    fontWeight: '500',
    marginBottom: 3,
    lineHeight: 18,
  },
  positionText: {
    fontSize: 13,
    color: '#8E8E93',
    fontWeight: '500',
    marginBottom: 3,
    lineHeight: 18,
  },
  infoText: {
    fontSize: 12,
    color: '#8E8E93',
    lineHeight: 16,
  },
  cardFooter: {
    borderTopWidth: 1,
    borderTopColor: '#E5E5EA',
    paddingTop: 6,
    marginTop: 4,
  },
  footerText: {
    fontSize: 11,
    color: '#8E8E93',
    textAlign: 'center',
    lineHeight: 14,
  },
  // 新增样式 - 参考延迟退休页面
  retirementAgeText: {
    fontSize: 13,
    color: '#333',
    fontWeight: '500',
    marginBottom: 3,
    lineHeight: 18,
  },
  delayInfoText: {
    fontSize: 12,
    color: '#FF9500',
    marginBottom: 3,
    lineHeight: 16,
  },
  timeInfoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
    flexWrap: 'wrap',
  },
  timeInfoText: {
    fontSize: 12,
    color: '#666',
    lineHeight: 16,
  },
  daysHighlight: {
    fontSize: 12,
    color: '#FF3B30',
    fontWeight: 'bold',
    lineHeight: 16,
  },
  transferReasonContainer: {
    marginTop: 8,
    padding: 10,
    backgroundColor: '#F8F9FA',
    borderRadius: 8,
    borderLeftWidth: 3,
    borderLeftColor: '#5856D6',
  },
  transferReasonLabel: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
    marginBottom: 4,
  },
  transferReasonText: {
    fontSize: 13,
    color: '#333',
    lineHeight: 18,
  },
});

export default TransferredPersonnelPage;
