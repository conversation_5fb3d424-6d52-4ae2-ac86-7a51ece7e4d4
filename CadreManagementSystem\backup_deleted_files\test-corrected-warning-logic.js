/**
 * 测试修正后的预警逻辑
 */
console.log('🔍 测试修正后的预警逻辑...\n');

// 测试数据 - 重点测试已退居二线预警的修正
const testCadres = [
  // 1. 已退居二线预警 - 男性中层58岁以上
  {
    姓名: '张三',
    性别: '男',
    出生日期: '1966-01-01', // 58岁
    职务: '',
    现职级: '中层正职'
  },
  {
    姓名: '李四',
    性别: '男',
    出生日期: '1964-01-01', // 60岁
    职务: '',
    现职级: '中层副职'
  },
  
  // 2. 已退居二线预警 - 女性中层54岁以上
  {
    姓名: '王五',
    性别: '女',
    出生日期: '1970-01-01', // 54岁
    职务: '',
    现职级: '正处'
  },
  {
    姓名: '赵六',
    性别: '女',
    出生日期: '1968-01-01', // 56岁
    职务: '',
    现职级: '副处'
  },
  
  // 3. 近两年退休预警 - 非中层男性58岁以上
  {
    姓名: '孙七',
    性别: '男',
    出生日期: '1966-01-01', // 58岁
    职务: '普通员工',
    现职级: ''
  },
  
  // 4. 近两年退休预警 - 非中层女性53岁以上
  {
    姓名: '周八',
    性别: '女',
    出生日期: '1971-01-01', // 53岁
    职务: '普通员工',
    现职级: ''
  },
  
  // 5. 近两年退居二线预警 - 男性中层56-58岁
  {
    姓名: '吴九',
    性别: '男',
    出生日期: '1968-01-01', // 56岁
    职务: '',
    现职级: '中层正职'
  },
  {
    姓名: '郑十',
    性别: '男',
    出生日期: '1967-01-01', // 57岁
    职务: '',
    现职级: '中层副职'
  },
  
  // 6. 近两年退居二线预警 - 女性中层52-54岁
  {
    姓名: '钱十一',
    性别: '女',
    出生日期: '1972-01-01', // 52岁
    职务: '',
    现职级: '正处'
  },
  {
    姓名: '陈十二',
    性别: '女',
    出生日期: '1971-01-01', // 53岁
    职务: '',
    现职级: '副处'
  },
  
  // 7. 已退休预警 - 超过延迟退休年龄
  {
    姓名: '刘十三',
    性别: '男',
    出生日期: '1960-01-01', // 64岁
    职务: '普通员工',
    现职级: ''
  },
  
  // 8. 无预警 - 年龄不符合条件
  {
    姓名: '林十四',
    性别: '男',
    出生日期: '1970-01-01', // 54岁
    职务: '普通员工',
    现职级: ''
  }
];

// 模拟修正后的预警逻辑
function checkCorrectedWarningConditions(cadre, age, daysRemaining, actualRetirementAge, delayInfo) {
  const gender = cadre.性别;
  const position = cadre.现职级 || cadre.职务 || "";
  
  // 判断是否为中层管理人员
  const isMiddleManagement = position.includes("中层正职") || 
                            position.includes("中层副职") || 
                            position.includes("正处") || 
                            position.includes("副处");

  // 4. 已退休预警 - 最高优先级
  if (daysRemaining <= 0) {
    return { warningType: "retired", message: `已退休预警（${age}岁，已超过退休年龄${Math.abs(daysRemaining)}天）` };
  }

  // 3. 已退居二线预警 - 中层管理人员且在指定年龄范围内
  if (isMiddleManagement) {
    let shouldWarnAlreadySecondLine = false;
    if (gender === "男" && age >= 58 && age <= actualRetirementAge) {
      shouldWarnAlreadySecondLine = true;
    } else if (gender === "女" && age >= 54 && age <= actualRetirementAge) {
      shouldWarnAlreadySecondLine = true;
    }
    
    if (shouldWarnAlreadySecondLine) {
      return { warningType: "already_second_line", message: `已退居二线预警（${age}岁，${position}）` };
    }
  }

  // 2. 近两年退居二线预警 - 中层管理人员且在指定年龄范围
  if (isMiddleManagement) {
    let shouldWarnSecondLine = false;
    if (gender === "男" && age >= 56 && age <= 58) {
      shouldWarnSecondLine = true;
    } else if (gender === "女" && age >= 52 && age <= 54) {
      shouldWarnSecondLine = true;
    }
    
    if (shouldWarnSecondLine) {
      return { warningType: "near_second_line", message: `近两年退居二线预警（${age}岁，${position}）` };
    }
  }

  // 1. 近两年退休预警 - 在指定年龄范围内，排除已退居二线的人员
  let shouldWarnRetirement = false;
  if (gender === "男" && age >= 58) {
    shouldWarnRetirement = true;
  } else if (gender === "女" && age >= 53) {
    shouldWarnRetirement = true;
  }
  
  // 排除已退居二线的人员
  let isAlreadySecondLine = false;
  if (isMiddleManagement) {
    if ((gender === "男" && age >= 58 && age <= actualRetirementAge) ||
        (gender === "女" && age >= 54 && age <= actualRetirementAge)) {
      isAlreadySecondLine = true;
    }
  }
  
  if (shouldWarnRetirement && !isAlreadySecondLine) {
    return { warningType: "near_retirement", message: `近两年退休预警（${age}岁，还剩${daysRemaining}天退休）` };
  }

  return null;
}

// 其他辅助函数（与之前相同）
function parseBirthDate(birthDateStr) {
  if (!birthDateStr) return null;
  
  try {
    const cleanStr = birthDateStr.replace(/[年月日\-\.\/]/g, "-");
    const parts = cleanStr.split("-").filter(p => p.length > 0);
    
    if (parts.length >= 2) {
      const year = parseInt(parts[0]);
      const month = parseInt(parts[1]) - 1;
      const day = parts.length >= 3 ? parseInt(parts[2]) : 1;
      
      if (year > 1900 && year < 2100 && month >= 0 && month < 12) {
        return new Date(year, month, day);
      }
    }
  } catch (error) {
    console.error("解析出生日期失败:", birthDateStr, error);
  }
  
  return null;
}

function calculateAge(birthDate, currentDate) {
  const age = currentDate.getFullYear() - birthDate.getFullYear();
  const monthDiff = currentDate.getMonth() - birthDate.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && currentDate.getDate() < birthDate.getDate())) {
    return age - 1;
  }
  
  return age;
}

function getBaseRetirementAge(cadre) {
  const gender = cadre.性别;
  const position = cadre.现职级 || cadre.职务 || "";
  
  if (gender === "男") {
    return 60;
  } else if (gender === "女") {
    const isCadre = position.includes("职") || position.includes("长") || 
                    position.includes("主任") || position.includes("副主任") ||
                    position.includes("正职") || position.includes("副职");
    return isCadre ? 55 : 50;
  }
  
  return 60;
}

function calculateDelayRetirement(birthYear) {
  let totalDelayMonths = 0;
  
  if (birthYear <= 1964) {
    totalDelayMonths = 0;
  } else if (birthYear === 1965) {
    totalDelayMonths = 3;
  } else if (birthYear >= 1966 && birthYear <= 1970) {
    totalDelayMonths = 3;
    const extraYears = birthYear - 1965;
    const extraMonths = Math.min(extraYears * 2, 12);
    totalDelayMonths += extraMonths;
  } else if (birthYear >= 1971 && birthYear <= 1980) {
    totalDelayMonths = 3 + 12;
    const extraYears = birthYear - 1970;
    const extraMonths = Math.min(extraYears * 3, 24);
    totalDelayMonths += extraMonths;
  } else if (birthYear >= 1981) {
    totalDelayMonths = 3 + 12 + 24;
    const extraYears = birthYear - 1980;
    const extraMonths = Math.min(extraYears * 4, 24);
    totalDelayMonths += extraMonths;
  }

  return {
    delayYears: Math.floor(totalDelayMonths / 12),
    delayMonths: totalDelayMonths % 12,
    totalDelayMonths
  };
}

function analyzeSingleCadre(cadre, today) {
  console.log(`\n🔍 分析干部: ${cadre.姓名} (性别:${cadre.性别}, 出生日期:${cadre.出生日期}, 职级:${cadre.现职级 || cadre.职务})`);
  
  const birthDate = parseBirthDate(cadre.出生日期);
  if (!birthDate) {
    console.log(`❌ ${cadre.姓名}: 出生日期解析失败 '${cadre.出生日期}'`);
    return null;
  }

  const age = calculateAge(birthDate, today);
  const birthYear = birthDate.getFullYear();
  const baseRetirementAge = getBaseRetirementAge(cadre);
  const delayInfo = calculateDelayRetirement(birthYear);
  const actualRetirementAge = baseRetirementAge + (delayInfo.totalDelayMonths / 12);

  const retirementDate = new Date(birthDate);
  retirementDate.setFullYear(retirementDate.getFullYear() + Math.floor(actualRetirementAge));
  retirementDate.setMonth(retirementDate.getMonth() + Math.round((actualRetirementAge % 1) * 12));

  const daysRemaining = Math.ceil((retirementDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

  console.log(`📊 ${cadre.姓名}: 年龄${age}岁, 基础退休${baseRetirementAge}岁, 延迟${delayInfo.totalDelayMonths}个月, 实际退休${actualRetirementAge.toFixed(1)}岁, 距离${daysRemaining}天`);

  // 使用修正后的预警条件检查
  const warning = checkCorrectedWarningConditions(cadre, age, daysRemaining, actualRetirementAge, delayInfo);
  
  if (warning) {
    console.log(`⚠️ ${cadre.姓名}: ${warning.message}`);
    return { cadre, ...warning, daysRemaining };
  } else {
    console.log(`✅ ${cadre.姓名}: 无预警 (年龄${age}岁，不符合预警条件)`);
    return null;
  }
}

// 执行测试
console.log('🚀 开始分析修正后的测试数据...\n');

const today = new Date();
const warnings = [];

testCadres.forEach(cadre => {
  const warning = analyzeSingleCadre(cadre, today);
  if (warning) {
    warnings.push(warning);
  }
});

console.log(`\n📊 分析结果:`);
console.log(`总人数: ${testCadres.length}`);
console.log(`预警人数: ${warnings.length}`);

// 按预警类型分组统计
const warningStats = {};
warnings.forEach(warning => {
  const type = warning.warningType;
  if (!warningStats[type]) {
    warningStats[type] = [];
  }
  warningStats[type].push(warning);
});

console.log(`\n📋 预警类型统计:`);
Object.keys(warningStats).forEach(type => {
  const typeNames = {
    'near_retirement': '近两年退休预警',
    'near_second_line': '近两年退居二线预警',
    'already_second_line': '已退居二线预警',
    'retired': '已退休预警'
  };
  
  console.log(`${typeNames[type] || type}: ${warningStats[type].length}人`);
  warningStats[type].forEach(warning => {
    console.log(`  - ${warning.cadre.姓名} (${warning.cadre.性别}, ${calculateAge(parseBirthDate(warning.cadre.出生日期), today)}岁, ${warning.cadre.现职级 || warning.cadre.职务})`);
  });
});

console.log(`\n🎯 修正后预警逻辑验证:`);
console.log(`1. ✅ 近两年退休预警: 男性≥58岁, 女性≥53岁, 排除已退居二线`);
console.log(`2. ✅ 近两年退居二线预警: 中层管理, 男性56-58岁, 女性52-54岁`);
console.log(`3. ✅ 已退居二线预警: 中层管理, 男性≥58岁, 女性≥54岁, ≤实际退休年龄`);
console.log(`4. ✅ 已退休预警: 超过延迟退休年龄`);
console.log(`5. ✅ 优先级: 已退休 > 已退居二线 > 近两年退居二线 > 近两年退休`);
console.log(`6. ✅ 排除逻辑: 已退居二线人员不出现在近两年退休预警中`);
