import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  RefreshControl
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useFocusEffect } from '@react-navigation/native';
import { CadreDao } from '../../src/database/cadreDao';
import { useTheme } from '../../src/contexts/ThemeContext';
import { ConfigurableRetirementCalculator } from '../../src/utils/configurableRetirementCalculator';
import { CadreStatusDao } from '../../src/database/cadreStatusDao';
import { DelayedRetirementPage } from '../../src/components/DelayedRetirementPage';
import TransferredPersonnelPage from '../../src/components/TransferredPersonnelPage';

const screenWidth = Dimensions.get('window').width;

export default function HomeScreen() {
  const { theme } = useTheme();
  const [statistics, setStatistics] = useState({
    totalCadres: 0,
    activeCadres: 0,
    retiredCadres: 0,
    warningCount: 0,
    delayedRetirement: 0,
    transferred: 0,
    secondLineRetired: 0,
    // 新增详细预警统计
    nearRetirement: 0,
    nearSecondLine: 0,
    alreadySecondLine: 0,
    alreadyRetired: 0
  });
  const [refreshing, setRefreshing] = useState(false);
  const [showDelayedRetirementPage, setShowDelayedRetirementPage] = useState(false);
  const [showTransferredPersonnelPage, setShowTransferredPersonnelPage] = useState(false);

  useEffect(() => {
    loadStatistics();
  }, []);

  // 监听页面焦点，重新加载数据
  useFocusEffect(
    React.useCallback(() => {
      loadStatistics();
    }, [])
  );

  const loadStatistics = async () => {
    try {
      console.log('🏠 首页：开始加载统计数据...');
      const cadres = await CadreDao.getAllCadres();
      console.log(`🏠 首页：获取到${cadres.length}条干部数据`);

      const totalCadres = cadres.length;

      // 如果没有数据，显示测试数据
      if (totalCadres === 0) {
        console.log('🏠 首页：没有数据，显示测试统计');
        setStatistics({
          totalCadres: 2,
          activeCadres: 2,
          retiredCadres: 0,
          warningCount: 2,
          delayedRetirement: 0,
          transferred: 0,
          secondLineRetired: 0,
          // 新增详细预警统计
          nearRetirement: 1,
          nearSecondLine: 1,
          alreadySecondLine: 0,
          alreadyRetired: 0
        });
        return;
      }

      // 获取状态统计
      const statusStats = await CadreStatusDao.getStatusStatistics();
      console.log('🏠 首页：状态统计', statusStats);

      // 计算退休预警数量
      console.log('🏠 首页：计算退休预警数量...');
      const warnings = await ConfigurableRetirementCalculator.generateWarnings(cadres);
      const warningCount = warnings.length;
      console.log(`🏠 首页：生成${warningCount}条预警`);

      // 计算各类预警人员数量
      const nearRetirement = warnings.filter(w => w.warningType === 'near_retirement').length;
      const nearSecondLine = warnings.filter(w => w.warningType === 'near_second_line').length;
      const alreadySecondLine = warnings.filter(w => w.warningType === 'already_second_line').length;
      const alreadyRetired = warnings.filter(w => w.warningType === 'retired').length;

      // 计算各类人员数量
      // 从预警数据中统计各类人员
      const retiredFromWarnings = alreadyRetired;
      const secondLineRetiredFromWarnings = alreadySecondLine;

      // 从状态统计中获取手动标记的各类人员
      const manualRetired = statusStats.retired || 0;
      const manualSecondLineRetired = statusStats.second_line_retired || 0;
      const delayedRetirement = statusStats.delayed_retirement || 0;
      const transferred = statusStats.transferred || 0;

      // 总的已退休人员 = 预警计算出的 + 手动标记的
      const totalRetiredCadres = retiredFromWarnings + manualRetired;

      // 总的已退居二线人员 = 预警计算出的 + 手动标记的
      const secondLineRetiredCadres = secondLineRetiredFromWarnings + manualSecondLineRetired;

      // 在职干部 = 总数 - 已退休 - 已退居二线 - 调动干部（延迟退休算作在职）
      const activeCadres = totalCadres - totalRetiredCadres - secondLineRetiredCadres - transferred;

      console.log(`🏠 首页统计详情：`);
      console.log(`  总干部数: ${totalCadres}`);
      console.log(`  预警计算已退休: ${retiredFromWarnings}`);
      console.log(`  手动标记已退休: ${manualRetired}`);
      console.log(`  总已退休: ${totalRetiredCadres}`);
      console.log(`  预警计算已退居二线: ${secondLineRetiredFromWarnings}`);
      console.log(`  手动标记已退居二线: ${manualSecondLineRetired}`);
      console.log(`  总已退居二线: ${secondLineRetiredCadres}`);
      console.log(`  延迟退休: ${delayedRetirement} (算作在职)`);
      console.log(`  调动干部: ${transferred}`);
      console.log(`  在职干部: ${activeCadres} (包含延迟退休人员)`);
      console.log(`  预警人数: ${warningCount}`);

      setStatistics({
        totalCadres,
        activeCadres,
        retiredCadres: totalRetiredCadres,
        warningCount,
        delayedRetirement,
        transferred,
        secondLineRetired: secondLineRetiredCadres,
        // 新增详细预警统计
        nearRetirement,
        nearSecondLine,
        alreadySecondLine,
        alreadyRetired
      });
    } catch (error) {
      console.error('❌ 首页：加载统计数据失败:', error);
      // 设置默认值
      setStatistics({
        totalCadres: 0,
        activeCadres: 0,
        retiredCadres: 0,
        warningCount: 0,
        delayedRetirement: 0,
        transferred: 0,
        secondLineRetired: 0,
        // 新增详细预警统计
        nearRetirement: 0,
        nearSecondLine: 0,
        alreadySecondLine: 0,
        alreadyRetired: 0
      });
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadStatistics();
    setRefreshing(false);
  };

  // 统计卡片配置，分为三行显示
  // 第一行：总体统计（3个卡片）
  const firstRowCards = [
    {
      key: 'totalCadres',
      value: statistics.totalCadres,
      label: '总干部数',
      color: '#007AFF',
      onPress: () => router.push('/(tabs)/cadre-list')
    },
    {
      key: 'activeCadres',
      value: statistics.activeCadres,
      label: '在职干部',
      color: '#34C759',
      onPress: () => router.push('/(tabs)/cadre-list')
    },
    {
      key: 'warningCount',
      value: statistics.warningCount,
      label: '预警人数',
      color: '#FF9500',
      onPress: () => router.push('/(tabs)/retirement-warning')
    }
  ];

  // 第二行：预警详情（4个卡片）
  const secondRowCards = [
    {
      key: 'nearRetirement',
      value: statistics.nearRetirement,
      label: '近两年退休',
      color: '#FF3B30',
      onPress: () => router.push('/(tabs)/retirement-warning')
    },
    {
      key: 'nearSecondLine',
      value: statistics.nearSecondLine,
      label: '近两年退居二线',
      color: '#FF9500',
      onPress: () => router.push('/(tabs)/retirement-warning')
    },
    {
      key: 'alreadySecondLine',
      value: statistics.alreadySecondLine,
      label: '已退居二线',
      color: '#FF69B4',
      onPress: () => router.push('/(tabs)/retirement-warning')
    },
    {
      key: 'alreadyRetired',
      value: statistics.alreadyRetired,
      label: '已退休',
      color: '#8E8E93',
      onPress: () => router.push('/(tabs)/retirement-warning')
    }
  ];

  // 第三行：其他状态（3个卡片）
  const thirdRowCards = [
    {
      key: 'delayedRetirement',
      value: statistics.delayedRetirement,
      label: '延迟退休',
      color: '#5856D6',
      onPress: () => setShowDelayedRetirementPage(true)
    },
    {
      key: 'transferred',
      value: statistics.transferred,
      label: '调动干部',
      color: '#32D74B',
      onPress: () => setShowTransferredPersonnelPage(true)
    },
    {
      key: 'placeholder',
      value: '',
      label: '',
      color: 'transparent',
      onPress: () => {}
    }
  ];

  const quickActions = [
    {
      title: '导入Excel',
      icon: 'document',
      color: '#34C759',
      onPress: () => router.push('/import-excel')
    },
    {
      title: '职级管理',
      icon: 'list',
      color: '#FF9500',
      onPress: () => router.push('/position-management')
    },
    {
      title: '退休预警',
      icon: 'warning',
      color: '#FF3B30',
      onPress: () => router.push('/(tabs)/retirement-warning')
    },
    {
      title: '添加干部',
      icon: 'person-add',
      color: '#007AFF',
      onPress: () => router.push('/cadre-detail?mode=add')
    }
  ];

  // 如果显示延迟退休页面，则渲染延迟退休页面
  if (showDelayedRetirementPage) {
    return (
      <DelayedRetirementPage
        onBack={() => setShowDelayedRetirementPage(false)}
      />
    );
  }

  // 如果显示调动干部页面，则渲染调动干部页面
  if (showTransferredPersonnelPage) {
    return (
      <View style={{ flex: 1 }}>
        <View style={{ flexDirection: 'row', alignItems: 'center', padding: 16, backgroundColor: '#fff' }}>
          <TouchableOpacity onPress={() => setShowTransferredPersonnelPage(false)}>
            <Ionicons name="arrow-back" size={24} color="#007AFF" />
          </TouchableOpacity>
          <Text style={{ fontSize: 18, fontWeight: '600', marginLeft: 12 }}>调动干部</Text>
        </View>
        <TransferredPersonnelPage />
      </View>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {/* 头部欢迎区域 */}
      <View style={[styles.header, { backgroundColor: theme.colors.primary }]}>
        <Text style={styles.welcomeText}>智慧干部信息管理系统</Text>
        <Text style={styles.subtitleText}>国有企业干部管理解决方案</Text>
      </View>

      {/* 统计卡片 - 3x4x3布局 */}
      <View style={styles.statsContainer}>
        {/* 第一行：总干部数、在职干部、预警人数 */}
        <View style={styles.statsRow}>
          {firstRowCards.map((card) => (
            <TouchableOpacity
              key={card.key}
              style={[styles.statCard, styles.statCardMedium, { backgroundColor: card.color }]}
              onPress={card.onPress}
            >
              <Text style={styles.statNumber}>{card.value}</Text>
              <Text style={styles.statLabel}>{card.label}</Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* 第二行：近两年退休、近两年退居二线、已退居二线、已退休 */}
        <View style={styles.statsRow}>
          {secondRowCards.map((card) => (
            <TouchableOpacity
              key={card.key}
              style={[styles.statCard, styles.statCardSmall, { backgroundColor: card.color }]}
              onPress={card.onPress}
            >
              <Text style={styles.statNumber}>{card.value}</Text>
              <Text style={styles.statLabel}>{card.label}</Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* 第三行：延迟退休、调动干部、占位符 */}
        <View style={styles.statsRow}>
          {thirdRowCards.map((card) => (
            card.key === 'placeholder' ? (
              <View key={card.key} style={[styles.statCard, styles.statCardMedium, { backgroundColor: 'transparent' }]} />
            ) : (
              <TouchableOpacity
                key={card.key}
                style={[styles.statCard, styles.statCardMedium, { backgroundColor: card.color }]}
                onPress={card.onPress}
              >
                <Text style={styles.statNumber}>{card.value}</Text>
                <Text style={styles.statLabel}>{card.label}</Text>
              </TouchableOpacity>
            )
          ))}
        </View>
      </View>

      {/* 快捷操作 - 1×4布局 */}
      <View style={styles.quickActionsContainer}>
        <Text style={styles.sectionTitle}>快捷操作</Text>
        <View style={styles.actionsRow}>
          {quickActions.map((action, index) => (
            <TouchableOpacity
              key={index}
              style={styles.actionCard}
              onPress={action.onPress}
            >
              <View style={[styles.actionIcon, { backgroundColor: action.color }]}>
                <Ionicons name={action.icon as any} size={20} color="#FFF" />
              </View>
              <Text style={styles.actionTitle}>{action.title}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* 功能模块 */}
      <View style={styles.modulesContainer}>
        <Text style={styles.sectionTitle}>功能模块</Text>

        <TouchableOpacity
          style={styles.moduleCard}
          onPress={() => router.push('/(tabs)/cadre-list')}
        >
          <View style={styles.moduleHeader}>
            <Ionicons name="people" size={24} color="#007AFF" />
            <Text style={styles.moduleTitle}>干部信息管理</Text>
            <Ionicons name="chevron-forward" size={20} color="#8E8E93" />
          </View>
          <Text style={styles.moduleDescription}>
            查看、添加、编辑干部信息，支持高级搜索和筛选功能
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.moduleCard}
          onPress={() => router.push('/(tabs)/retirement-warning')}
        >
          <View style={styles.moduleHeader}>
            <Ionicons name="warning" size={24} color="#FF3B30" />
            <Text style={styles.moduleTitle}>退休预警系统</Text>
            <Ionicons name="chevron-forward" size={20} color="#8E8E93" />
          </View>
          <Text style={styles.moduleDescription}>
            自动计算退休预警，支持多维度预警分析和可视化展示
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.moduleCard}
          onPress={() => router.push('/position-management')}
        >
          <View style={styles.moduleHeader}>
            <Ionicons name="list" size={24} color="#FF9500" />
            <Text style={styles.moduleTitle}>职级分类管理</Text>
            <Ionicons name="chevron-forward" size={20} color="#8E8E93" />
          </View>
          <Text style={styles.moduleDescription}>
            管理企业职级体系，支持职级的增删改查和分类筛选
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7'
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 30,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 6,
  },
  welcomeText: {
    fontSize: 24,
    fontWeight: '700',
    color: '#FFF',
    marginBottom: 8
  },
  subtitleText: {
    fontSize: 16,
    color: '#E3F2FD',
    opacity: 0.9
  },
  statsContainer: {
    paddingHorizontal: 20,
    marginTop: -20
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 10,
  },
  statCard: {
    backgroundColor: '#007AFF',
    borderRadius: 12,
    paddingVertical: 8,
    paddingHorizontal: 6,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 70,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statCardLarge: {
    width: (screenWidth - 60) / 2,
  },
  statCardMedium: {
    width: (screenWidth - 80) / 3,
  },
  statCardSmall: {
    width: (screenWidth - 100) / 4,
  },
  statNumber: {
    fontSize: 22,
    fontWeight: '700',
    color: '#FFF',
    marginBottom: 2
  },
  statLabel: {
    fontSize: 9,
    color: '#FFF',
    opacity: 0.9,
    textAlign: 'center',
    lineHeight: 11,
    numberOfLines: 1,
  },
  quickActionsContainer: {
    paddingHorizontal: 20,
    marginTop: 16
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000',
    marginBottom: 16
  },
  actionsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionCard: {
    width: (screenWidth - 80) / 4,
    backgroundColor: '#FFF',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    marginHorizontal: 4,
  },
  actionIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8
  },
  actionTitle: {
    fontSize: 10,
    fontWeight: '500',
    color: '#000',
    textAlign: 'center',
    lineHeight: 12,
    numberOfLines: 1,
  },
  modulesContainer: {
    paddingHorizontal: 20,
    marginTop: 20,
    paddingBottom: 20
  },
  moduleCard: {
    backgroundColor: '#FFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3
  },
  moduleHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8
  },
  moduleTitle: {
    flex: 1,
    fontSize: 16,
    fontWeight: '600',
    color: '#000',
    marginLeft: 12
  },
  moduleDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20
  }
});
