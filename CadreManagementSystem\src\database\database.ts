import { Platform } from 'react-native';

// Web环境下的模拟数据库
class WebDatabase {
  private data: { [key: string]: any[] } = {};

  async execAsync(sql: string): Promise<void> {
    console.log('Web模拟执行SQL:', sql);
    // 解析CREATE TABLE语句
    if (sql.includes('CREATE TABLE')) {
      const tableName = sql.match(/CREATE TABLE (\w+)/)?.[1];
      if (tableName && !this.data[tableName]) {
        this.data[tableName] = [];
      }
    }
  }

  async runAsync(sql: string, params: any[] = []): Promise<{ lastInsertRowId: number; changes: number }> {
    console.log('Web模拟运行SQL:', sql, params);

    if (sql.includes('INSERT INTO')) {
      const tableName = sql.match(/INSERT INTO (\w+)/)?.[1];
      if (tableName) {
        const id = Math.floor(Math.random() * 10000);
        const record = { id, ...this.parseInsertParams(sql, params) };
        this.data[tableName] = this.data[tableName] || [];
        this.data[tableName].push(record);
        return { lastInsertRowId: id, changes: 1 };
      }
    }

    return { lastInsertRowId: 0, changes: 0 };
  }

  async getFirstAsync(sql: string, params: any[] = []): Promise<any> {
    console.log('Web模拟查询单条:', sql, params);
    const results = await this.getAllAsync(sql, params);
    return results[0] || null;
  }

  async getAllAsync(sql: string, params: any[] = []): Promise<any[]> {
    console.log('Web模拟查询所有:', sql, params);

    if (sql.includes('SELECT')) {
      const tableName = sql.match(/FROM (\w+)/)?.[1];
      if (tableName && this.data[tableName]) {
        return this.data[tableName];
      }
    }

    return [];
  }

  async closeAsync(): Promise<void> {
    console.log('Web模拟关闭数据库');
  }

  async withTransactionAsync(callback: () => Promise<void>): Promise<void> {
    await callback();
  }

  private parseInsertParams(sql: string, params: any[]): any {
    // 简单的参数解析
    const obj: any = {};
    params.forEach((param, index) => {
      obj[`field_${index}`] = param;
    });
    return obj;
  }
}

// 跨平台数据库适配器
let SQLite: any;
if (Platform.OS === 'web') {
  SQLite = {
    openDatabaseAsync: async (name: string) => new WebDatabase()
  };
} else {
  SQLite = require('expo-sqlite');
}

// 数据库配置
const DATABASE_NAME = 'cadre_management.db';
const DATABASE_VERSION = 1;

// 数据库实例
let db: SQLite.SQLiteDatabase | null = null;

// 初始化数据库
export const initDatabase = async (): Promise<SQLite.SQLiteDatabase> => {
  if (db) {
    return db;
  }

  try {
    db = await SQLite.openDatabaseAsync(DATABASE_NAME);
    await createTables();
    console.log('数据库初始化成功');
    return db;
  } catch (error) {
    console.error('数据库初始化失败:', error);
    throw error;
  }
};

// 获取数据库实例
export const getDatabase = (): SQLite.SQLiteDatabase => {
  if (!db) {
    throw new Error('数据库未初始化，请先调用 initDatabase()');
  }
  return db;
};

// 创建数据表
const createTables = async () => {
  if (!db) return;

  try {
    // 创建干部信息表
    await db.execAsync(`
      CREATE TABLE IF NOT EXISTS cadres (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        序号 TEXT,
        单位 TEXT,
        姓名 TEXT NOT NULL,
        职务 TEXT,
        性别 TEXT,
        民族 TEXT,
        籍贯 TEXT,
        出生日期 TEXT,
        参加工作时间 TEXT,
        政治面貌 TEXT,
        入党时间 TEXT,
        全日制教育 TEXT,
        毕业院校系及专业 TEXT,
        在职教育 TEXT,
        毕业院校系及专业_在职 TEXT,
        专业技术职务 TEXT,
        现职级 TEXT,
        任现职时间 TEXT,
        任现职级时间 TEXT,
        工作简历 TEXT,
        身份证号 TEXT UNIQUE,
        身份证号码校验正误 TEXT,
        联系方式 TEXT,
        获得奖励荣誉情况 TEXT,
        党纪政纪处分情况 TEXT,
        备注 TEXT,
        照片路径 TEXT,
        退休状态 TEXT DEFAULT 'active',
        创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
        更新时间 DATETIME DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // 创建职级管理表
    await db.execAsync(`
      CREATE TABLE IF NOT EXISTS position_levels (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        level_name TEXT NOT NULL UNIQUE,
        level_category TEXT,
        sort_order INTEGER DEFAULT 0,
        is_active INTEGER DEFAULT 1,
        创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
        更新时间 DATETIME DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // 创建退休预警配置表
    await db.execAsync(`
      CREATE TABLE IF NOT EXISTS retirement_config (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        config_type TEXT NOT NULL,
        config_name TEXT NOT NULL,
        config_value TEXT NOT NULL,
        description TEXT,
        创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
        更新时间 DATETIME DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // 创建系统设置表
    await db.execAsync(`
      CREATE TABLE IF NOT EXISTS system_settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        setting_key TEXT NOT NULL UNIQUE,
        setting_value TEXT,
        setting_type TEXT DEFAULT 'string',
        description TEXT,
        创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
        更新时间 DATETIME DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // 创建新的职级表（更完善的结构）
    await db.execAsync(`
      CREATE TABLE IF NOT EXISTS position_levels_new (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT UNIQUE NOT NULL,
        level INTEGER NOT NULL,
        category TEXT NOT NULL CHECK (category IN ('administrative', 'technical', 'worker')),
        retirement_age_male INTEGER,
        retirement_age_female INTEGER,
        description TEXT,
        is_active INTEGER DEFAULT 1,
        sort_order INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // 创建退休规则表
    await db.execAsync(`
      CREATE TABLE IF NOT EXISTS retirement_rules (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        position_level_id INTEGER,
        gender TEXT NOT NULL CHECK (gender IN ('male', 'female', 'all')),
        base_retirement_age INTEGER NOT NULL,
        delay_start_year INTEGER NOT NULL,
        delay_months_per_year INTEGER NOT NULL,
        max_retirement_age INTEGER NOT NULL,
        is_active INTEGER DEFAULT 1,
        description TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (position_level_id) REFERENCES position_levels_new (id)
      );
    `);

    // 创建数据清空密码表
    await db.execAsync(`
      CREATE TABLE IF NOT EXISTS data_clear_passwords (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        password_hash TEXT NOT NULL,
        salt TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // 插入默认职级数据
    await insertDefaultPositionLevels();

    // 插入默认系统设置
    await insertDefaultSettings();

    // 暂时注释掉新功能的初始化，避免错误
    // try {
    //   await initializeNewPositionLevels();
    // } catch (error) {
    //   console.error('初始化职级数据失败:', error);
    // }

    // try {
    //   await initializeRetirementRules();
    // } catch (error) {
    //   console.error('初始化退休规则失败:', error);
    // }

    console.log('数据表创建成功');
  } catch (error) {
    console.error('创建数据表失败:', error);
    throw error;
  }
};

// 插入默认职级数据
const insertDefaultPositionLevels = async () => {
  if (!db) return;

  const defaultLevels = [
    { level_name: '集团中层正职', level_category: '集团级', sort_order: 1 },
    { level_name: '集团中层副职', level_category: '集团级', sort_order: 2 },
    { level_name: '二级公司中层正职', level_category: '二级公司', sort_order: 3 },
    { level_name: '二级公司中层副职', level_category: '二级公司', sort_order: 4 },
    { level_name: '二级公司主管级', level_category: '二级公司', sort_order: 5 },
    { level_name: '二级公司主办级', level_category: '二级公司', sort_order: 6 },
    { level_name: '二级公司一级技术主管', level_category: '二级公司', sort_order: 7 },
    { level_name: '二级公司二级技术主管', level_category: '二级公司', sort_order: 8 },
    { level_name: '三级公司班子成员', level_category: '三级公司', sort_order: 9 },
    { level_name: '三级公司中层正职', level_category: '三级公司', sort_order: 10 },
    { level_name: '三级公司中层副职', level_category: '三级公司', sort_order: 11 },
    { level_name: '三级公司主管级', level_category: '三级公司', sort_order: 12 },
    { level_name: '三级公司主办级', level_category: '三级公司', sort_order: 13 },
    { level_name: '三级公司一级技术主管', level_category: '三级公司', sort_order: 14 },
    { level_name: '三级公司二级技术主管', level_category: '三级公司', sort_order: 15 }
  ];

  try {
    for (const level of defaultLevels) {
      await db.runAsync(
        `INSERT OR IGNORE INTO position_levels (level_name, level_category, sort_order) VALUES (?, ?, ?)`,
        [level.level_name, level.level_category, level.sort_order]
      );
    }
    console.log('默认职级数据插入成功');
  } catch (error) {
    console.error('插入默认职级数据失败:', error);
  }
};

// 插入默认系统设置
const insertDefaultSettings = async () => {
  if (!db) return;

  const defaultSettings = [
    { setting_key: 'male_retirement_age', setting_value: '60', setting_type: 'number', description: '男性退休年龄' },
    { setting_key: 'female_retirement_age', setting_value: '58', setting_type: 'number', description: '女性退休年龄' },
    { setting_key: 'male_early_warning_age', setting_value: '58', setting_type: 'number', description: '男性退休预警年龄' },
    { setting_key: 'female_early_warning_age', setting_value: '56', setting_type: 'number', description: '女性退休预警年龄' },
    { setting_key: 'second_line_age', setting_value: '56', setting_type: 'number', description: '退居二线年龄' },
    { setting_key: 'app_version', setting_value: '1.0.0', setting_type: 'string', description: '应用版本' }
  ];

  try {
    for (const setting of defaultSettings) {
      await db.runAsync(
        `INSERT OR IGNORE INTO system_settings (setting_key, setting_value, setting_type, description) VALUES (?, ?, ?, ?)`,
        [setting.setting_key, setting.setting_value, setting.setting_type, setting.description]
      );
    }
    console.log('默认系统设置插入成功');
  } catch (error) {
    console.error('插入默认系统设置失败:', error);
  }
};

// 初始化新的职级数据
const initializeNewPositionLevels = async () => {
  if (!db) return;

  const defaultPositions = [
    // 行政职级
    { name: '正部级', level: 10, category: 'administrative', retirement_age_male: 65, retirement_age_female: 60, sort_order: 1 },
    { name: '副部级', level: 9, category: 'administrative', retirement_age_male: 65, retirement_age_female: 60, sort_order: 2 },
    { name: '正厅级', level: 8, category: 'administrative', retirement_age_male: 60, retirement_age_female: 55, sort_order: 3 },
    { name: '副厅级', level: 7, category: 'administrative', retirement_age_male: 60, retirement_age_female: 55, sort_order: 4 },
    { name: '正处级', level: 6, category: 'administrative', retirement_age_male: 60, retirement_age_female: 55, sort_order: 5 },
    { name: '副处级', level: 5, category: 'administrative', retirement_age_male: 60, retirement_age_female: 55, sort_order: 6 },
    { name: '正科级', level: 4, category: 'administrative', retirement_age_male: 60, retirement_age_female: 55, sort_order: 7 },
    { name: '副科级', level: 3, category: 'administrative', retirement_age_male: 60, retirement_age_female: 55, sort_order: 8 },
    { name: '科员', level: 2, category: 'administrative', retirement_age_male: 60, retirement_age_female: 55, sort_order: 9 },
    { name: '办事员', level: 1, category: 'administrative', retirement_age_male: 60, retirement_age_female: 55, sort_order: 10 },

    // 技术职级
    { name: '正高级', level: 8, category: 'technical', retirement_age_male: 65, retirement_age_female: 60, sort_order: 11 },
    { name: '副高级', level: 7, category: 'technical', retirement_age_male: 60, retirement_age_female: 55, sort_order: 12 },
    { name: '中级', level: 6, category: 'technical', retirement_age_male: 60, retirement_age_female: 55, sort_order: 13 },
    { name: '初级', level: 5, category: 'technical', retirement_age_male: 60, retirement_age_female: 55, sort_order: 14 },

    // 工人职级
    { name: '高级技师', level: 5, category: 'worker', retirement_age_male: 60, retirement_age_female: 50, sort_order: 15 },
    { name: '技师', level: 4, category: 'worker', retirement_age_male: 60, retirement_age_female: 50, sort_order: 16 },
    { name: '高级工', level: 3, category: 'worker', retirement_age_male: 60, retirement_age_female: 50, sort_order: 17 },
    { name: '中级工', level: 2, category: 'worker', retirement_age_male: 60, retirement_age_female: 50, sort_order: 18 },
    { name: '初级工', level: 1, category: 'worker', retirement_age_male: 60, retirement_age_female: 50, sort_order: 19 },
  ];

  try {
    for (const position of defaultPositions) {
      await db.runAsync(
        `INSERT OR IGNORE INTO position_levels_new (name, level, category, retirement_age_male, retirement_age_female, description, sort_order)
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [
          position.name,
          position.level,
          position.category,
          position.retirement_age_male,
          position.retirement_age_female,
          `${position.category === 'administrative' ? '行政' : position.category === 'technical' ? '技术' : '工人'}职级`,
          position.sort_order
        ]
      );
    }
    console.log('新职级数据初始化成功');
  } catch (error) {
    console.error('初始化新职级数据失败:', error);
  }
};

// 初始化退休规则
const initializeRetirementRules = async () => {
  if (!db) return;

  const defaultRules = [
    // 通用规则
    {
      position_level_id: null,
      gender: 'male',
      base_retirement_age: 60,
      delay_start_year: 2025,
      delay_months_per_year: 3,
      max_retirement_age: 65,
      description: '男性通用延迟退休规则'
    },
    {
      position_level_id: null,
      gender: 'female',
      base_retirement_age: 55,
      delay_start_year: 2025,
      delay_months_per_year: 3,
      max_retirement_age: 60,
      description: '女性通用延迟退休规则'
    }
  ];

  try {
    for (const rule of defaultRules) {
      await db.runAsync(
        `INSERT OR IGNORE INTO retirement_rules (position_level_id, gender, base_retirement_age, delay_start_year, delay_months_per_year, max_retirement_age, description)
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [
          rule.position_level_id,
          rule.gender,
          rule.base_retirement_age,
          rule.delay_start_year,
          rule.delay_months_per_year,
          rule.max_retirement_age,
          rule.description
        ]
      );
    }
    console.log('退休规则初始化成功');
  } catch (error) {
    console.error('初始化退休规则失败:', error);
  }
};

// 关闭数据库连接
export const closeDatabase = async () => {
  if (db) {
    await db.closeAsync();
    db = null;
    console.log('数据库连接已关闭');
  }
};
