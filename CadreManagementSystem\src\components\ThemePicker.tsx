import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../contexts/ThemeContext';

interface ThemePickerProps {
  visible: boolean;
  onClose: () => void;
}

export const ThemePicker: React.FC<ThemePickerProps> = ({ visible, onClose }) => {
  const { theme, themeName, setTheme, availableThemes } = useTheme();

  const themePreviewData = {
    premium: {
      description: '深色奢华风格，适合高端商务场景',
      features: ['深色主题', '奢华质感', '高对比度', '专业感强'],
      color: '#1A1A2E',
    },
    business: {
      description: '专业蓝色风格，适合正式商务环境',
      features: ['专业配色', '商务风格', '清晰易读', '稳重大方'],
      color: '#1E3A8A',
    },
    minimalist: {
      description: '清新简洁风格，注重内容展示',
      features: ['简洁设计', '清新配色', '轻量感', '现代时尚'],
      color: '#2DD4BF',
    },
    classic: {
      description: '传统稳重风格，经典耐看',
      features: ['经典配色', '稳重风格', '传统美感', '持久耐看'],
      color: '#7C3AED',
    },
  };

  const handleThemeSelect = (selectedTheme: string) => {
    setTheme(selectedTheme);
    onClose();
  };

  const renderThemeCard = (themeInfo: { name: string; displayName: string }) => {
    const isSelected = themeName === themeInfo.name;
    const previewData = themePreviewData[themeInfo.name as keyof typeof themePreviewData];

    return (
      <TouchableOpacity
        key={themeInfo.name}
        style={[
          styles.themeCard,
          { 
            backgroundColor: theme.colors.card,
            borderColor: theme.colors.border 
          },
          isSelected && { borderColor: theme.colors.primary, borderWidth: 2 },
        ]}
        onPress={() => handleThemeSelect(themeInfo.name)}
      >
        <View style={styles.themePreview}>
          <View style={[styles.previewColor, { backgroundColor: previewData.color }]} />
          {isSelected && (
            <View style={styles.selectedBadge}>
              <Ionicons name="checkmark-circle" size={24} color={theme.colors.primary} />
            </View>
          )}
        </View>

        <View style={styles.themeInfo}>
          <Text style={[styles.themeName, { color: theme.colors.text }]}>
            {themeInfo.displayName}
          </Text>
          <Text style={[styles.themeDescription, { color: theme.colors.textSecondary }]}>
            {previewData.description}
          </Text>

          <View style={styles.featuresContainer}>
            {previewData.features.map((feature, index) => (
              <View
                key={index}
                style={[
                  styles.featureTag,
                  { backgroundColor: theme.colors.surface, borderColor: theme.colors.border },
                ]}
              >
                <Text style={[styles.featureText, { color: theme.colors.textSecondary }]}>
                  {feature}
                </Text>
              </View>
            ))}
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={[styles.header, { backgroundColor: theme.colors.surface, borderBottomColor: theme.colors.border }]}>
          <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
            选择主题风格
          </Text>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={theme.colors.text} />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.themeList} showsVerticalScrollIndicator={false}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text, marginHorizontal: 20, marginBottom: 16, marginTop: 20 }]}>
            选择新主题
          </Text>
          {availableThemes.map(renderThemeCard)}
        </ScrollView>

        <View style={[styles.footer, { backgroundColor: theme.colors.surface, borderTopColor: theme.colors.border }]}>
          <Text style={[styles.footerText, { color: theme.colors.textSecondary }]}>
            主题设置会自动保存，重启应用后依然有效
          </Text>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    paddingTop: 60,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
  },
  closeButton: {
    padding: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  themeList: {
    flex: 1,
    paddingHorizontal: 20,
  },
  themeCard: {
    borderRadius: 16,
    borderWidth: 1,
    marginBottom: 16,
    overflow: 'hidden',
  },
  themePreview: {
    height: 120,
    position: 'relative',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  previewColor: {
    width: '100%',
    height: '100%',
  },
  selectedBadge: {
    position: 'absolute',
    top: 12,
    right: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 12,
    padding: 4,
  },
  themeInfo: {
    padding: 16,
  },
  themeName: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  themeDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  featuresContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  featureTag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    borderWidth: 1,
    marginRight: 8,
    marginBottom: 4,
  },
  featureText: {
    fontSize: 12,
  },
  footer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
  },
  footerText: {
    fontSize: 12,
    textAlign: 'center',
    lineHeight: 18,
  },
});
