/**
 * 数据库调试脚本
 * 用于检查数据库中的数据和退休预警计算
 */

const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// 数据库文件路径（这是一个示例，实际路径可能不同）
const dbPath = path.join(__dirname, 'cadre_management.db');

console.log('🔍 开始检查数据库...');
console.log('数据库路径:', dbPath);

const db = new sqlite3.Database(':memory:', (err) => {
  if (err) {
    console.error('❌ 无法连接到数据库:', err.message);
    return;
  }
  console.log('✅ 已连接到内存数据库（用于测试）');
});

// 创建测试表
db.serialize(() => {
  // 创建干部信息表
  db.run(`CREATE TABLE IF NOT EXISTS cadre_info (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    序号 TEXT,
    单位 TEXT,
    姓名 TEXT NOT NULL,
    职务 TEXT,
    性别 TEXT,
    民族 TEXT,
    籍贯 TEXT,
    出生日期 TEXT,
    参加工作时间 TEXT,
    政治面貌 TEXT,
    入党时间 TEXT,
    全日制教育 TEXT,
    毕业院校系及专业 TEXT,
    在职教育 TEXT,
    毕业院校系及专业_在职 TEXT,
    专业技术职务 TEXT,
    现职级 TEXT,
    任现职时间 TEXT,
    任现职级时间 TEXT,
    工作简历 TEXT,
    身份证号 TEXT,
    身份证号码校验正误 TEXT,
    联系方式 TEXT,
    获得奖励荣誉情况 TEXT,
    党纪政纪处分情况 TEXT,
    备注 TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )`, (err) => {
    if (err) {
      console.error('❌ 创建表失败:', err.message);
      return;
    }
    console.log('✅ 表创建成功');
  });

  // 插入测试数据
  const testData = [
    ['王四顿', '男', '1964-03-15', '处长', '处级'],
    ['许国泰', '男', '1969-08-20', '科长', '科级'],
    ['李明华', '女', '1970-08-10', '副处长', '处级'],
    ['张建国', '男', '1965-04-01', '主任', '处级'],
    ['刘秀英', '女', '1975-12-25', '操作员', '工人'],
    ['赵六', '女', '1990-07-25', '科员', '科员'],
    ['钱七', '男', '1978-11-30', '副主任', '副处级']
  ];

  const stmt = db.prepare(`INSERT INTO cadre_info (姓名, 性别, 出生日期, 职务, 现职级) VALUES (?, ?, ?, ?, ?)`);
  
  testData.forEach(data => {
    stmt.run(data, (err) => {
      if (err) {
        console.error('❌ 插入数据失败:', err.message);
      }
    });
  });
  
  stmt.finalize();

  // 查询所有数据
  db.all("SELECT * FROM cadre_info", [], (err, rows) => {
    if (err) {
      console.error('❌ 查询失败:', err.message);
      return;
    }
    
    console.log(`\n📊 数据库中共有 ${rows.length} 条记录:`);
    rows.forEach((row, index) => {
      console.log(`${index + 1}. ${row.姓名} (性别:${row.性别}, 出生日期:${row.出生日期}, 职务:${row.职务})`);
    });

    // 模拟退休预警计算
    console.log('\n🔍 开始退休预警分析...');
    
    rows.forEach(row => {
      const result = calculateRetirementWarning(row);
      console.log(`👤 ${row.姓名}: ${result}`);
    });
  });
});

// 简化的退休预警计算函数
function calculateRetirementWarning(cadre) {
  if (!cadre.出生日期) {
    return '❌ 出生日期缺失';
  }

  const birthDate = new Date(cadre.出生日期);
  if (isNaN(birthDate.getTime())) {
    return '❌ 出生日期格式错误';
  }

  const today = new Date();
  const age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  
  let currentAge = age;
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    currentAge--;
  }

  // 简化的退休年龄计算
  let retirementAge = 60; // 默认男性
  if (cadre.性别 === '女') {
    const position = cadre.现职级 || cadre.职务 || '';
    retirementAge = position.includes('工人') || position.includes('操作') ? 50 : 55;
  }

  const yearsToRetirement = retirementAge - currentAge;

  if (yearsToRetirement <= 0) {
    return `🚨 已达到退休年龄 (当前${currentAge}岁)`;
  } else if (yearsToRetirement <= 2) {
    return `⚠️ 即将退休 (当前${currentAge}岁，${yearsToRetirement}年后退休)`;
  } else if (yearsToRetirement <= 5) {
    return `📋 退居二线预警 (当前${currentAge}岁，${yearsToRetirement}年后退休)`;
  } else {
    return `✅ 正常 (当前${currentAge}岁，${yearsToRetirement}年后退休)`;
  }
}

// 关闭数据库连接
setTimeout(() => {
  db.close((err) => {
    if (err) {
      console.error('❌ 关闭数据库失败:', err.message);
    } else {
      console.log('\n✅ 数据库连接已关闭');
    }
  });
}, 1000);
