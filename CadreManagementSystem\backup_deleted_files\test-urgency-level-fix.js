/**
 * 测试预警级别修复和出生日期显示
 */

console.log('🎯 测试预警级别修复和出生日期显示...\n');

// 模拟预警级别计算函数
function calculateUrgencyLevel(daysUntilRetirement) {
  if (daysUntilRetirement <= 0) {
    return 'info'; // 已退休
  } else if (daysUntilRetirement <= 90) { // 小于3个月
    return 'critical'; // 非常紧急
  } else if (daysUntilRetirement <= 180) { // 3-6个月
    return 'urgent'; // 紧急
  } else if (daysUntilRetirement <= 730) { // 6个月-2年
    return 'normal'; // 一般
  } else {
    return 'info'; // 信息
  }
}

// 获取预警级别颜色
function getUrgencyColor(level) {
  switch (level) {
    case 'critical':
      return '#FF0000'; // 非常紧急 - 深红色
    case 'urgent':
      return '#FF3B30'; // 紧急 - 红色
    case 'normal':
      return '#FF9500'; // 一般 - 橙色
    case 'info':
      return '#34C759'; // 信息 - 绿色
    default:
      return '#007AFF'; // 默认 - 蓝色
  }
}

// 获取预警级别文本
function getUrgencyText(level) {
  switch (level) {
    case 'critical':
      return '非常紧急';
    case 'urgent':
      return '紧急';
    case 'normal':
      return '一般';
    case 'info':
      return '信息';
    default:
      return '未知';
  }
}

// 格式化出生日期
function formatBirthDate(birthDateStr) {
  if (typeof birthDateStr === 'number') {
    try {
      const excelDate = new Date((birthDateStr - 25569) * 86400 * 1000);
      if (!isNaN(excelDate.getTime()) && excelDate.getFullYear() > 1900 && excelDate.getFullYear() < 2100) {
        const year = excelDate.getFullYear();
        const month = (excelDate.getMonth() + 1).toString().padStart(2, '0');
        const day = excelDate.getDate().toString().padStart(2, '0');
        return `${year}/${month}/${day}`;
      }
    } catch (e) {
      // 忽略错误
    }
  }
  return '未知';
}

// 测试不同剩余天数的预警级别
const urgencyTestCases = [
  { name: '吴小方（仅剩6天）', days: 6, expected: 'critical' },
  { name: '测试人员A（剩余60天）', days: 60, expected: 'critical' },
  { name: '测试人员B（剩余120天）', days: 120, expected: 'urgent' },
  { name: '测试人员C（剩余300天）', days: 300, expected: 'normal' },
  { name: '测试人员D（剩余600天）', days: 600, expected: 'normal' },
  { name: '韦树林（剩余444天）', days: 444, expected: 'normal' },
  { name: '王四顿（已退休）', days: -30, expected: 'info' }
];

console.log('📊 预警级别计算测试：\n');

urgencyTestCases.forEach((testCase, index) => {
  const calculatedLevel = calculateUrgencyLevel(testCase.days);
  const isCorrect = calculatedLevel === testCase.expected;
  
  console.log(`${index + 1}. ${testCase.name}:`);
  console.log(`   剩余天数: ${testCase.days}天`);
  console.log(`   预期级别: ${testCase.expected} (${getUrgencyText(testCase.expected)})`);
  console.log(`   计算级别: ${calculatedLevel} (${getUrgencyText(calculatedLevel)})`);
  console.log(`   颜色代码: ${getUrgencyColor(calculatedLevel)}`);
  console.log(`   结果: ${isCorrect ? '✅ 正确' : '❌ 错误'}`);
  console.log('');
});

// 测试完整的个人信息卡片显示
console.log('👥 完整个人信息卡片显示测试：\n');

const cardTestCases = [
  {
    name: '吴小方',
    birthDate: 25200, // Excel日期序列号
    unit: '晋圣三沟鑫都煤业',
    position: '副矿长（通风）',
    daysUntilRetirement: 6,
    warningType: 'second_line_warning'
  },
  {
    name: '韦树林',
    birthDate: 24368,
    unit: '晋圣公司',
    position: '纪委副书记兼信访案管室主任',
    daysUntilRetirement: 444,
    warningType: 'retirement_warning'
  },
  {
    name: '王四顿',
    birthDate: 23853,
    unit: '晋圣凤红煤业',
    position: '调研员',
    daysUntilRetirement: -30,
    warningType: 'retired'
  }
];

cardTestCases.forEach((person, index) => {
  const urgencyLevel = calculateUrgencyLevel(person.daysUntilRetirement);
  const formattedBirthDate = formatBirthDate(person.birthDate);
  const urgencyColor = getUrgencyColor(urgencyLevel);
  
  console.log(`${index + 1}. ${person.name}的个人信息卡片：`);
  console.log(`┌─────────────────────────────────────────────────┐`);
  console.log(`│ ${person.name} ${formattedBirthDate}     [${getUrgencyText(urgencyLevel)}] │`);
  console.log(`│ ${person.unit.padEnd(35)} │`);
  console.log(`│ ${person.position.padEnd(35)} │`);
  console.log(`│                                                 │`);
  console.log(`│ 预警信息内容...                              │`);
  console.log(`└─────────────────────────────────────────────────┘`);
  console.log(`   出生日期颜色: ${urgencyColor} (与预警级别一致)`);
  console.log('');
});

console.log('✅ 测试完成！');
console.log('\n🎨 修复总结：');
console.log('1. 📊 预警级别修复：');
console.log('   - 小于3个月（≤90天）：非常紧急 (critical) - 深红色');
console.log('   - 3-6个月（91-180天）：紧急 (urgent) - 红色');
console.log('   - 6个月-2年（181-730天）：一般 (normal) - 橙色');
console.log('   - 已退休或超过2年：信息 (info) - 绿色');

console.log('\n2. 🎯 出生日期显示：');
console.log('   - 位置：姓名后面，同一行显示');
console.log('   - 格式：YYYY/MM/DD');
console.log('   - 颜色：与预警级别颜色一致');
console.log('   - 支持Excel日期序列号解析');

console.log('\n3. 📱 显示效果：');
console.log('   - 吴小方 1969/01/15 [非常紧急] - 深红色');
console.log('   - 韦树林 1966/09/18 [一般] - 橙色');
console.log('   - 王四顿 1965/04/21 [信息] - 绿色');

console.log('\n🚀 现在应该能看到所有符合条件的人员，并且预警级别正确！');
