@echo off
echo 🔍 EXPO启动调试脚本
echo.

echo 📋 检查当前目录...
cd
echo.

echo 📋 检查package.json...
if exist package.json (
    echo ✅ package.json 存在
) else (
    echo ❌ package.json 不存在
    pause
    exit
)

echo.
echo 📋 检查node_modules...
if exist node_modules (
    echo ✅ node_modules 存在
) else (
    echo ❌ node_modules 不存在，正在安装依赖...
    npm install
)

echo.
echo 📋 检查EXPO CLI...
npx expo --version

echo.
echo 📋 启动EXPO开发服务器...
echo 请注意观察输出信息，如果出现错误请截图
echo.

npx expo start --clear

pause
