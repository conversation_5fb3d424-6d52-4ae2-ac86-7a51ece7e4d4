@echo off
echo 🧹 开始清理项目无用文件...
echo.

REM 创建备份目录
if not exist "backup_deleted_files" mkdir backup_deleted_files
echo ✅ 创建备份目录: backup_deleted_files

REM 移动测试脚本到备份目录
echo.
echo 📦 备份并删除测试脚本文件...
move test-*.js backup_deleted_files\ 2>nul
move test-*.ps1 backup_deleted_files\ 2>nul

REM 移动调试脚本到备份目录
echo 📦 备份并删除调试脚本文件...
move debug-*.js backup_deleted_files\ 2>nul
move analyze-*.js backup_deleted_files\ 2>nul
move check-*.js backup_deleted_files\ 2>nul
move verify-*.js backup_deleted_files\ 2>nul
move correct-*.js backup_deleted_files\ 2>nul
move final-*.js backup_deleted_files\ 2>nul
move query-*.js backup_deleted_files\ 2>nul

REM 移动多余的启动脚本
echo 📦 备份并删除多余的启动脚本...
move auto-start-expo.bat backup_deleted_files\ 2>nul
move start-expo-debug.bat backup_deleted_files\ 2>nul
move start-expo-lan.bat backup_deleted_files\ 2>nul
move start-simple.bat backup_deleted_files\ 2>nul
move debug-start.bat backup_deleted_files\ 2>nul
move clean-start.bat backup_deleted_files\ 2>nul

REM 移动临时文件
echo 📦 备份并删除临时文件...
move 0606.xlsx backup_deleted_files\ 2>nul
move *.backup backup_deleted_files\ 2>nul

REM 清理备份文件
echo 📦 清理组件备份文件...
if exist "src\components\*.backup" move src\components\*.backup backup_deleted_files\ 2>nul

echo.
echo ✅ 清理完成！
echo.
echo 📊 清理统计:
echo - 测试脚本: 已移动到备份目录
echo - 调试脚本: 已移动到备份目录  
echo - 启动脚本: 保留主要的，其他已备份
echo - 临时文件: 已移动到备份目录
echo.
echo 💡 提示:
echo - 所有文件已备份到 backup_deleted_files 目录
echo - 如需恢复某个文件，可从备份目录复制回来
echo - 确认项目正常运行后，可删除备份目录
echo.
echo 🚀 项目目录现在更加整洁！
pause
