import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  TextInput,
  Modal,
  Switch,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useTheme } from '../src/contexts/ThemeContext';
import { PositionLevelDao } from '../src/database/positionLevelDao';
import { PositionLevel } from '../src/types';

export default function PositionManagementScreen() {
  const { theme } = useTheme();
  const [positions, setPositions] = useState<PositionLevel[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingPosition, setEditingPosition] = useState<PositionLevel | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<'all' | 'administrative' | 'technical' | 'worker'>('all');
  const [showStatsModal, setShowStatsModal] = useState(false);
  const [positionStats, setPositionStats] = useState<Array<{现职级: string, count: number}>>([]);

  useEffect(() => {
    loadPositions();
  }, []);

  const loadPositions = async () => {
    try {
      setLoading(true);
      const data = await PositionLevelDao.getAllPositionLevels();
      setPositions(data);
    } catch (error) {
      console.error('加载职级数据失败:', error);
      Alert.alert('错误', '加载职级数据失败');
    } finally {
      setLoading(false);
    }
  };

  const handleAddPosition = () => {
    setEditingPosition({
      name: '',
      level: 1,
      category: 'administrative',
      retirement_age_male: 60,
      retirement_age_female: 55,
      description: '',
      is_active: true,
      sort_order: positions.length + 1
    });
    setShowAddModal(true);
  };

  const handleEditPosition = (position: PositionLevel) => {
    setEditingPosition(position);
    setShowAddModal(true);
  };

  const handleSavePosition = async () => {
    if (!editingPosition) return;

    if (!editingPosition.name.trim()) {
      Alert.alert('错误', '请输入职级名称');
      return;
    }

    try {
      if (editingPosition.id) {
        await PositionLevelDao.updatePositionLevel(editingPosition);
        Alert.alert('成功', '职级更新成功');
      } else {
        await PositionLevelDao.addPositionLevel(editingPosition);
        Alert.alert('成功', '职级添加成功');
      }
      
      setShowAddModal(false);
      setEditingPosition(null);
      loadPositions();
    } catch (error) {
      console.error('保存职级失败:', error);
      Alert.alert('错误', '保存职级失败');
    }
  };

  const handleDeletePosition = async (position: PositionLevel) => {
    try {
      // 先尝试普通删除
      await PositionLevelDao.deletePositionLevel(position.id!);
      Alert.alert('成功', '职级删除成功');
      loadPositions();
    } catch (error) {
      console.error('删除职级失败:', error);
      const errorMessage = error instanceof Error ? error.message : '删除职级失败';

      // 如果是因为正在使用而无法删除，提供更多选项
      if (errorMessage.includes('正在被') && errorMessage.includes('使用')) {
        Alert.alert(
          '职级正在使用中',
          errorMessage,
          [
            { text: '取消', style: 'cancel' },
            {
              text: '查看使用详情',
              onPress: () => showPositionUsageDetails(position.name)
            },
            {
              text: '批量替换',
              onPress: () => showBatchReplaceModal(position.name)
            },
            {
              text: '强制删除',
              style: 'destructive',
              onPress: () => confirmForceDelete(position)
            }
          ]
        );
      } else {
        Alert.alert('错误', errorMessage);
      }
    }
  };

  const showPositionUsageDetails = async (positionName: string) => {
    try {
      const usageDetails = await PositionLevelDao.getPositionUsageDetails(positionName);
      const detailText = usageDetails.map(u => `${u.姓名} (${u.单位})`).join('\n');
      Alert.alert(
        `职级"${positionName}"使用详情`,
        `共${usageDetails.length}人使用此职级：\n\n${detailText}`,
        [{ text: '确定' }]
      );
    } catch (error) {
      Alert.alert('错误', '获取使用详情失败');
    }
  };

  const showBatchReplaceModal = (oldPosition: string) => {
    Alert.prompt(
      '批量替换职级',
      `将所有使用"${oldPosition}"的干部职级替换为：`,
      [
        { text: '取消', style: 'cancel' },
        {
          text: '替换',
          onPress: async (newPosition) => {
            if (newPosition && newPosition.trim()) {
              try {
                const count = await PositionLevelDao.batchReplacePosition(oldPosition, newPosition.trim());
                Alert.alert('成功', `已成功替换${count}名干部的职级`);
                loadPositions();
              } catch (error) {
                Alert.alert('错误', '批量替换失败');
              }
            }
          }
        }
      ],
      'plain-text'
    );
  };

  const confirmForceDelete = (position: PositionLevel) => {
    Alert.alert(
      '强制删除确认',
      `强制删除职级"${position.name}"将会：\n\n1. 将所有使用此职级的干部职级设为空\n2. 删除此职级配置\n\n此操作不可撤销，确定继续吗？`,
      [
        { text: '取消', style: 'cancel' },
        {
          text: '强制删除',
          style: 'destructive',
          onPress: async () => {
            try {
              const affectedCount = await PositionLevelDao.forceDeletePositionLevel(position.id!);
              Alert.alert('成功', `职级已强制删除，影响${affectedCount}名干部`);
              loadPositions();
            } catch (error) {
              Alert.alert('错误', '强制删除失败');
            }
          }
        }
      ]
    );
  };

  const showCurrentPositionStats = async () => {
    try {
      const stats = await PositionLevelDao.getCurrentPositionStatistics();
      setPositionStats(stats);
      setShowStatsModal(true);
    } catch (error) {
      Alert.alert('错误', '获取职级统计失败');
    }
  };

  const filteredPositions = selectedCategory === 'all'
    ? positions 
    : positions.filter(p => p.category === selectedCategory);

  const getCategoryName = (category: string) => {
    switch (category) {
      case 'administrative': return '行政';
      case 'technical': return '技术';
      case 'worker': return '工人';
      default: return category;
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 16,
      backgroundColor: theme.colors.primary,
      paddingTop: 50,
    },
    backButton: {
      marginRight: 16,
    },
    headerTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: '#FFF',
      flex: 1,
    },
    headerButton: {
      padding: 4,
      marginRight: 12,
    },
    addButton: {
      padding: 8,
    },
    content: {
      flex: 1,
      padding: 16,
    },
    filterContainer: {
      flexDirection: 'row',
      marginBottom: 16,
    },
    filterButton: {
      flex: 1,
      paddingVertical: 8,
      paddingHorizontal: 12,
      marginHorizontal: 4,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: theme.colors.primary,
      alignItems: 'center',
    },
    filterButtonActive: {
      backgroundColor: theme.colors.primary,
    },
    filterButtonText: {
      fontSize: 14,
      color: theme.colors.primary,
    },
    filterButtonTextActive: {
      color: '#FFF',
    },
    positionItem: {
      backgroundColor: theme.colors.surface,
      borderRadius: 8,
      padding: 12,
      marginBottom: 8,
      elevation: 2,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    positionHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 8,
    },
    positionName: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
      flex: 1,
    },
    positionActions: {
      flexDirection: 'row',
    },
    actionButton: {
      padding: 4,
      marginLeft: 8,
    },
    positionDetails: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      lineHeight: 16,
    },
    categoryBadge: {
      paddingHorizontal: 8,
      paddingVertical: 2,
      borderRadius: 4,
      marginRight: 8,
    },
    categoryText: {
      fontSize: 10,
      color: '#FFF',
      fontWeight: '600',
    },
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    modalContent: {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 20,
      width: '90%',
      maxHeight: '80%',
    },
    modalTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 16,
      textAlign: 'center',
    },
    formGroup: {
      marginBottom: 16,
    },
    label: {
      fontSize: 14,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 8,
    },
    input: {
      borderWidth: 1,
      borderColor: '#E5E5EA',
      borderRadius: 8,
      paddingHorizontal: 12,
      paddingVertical: 10,
      fontSize: 16,
      color: theme.colors.text,
    },
    switchContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    modalActions: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: 20,
    },
    modalButton: {
      flex: 1,
      paddingVertical: 12,
      borderRadius: 8,
      alignItems: 'center',
      marginHorizontal: 8,
    },
    cancelButton: {
      backgroundColor: '#8E8E93',
    },
    saveButton: {
      backgroundColor: theme.colors.primary,
    },
    buttonText: {
      color: '#FFF',
      fontSize: 16,
      fontWeight: '600',
    },
    statItem: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: 8,
      borderBottomWidth: 1,
      borderBottomColor: '#F0F0F0',
    },
    statLabel: {
      fontSize: 14,
      color: theme.colors.text,
      flex: 1,
    },
    statValue: {
      fontSize: 14,
      fontWeight: '600',
      color: theme.colors.primary,
    },
    emptyText: {
      textAlign: 'center',
      color: theme.colors.textSecondary,
      fontSize: 14,
      padding: 20,
    },
  });

  return (
    <View style={styles.container}>
      {/* 头部 */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="arrow-back" size={24} color="#FFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>职级管理</Text>
        <TouchableOpacity
          style={styles.headerButton}
          onPress={showCurrentPositionStats}
        >
          <Ionicons name="stats-chart" size={20} color="#FFF" />
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.addButton}
          onPress={handleAddPosition}
        >
          <Ionicons name="add" size={24} color="#FFF" />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        {/* 分类筛选 */}
        <View style={styles.filterContainer}>
          {[
            { key: 'all', label: '全部' },
            { key: 'administrative', label: '行政' },
            { key: 'technical', label: '技术' },
            { key: 'worker', label: '工人' }
          ].map(filter => (
            <TouchableOpacity
              key={filter.key}
              style={[
                styles.filterButton,
                selectedCategory === filter.key && styles.filterButtonActive
              ]}
              onPress={() => setSelectedCategory(filter.key as any)}
            >
              <Text style={[
                styles.filterButtonText,
                selectedCategory === filter.key && styles.filterButtonTextActive
              ]}>
                {filter.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* 职级列表 */}
        {filteredPositions.map((position, index) => (
          <View key={position.id || index} style={styles.positionItem}>
            <View style={styles.positionHeader}>
              <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
                <View style={[
                  styles.categoryBadge,
                  { 
                    backgroundColor: position.category === 'administrative' ? '#007AFF' : 
                                   position.category === 'technical' ? '#34C759' : '#FF9500'
                  }
                ]}>
                  <Text style={styles.categoryText}>
                    {getCategoryName(position.category)}
                  </Text>
                </View>
                <Text style={styles.positionName}>{position.name}</Text>
              </View>
              <View style={styles.positionActions}>
                <TouchableOpacity 
                  style={styles.actionButton}
                  onPress={() => handleEditPosition(position)}
                >
                  <Ionicons name="create" size={16} color={theme.colors.primary} />
                </TouchableOpacity>
                <TouchableOpacity 
                  style={styles.actionButton}
                  onPress={() => handleDeletePosition(position)}
                >
                  <Ionicons name="trash" size={16} color="#FF3B30" />
                </TouchableOpacity>
              </View>
            </View>
            <Text style={styles.positionDetails}>
              等级：{position.level} | 男性退休：{position.retirement_age_male}岁 | 女性退休：{position.retirement_age_female}岁{'\n'}
              {position.description && `说明：${position.description}`}
            </Text>
          </View>
        ))}
      </ScrollView>

      {/* 添加/编辑职级模态框 */}
      <Modal
        visible={showAddModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowAddModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>
              {editingPosition?.id ? '编辑职级' : '添加职级'}
            </Text>
            
            <ScrollView>
              <View style={styles.formGroup}>
                <Text style={styles.label}>职级名称</Text>
                <TextInput
                  style={styles.input}
                  value={editingPosition?.name || ''}
                  onChangeText={(text) => setEditingPosition(prev => prev ? { ...prev, name: text } : null)}
                  placeholder="例如：正科级"
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.label}>职级等级</Text>
                <TextInput
                  style={styles.input}
                  value={editingPosition?.level?.toString() || ''}
                  onChangeText={(text) => setEditingPosition(prev => prev ? { ...prev, level: parseInt(text) || 1 } : null)}
                  keyboardType="numeric"
                  placeholder="数字越大级别越高"
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.label}>职级类别</Text>
                <View style={{ flexDirection: 'row', justifyContent: 'space-around' }}>
                  {[
                    { key: 'administrative', label: '行政' },
                    { key: 'technical', label: '技术' },
                    { key: 'worker', label: '工人' }
                  ].map(category => (
                    <TouchableOpacity
                      key={category.key}
                      style={[
                        styles.input,
                        { 
                          flex: 1, 
                          marginHorizontal: 4,
                          backgroundColor: editingPosition?.category === category.key ? theme.colors.primary : 'transparent'
                        }
                      ]}
                      onPress={() => setEditingPosition(prev => prev ? { ...prev, category: category.key as any } : null)}
                    >
                      <Text style={{ 
                        textAlign: 'center',
                        color: editingPosition?.category === category.key ? '#FFF' : theme.colors.text
                      }}>
                        {category.label}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.label}>男性退休年龄</Text>
                <TextInput
                  style={styles.input}
                  value={editingPosition?.retirement_age_male?.toString() || ''}
                  onChangeText={(text) => setEditingPosition(prev => prev ? { ...prev, retirement_age_male: parseInt(text) || undefined } : null)}
                  keyboardType="numeric"
                  placeholder="例如：60"
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.label}>女性退休年龄</Text>
                <TextInput
                  style={styles.input}
                  value={editingPosition?.retirement_age_female?.toString() || ''}
                  onChangeText={(text) => setEditingPosition(prev => prev ? { ...prev, retirement_age_female: parseInt(text) || undefined } : null)}
                  keyboardType="numeric"
                  placeholder="例如：55"
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.label}>职级描述</Text>
                <TextInput
                  style={[styles.input, { height: 80, textAlignVertical: 'top' }]}
                  value={editingPosition?.description || ''}
                  onChangeText={(text) => setEditingPosition(prev => prev ? { ...prev, description: text } : null)}
                  multiline
                  placeholder="输入职级描述..."
                />
              </View>

              <View style={styles.formGroup}>
                <View style={styles.switchContainer}>
                  <Text style={styles.label}>启用职级</Text>
                  <Switch
                    value={editingPosition?.is_active || false}
                    onValueChange={(value) => setEditingPosition(prev => prev ? { ...prev, is_active: value } : null)}
                  />
                </View>
              </View>
            </ScrollView>

            <View style={styles.modalActions}>
              <TouchableOpacity 
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => setShowAddModal(false)}
              >
                <Text style={styles.buttonText}>取消</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={[styles.modalButton, styles.saveButton]}
                onPress={handleSavePosition}
              >
                <Text style={styles.buttonText}>保存</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* 职级统计模态框 */}
      <Modal
        visible={showStatsModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowStatsModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>当前职级使用统计</Text>

            <ScrollView style={{ maxHeight: 400 }}>
              {positionStats.map((stat, index) => (
                <View key={index} style={styles.statItem}>
                  <Text style={styles.statLabel}>{stat.现职级}</Text>
                  <Text style={styles.statValue}>{stat.count} 人</Text>
                </View>
              ))}
              {positionStats.length === 0 && (
                <Text style={styles.emptyText}>暂无职级使用数据</Text>
              )}
            </ScrollView>

            <View style={styles.modalActions}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => setShowStatsModal(false)}
              >
                <Text style={styles.buttonText}>关闭</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}
