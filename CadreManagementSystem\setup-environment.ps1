# 智慧干部管理系统 - 环境配置脚本
# 此脚本将帮助您配置开发和构建环境

Write-Host "=== 智慧干部管理系统环境配置 ===" -ForegroundColor Green
Write-Host ""

# 检查Node.js是否已安装
Write-Host "检查Node.js安装状态..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version 2>$null
    if ($nodeVersion) {
        Write-Host "✅ Node.js已安装: $nodeVersion" -ForegroundColor Green
    } else {
        throw "Node.js未安装"
    }
} catch {
    Write-Host "❌ Node.js未安装" -ForegroundColor Red
    Write-Host "请按照以下步骤安装Node.js:" -ForegroundColor Yellow
    Write-Host "1. 访问 https://nodejs.org/zh-cn/download" -ForegroundColor Cyan
    Write-Host "2. 下载Windows Installer (.msi)" -ForegroundColor Cyan
    Write-Host "3. 运行安装程序，使用默认设置" -ForegroundColor Cyan
    Write-Host "4. 重启PowerShell后重新运行此脚本" -ForegroundColor Cyan
    Write-Host ""
    Read-Host "按Enter键打开Node.js下载页面"
    Start-Process "https://nodejs.org/zh-cn/download"
    exit 1
}

# 检查npm是否可用
Write-Host "检查npm..." -ForegroundColor Yellow
try {
    $npmVersion = npm --version 2>$null
    Write-Host "✅ npm已安装: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ npm不可用" -ForegroundColor Red
    exit 1
}

# 配置npm国内镜像源
Write-Host "配置npm国内镜像源..." -ForegroundColor Yellow
npm config set registry https://registry.npmmirror.com
npm config set disturl https://npmmirror.com/dist
npm config set electron_mirror https://npmmirror.com/mirrors/electron/
npm config set sass_binary_site https://npmmirror.com/mirrors/node-sass/
npm config set phantomjs_cdnurl https://npmmirror.com/mirrors/phantomjs/
Write-Host "✅ npm镜像源配置完成" -ForegroundColor Green

# 检查项目依赖
Write-Host "检查项目依赖..." -ForegroundColor Yellow
if (Test-Path "node_modules") {
    Write-Host "✅ 项目依赖已安装" -ForegroundColor Green
} else {
    Write-Host "安装项目依赖..." -ForegroundColor Yellow
    npm install
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ 项目依赖安装完成" -ForegroundColor Green
    } else {
        Write-Host "❌ 项目依赖安装失败" -ForegroundColor Red
        exit 1
    }
}

# 检查Expo CLI
Write-Host "检查Expo CLI..." -ForegroundColor Yellow
try {
    $expoVersion = npx expo --version 2>$null
    Write-Host "✅ Expo CLI可用: $expoVersion" -ForegroundColor Green
} catch {
    Write-Host "安装Expo CLI..." -ForegroundColor Yellow
    npm install -g @expo/cli
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Expo CLI安装完成" -ForegroundColor Green
    } else {
        Write-Host "❌ Expo CLI安装失败" -ForegroundColor Red
    }
}

# 检查Java环境
Write-Host "检查Java环境..." -ForegroundColor Yellow
try {
    $javaVersion = java -version 2>&1 | Select-String "version"
    if ($javaVersion) {
        Write-Host "✅ Java已安装: $javaVersion" -ForegroundColor Green
    } else {
        throw "Java未安装"
    }
} catch {
    Write-Host "⚠️ Java未安装，Android构建需要Java 11+" -ForegroundColor Yellow
    Write-Host "建议安装OpenJDK 11或更高版本" -ForegroundColor Cyan
}

# 检查Android SDK
Write-Host "检查Android SDK..." -ForegroundColor Yellow
$androidHome = $env:ANDROID_HOME
if ($androidHome -and (Test-Path $androidHome)) {
    Write-Host "✅ Android SDK已配置: $androidHome" -ForegroundColor Green
} else {
    Write-Host "⚠️ Android SDK未配置" -ForegroundColor Yellow
    Write-Host "请安装Android Studio并配置ANDROID_HOME环境变量" -ForegroundColor Cyan
}

Write-Host ""
Write-Host "=== 环境配置完成 ===" -ForegroundColor Green
Write-Host ""
Write-Host "下一步操作:" -ForegroundColor Yellow
Write-Host "1. 运行 'npm start' 启动开发服务器" -ForegroundColor Cyan
Write-Host "2. 运行 'npm run android' 构建Android应用" -ForegroundColor Cyan
Write-Host "3. 或运行 'npx expo build:android' 构建APK" -ForegroundColor Cyan
Write-Host ""

# 询问是否立即启动开发服务器
$startDev = Read-Host "是否立即启动开发服务器? (y/n)"
if ($startDev -eq "y" -or $startDev -eq "Y") {
    Write-Host "启动开发服务器..." -ForegroundColor Green
    npm start
}
