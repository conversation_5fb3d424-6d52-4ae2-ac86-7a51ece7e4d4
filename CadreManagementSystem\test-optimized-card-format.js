/**
 * 测试优化后的个人信息卡片格式
 */

console.log('🎨 测试优化后的个人信息卡片格式...\n');

// 模拟精确年龄计算
function calculatePreciseAge(birthDate) {
  const today = new Date();
  const ageInMilliseconds = today.getTime() - birthDate.getTime();
  const ageInYears = ageInMilliseconds / (365.25 * 24 * 60 * 60 * 1000);
  return Math.round(ageInYears * 10) / 10;
}

// 模拟计算距离指定年龄的剩余时间
function calculateTimeToAge(birthDate, targetAge) {
  const targetDate = new Date(birthDate);
  targetDate.setFullYear(birthDate.getFullYear() + targetAge);
  
  const today = new Date();
  const diffTime = targetDate.getTime() - today.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  const years = Math.floor(diffDays / 365);
  const months = Math.floor((diffDays % 365) / 30);
  
  return {
    years: Math.max(0, years),
    months: Math.max(0, months),
    days: Math.max(0, diffDays)
  };
}

// 解析Excel日期序列号
function parseBirthDate(birthDateStr) {
  if (typeof birthDateStr === 'number') {
    try {
      const excelDate = new Date((birthDateStr - 25569) * 86400 * 1000);
      if (!isNaN(excelDate.getTime()) && excelDate.getFullYear() > 1900 && excelDate.getFullYear() < 2100) {
        return excelDate;
      }
    } catch (e) {
      // 忽略错误
    }
  }
  return null;
}

// 测试数据
const testCases = [
  {
    name: '吴小方（退居二线预警）',
    cadre: {
      姓名: '吴小方',
      单位: '晋圣三沟鑫都煤业',
      职务: '副矿长（通风）',
      出生日期: 25200 // 约1969年，56岁左右
    },
    warningType: 'second_line_warning',
    urgencyLevel: 'normal',
    delayInfo: {
      delayYears: 0,
      delayMonths: 6,
      actualRetirementAge: 60.5
    },
    daysUntilRetirement: 1460
  },
  {
    name: '韦树林（退休预警）',
    cadre: {
      姓名: '韦树林',
      单位: '晋圣公司',
      职务: '纪委副书记兼信访案管室主任',
      出生日期: 24368 // 1966年，约58岁
    },
    warningType: 'retirement_warning',
    urgencyLevel: 'urgent',
    delayInfo: {
      delayYears: 0,
      delayMonths: 2,
      actualRetirementAge: 60.17
    },
    daysUntilRetirement: 229
  },
  {
    name: '王四顿（已退休）',
    cadre: {
      姓名: '王四顿',
      单位: '晋圣凤红煤业',
      职务: '调研员',
      出生日期: 23853 // 1965年，约60岁
    },
    warningType: 'retired',
    urgencyLevel: 'info',
    delayInfo: {
      delayYears: 0,
      delayMonths: 0,
      actualRetirementAge: 60
    },
    daysUntilRetirement: -30
  }
];

// 执行测试
testCases.forEach((testCase, index) => {
  console.log(`${index + 1}. ${testCase.name}:`);
  
  const birthDate = parseBirthDate(testCase.cadre.出生日期);
  if (birthDate) {
    const preciseAge = calculatePreciseAge(birthDate);
    const legalRetirementTime = calculateTimeToAge(birthDate, 60);
    const secondLineTime = calculateTimeToAge(birthDate, 58);
    
    console.log(`   📱 卡片显示格式:`);
    console.log(`   ┌─────────────────────────────────────────┐`);
    console.log(`   │ ${testCase.cadre.姓名.padEnd(20)} [${testCase.urgencyLevel === 'urgent' ? '紧急' : testCase.urgencyLevel === 'normal' ? '一般' : '信息'}] │`);
    console.log(`   │ ${testCase.cadre.单位.padEnd(30)} │`);
    console.log(`   │ ${testCase.cadre.职务.padEnd(30)} │`);
    console.log(`   │                                         │`);
    
    if (testCase.warningType === 'second_line_warning') {
      console.log(`   │ 距退居二线剩余${secondLineTime.years}年${secondLineTime.months}个月（剩余${secondLineTime.days}天）${' '.repeat(Math.max(0, 10 - secondLineTime.days.toString().length))} │`);
      console.log(`   │ 预计延迟退休年龄: ${testCase.delayInfo.actualRetirementAge.toFixed(1)}岁${' '.repeat(15)} │`);
      console.log(`   │ 延迟${testCase.delayInfo.delayYears}年${testCase.delayInfo.delayMonths}个月（剩余${testCase.daysUntilRetirement}天）${' '.repeat(Math.max(0, 12 - testCase.daysUntilRetirement.toString().length))} │`);
    } else if (testCase.warningType === 'retirement_warning') {
      console.log(`   │ 距法定退休年龄剩余${legalRetirementTime.years}年${legalRetirementTime.months}个月（剩余${legalRetirementTime.days}天）${' '.repeat(Math.max(0, 5 - legalRetirementTime.days.toString().length))} │`);
      console.log(`   │ 预计延迟退休年龄: ${testCase.delayInfo.actualRetirementAge.toFixed(1)}岁${' '.repeat(15)} │`);
      console.log(`   │ 延迟${testCase.delayInfo.delayYears}年${testCase.delayInfo.delayMonths}个月（剩余${testCase.daysUntilRetirement}天）${' '.repeat(Math.max(0, 15 - testCase.daysUntilRetirement.toString().length))} │`);
    } else {
      console.log(`   │ 已超过法定退休年龄${' '.repeat(20)} │`);
      console.log(`   │ 实际退休年龄: ${testCase.delayInfo.actualRetirementAge.toFixed(1)}岁${' '.repeat(18)} │`);
      console.log(`   │ 已超过退休年龄${Math.abs(testCase.daysUntilRetirement)}天${' '.repeat(18)} │`);
    }
    
    console.log(`   └─────────────────────────────────────────┘`);
  } else {
    console.log(`   ❌ 无法解析出生日期`);
  }
  
  console.log('');
});

console.log('✅ 测试完成！');
console.log('\n🎨 新格式特点：');
console.log('1. 姓名：18px，深色，加粗 - 最突出');
console.log('2. 单位：14px，中灰色 - 次要信息');
console.log('3. 职务：14px，浅灰色，中等加粗 - 补充信息');
console.log('4. 时间信息：13px，深灰色，中等加粗');
console.log('5. 退休年龄：13px，深色，加粗');
console.log('6. 延迟信息：13px，蓝色，加粗 - 重要提醒');

console.log('\n📐 布局特点：');
console.log('- 每行一个层次的信息');
console.log('- 合适的行间距（8px gap）');
console.log('- 预警级别标签右对齐');
console.log('- 信息区域与标签有适当间距');

console.log('\n🚀 现在可以测试应用了！');
