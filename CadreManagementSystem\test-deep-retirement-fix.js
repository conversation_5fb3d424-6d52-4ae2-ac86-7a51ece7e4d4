console.log('🔧 深度排查退休分类问题...\n');

// 测试李财茂的具体情况
const testCadre = {
  姓名: '李财茂',
  出生日期: '1965/06/03',
  性别: '男',
  现职级: '正处',
  单位: '晋圣公司提副总经理综合办公室副主任'
};

console.log(`📋 测试对象: ${testCadre.姓名}`);
console.log(`出生日期: ${testCadre.出生日期}`);
console.log(`性别: ${testCadre.性别}`);

// 解析出生日期
function parseBirthDate(dateStr) {
  if (!dateStr) return null;
  const cleanStr = dateStr.replace(/[年月日\-\.]/g, '/');
  const date = new Date(cleanStr);
  return isNaN(date.getTime()) ? null : date;
}

const birthDate = parseBirthDate(testCadre.出生日期);
if (!birthDate) {
  console.log('❌ 无法解析出生日期');
  process.exit(1);
}

const birthYear = birthDate.getFullYear();
console.log(`出生年份: ${birthYear}年`);

// 计算当前年龄
function calculateAge(birthDate) {
  const today = new Date();
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  
  return age;
}

const age = calculateAge(birthDate);
console.log(`当前年龄: ${age}岁`);

// 计算延迟退休
function calculateDelayRetirement(birthYear) {
  let totalDelayMonths = 3; // 基础延迟3个月
  
  if (birthYear >= 1966 && birthYear <= 1970) {
    const extraYears = birthYear - 1965;
    const extraMonths = Math.min(extraYears * 2, 12);
    totalDelayMonths += extraMonths;
  } else if (birthYear >= 1971 && birthYear <= 1980) {
    totalDelayMonths += 12;
    const extraYears = birthYear - 1970;
    const extraMonths = Math.min(extraYears * 3, 24);
    totalDelayMonths += extraMonths;
  } else if (birthYear >= 1981) {
    totalDelayMonths += 36;
    const extraYears = birthYear - 1980;
    const extraMonths = Math.min(extraYears * 4, 24);
    totalDelayMonths += extraMonths;
  }
  
  const maxDelayMonths = 60;
  totalDelayMonths = Math.min(totalDelayMonths, maxDelayMonths);
  
  return {
    totalDelayMonths,
    delayYears: Math.floor(totalDelayMonths / 12),
    delayMonths: totalDelayMonths % 12
  };
}

const delayInfo = calculateDelayRetirement(birthYear);
console.log(`\n⏰ 延迟退休计算:`);
console.log(`延迟月数: ${delayInfo.totalDelayMonths}个月`);

// 计算实际退休日期
const baseRetirementAge = testCadre.性别 === '女' ? 55 : 60;
const actualRetirementAge = baseRetirementAge + (delayInfo.totalDelayMonths / 12);

const legalRetirementDate = new Date(birthDate);
legalRetirementDate.setFullYear(birthDate.getFullYear() + baseRetirementAge);

const actualRetirementDate = new Date(legalRetirementDate);
actualRetirementDate.setMonth(actualRetirementDate.getMonth() + delayInfo.totalDelayMonths);

const today = new Date();
const daysUntilRetirement = Math.ceil((actualRetirementDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

console.log(`法定退休年龄: ${baseRetirementAge}岁`);
console.log(`实际退休年龄: ${actualRetirementAge.toFixed(1)}岁`);
console.log(`实际退休日期: ${actualRetirementDate.toLocaleDateString()}`);
console.log(`距离实际退休: ${daysUntilRetirement}天`);

// 模拟退休状态判断逻辑
console.log(`\n📊 退休状态判断:`);

// 1. 检查是否达到法定退休年龄
const reachedLegalAge = age >= baseRetirementAge;
console.log(`是否达到法定退休年龄: ${reachedLegalAge} (${age}岁 >= ${baseRetirementAge}岁)`);

// 2. 检查是否超过实际退休日期
const passedActualRetirement = daysUntilRetirement <= 0;
console.log(`是否超过实际退休日期: ${passedActualRetirement} (剩余${daysUntilRetirement}天)`);

// 3. 根据修复后的逻辑判断状态
console.log(`\n🎯 修复后的状态判断:`);
if (passedActualRetirement) {
  console.log(`✅ 应归类为: 已退休 (实际退休日期已过)`);
} else if (reachedLegalAge) {
  console.log(`⚠️ 应归类为: 已超过法定退休年龄，但未到实际退休 (在近两年退休列表)`);
} else {
  console.log(`📋 应归类为: 近两年退休预警`);
}

// 4. 检查修复前的错误逻辑
console.log(`\n❌ 修复前的错误逻辑:`);
console.log(`旧逻辑只检查: age >= baseRetirementAge = ${reachedLegalAge}`);
console.log(`旧逻辑会错误地将李财茂归类为: 已退休`);
console.log(`但实际上李财茂还有${daysUntilRetirement}天才到实际退休日期`);

console.log(`\n🔧 修复要点:`);
console.log(`1. matchRetiredRule方法必须检查daysUntilRetirement <= 0`);
console.log(`2. 只有实际退休日期已过才算"已退休"`);
console.log(`3. 达到法定退休年龄但未到实际退休日期的人员应在"近两年退休"列表`);

console.log(`\n📋 验证清单:`);
console.log(`□ 李财茂应该从"已退休"列表中移除`);
console.log(`□ 李财茂应该出现在"近两年退休"列表中`);
console.log(`□ 显示延迟3个月`);
console.log(`□ 显示距离实际退休${daysUntilRetirement}天`);
console.log(`□ 统计数据应该准确反映分类变化`);

// 检查其他1965年出生人员
console.log(`\n🔍 其他1965年出生人员检查:`);
const other1965Born = [
  { name: '王四顿', birthDate: '1965/04/21' },
  { name: '宋晨君', birthDate: '1965/05/01' },
  { name: '吴五雷', birthDate: '1965/06/04' }
];

other1965Born.forEach(person => {
  const personBirthDate = parseBirthDate(person.birthDate);
  if (personBirthDate) {
    const personActualRetirementDate = new Date(personBirthDate);
    personActualRetirementDate.setFullYear(personBirthDate.getFullYear() + 60);
    personActualRetirementDate.setMonth(personActualRetirementDate.getMonth() + 3); // 延迟3个月
    
    const personDaysUntilRetirement = Math.ceil((personActualRetirementDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    
    console.log(`${person.name}: 距离实际退休${personDaysUntilRetirement}天 - ${personDaysUntilRetirement <= 0 ? '已退休' : '近两年退休'}`);
  }
});

console.log(`\n🎉 深度排查完成！`);
console.log(`\n💡 关键修复点:`);
console.log(`确保configurableRetirementCalculator.ts中的matchRetiredRule方法:`);
console.log(`- 接收daysUntilRetirement参数`);
console.log(`- 只有当daysUntilRetirement <= 0时才返回已退休规则`);
console.log(`- 删除旧版本的只检查年龄的方法`);

console.log('\n🔧 深度退休分类问题排查完成！');
