import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Alert
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useTheme } from '../../src/contexts/ThemeContext';
import { ThemePicker } from '../../src/components/ThemePicker';

export default function SettingsScreen() {
  const { theme } = useTheme();
  const [showThemePicker, setShowThemePicker] = useState(false);

  const settingsItems = [
    {
      title: '主题设置',
      subtitle: '选择应用主题风格',
      icon: 'color-palette',
      onPress: () => setShowThemePicker(true)
    },
    {
      title: '职级管理',
      subtitle: '管理企业职级体系和分类',
      icon: 'business',
      onPress: () => router.push('/position-management')
    },
    {
      title: '退休规则设置',
      subtitle: '配置退休预警、退居二线和延迟退休规则',
      icon: 'time',
      onPress: () => router.push('/retirement-settings')
    },
    {
      title: 'Excel导入',
      subtitle: '批量导入干部信息',
      icon: 'document',
      onPress: () => router.push('/import-excel')
    },
    {
      title: '数据管理',
      subtitle: '数据备份、清空和安全管理',
      icon: 'server',
      onPress: () => router.push('/data-management')
    },
    {
      title: '关于应用',
      subtitle: '版本信息和帮助',
      icon: 'information-circle',
      onPress: () => showAbout()
    }
  ];

  const showAbout = () => {
    Alert.alert(
      '关于应用',
      '智慧干部信息管理系统\n版本: 2.0.0\n\n专为国有企业干部管理设计的智能化系统\n\n✨ 核心功能：\n• Excel数据批量导入\n• 延迟退休年龄计算\n• 退休预警智能提醒\n• 多主题界面切换\n• 职级分类管理\n• 数据安全保护\n\n🎯 适用场景：\n• 国有企业人事管理\n• 干部信息统计分析\n• 退休规划和预警\n• 人力资源数据管理',
      [{ text: '确定' }]
    );
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={[styles.header, { backgroundColor: theme.colors.primary }]}>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>设置</Text>
        <Text style={[styles.headerSubtitle, { color: theme.colors.text }]}>系统配置和管理</Text>
      </View>

      <View style={styles.settingsContainer}>
        {settingsItems.map((item, index) => (
          <TouchableOpacity
            key={index}
            style={[styles.settingItem, { backgroundColor: theme.colors.card }]}
            onPress={item.onPress}
          >
            <View style={[styles.settingIcon, { backgroundColor: theme.colors.surface }]}>
              <Ionicons name={item.icon as any} size={24} color={theme.colors.primary} />
            </View>
            <View style={styles.settingContent}>
              <Text style={[styles.settingTitle, { color: theme.colors.text }]}>{item.title}</Text>
              <Text style={[styles.settingSubtitle, { color: theme.colors.textSecondary }]}>{item.subtitle}</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
          </TouchableOpacity>
        ))}
      </View>

      <View style={styles.footer}>
        <Text style={[styles.footerText, { color: theme.colors.textSecondary }]}>
          智慧干部信息管理系统 v1.0.0
        </Text>
        <Text style={[styles.footerSubtext, { color: theme.colors.textSecondary }]}>
          专为国有企业干部管理设计
        </Text>
      </View>

      <ThemePicker
        visible={showThemePicker}
        onClose={() => setShowThemePicker(false)}
      />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7'
  },
  header: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 30,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: '700',
    color: '#FFF',
    marginBottom: 8
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#E3F2FD',
    opacity: 0.9
  },
  settingsContainer: {
    paddingHorizontal: 20,
    marginTop: 20
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F0F8FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16
  },
  settingContent: {
    flex: 1
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000',
    marginBottom: 4
  },
  settingSubtitle: {
    fontSize: 14,
    color: '#666'
  },
  footer: {
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20
  },
  footerText: {
    fontSize: 14,
    color: '#8E8E93',
    marginBottom: 4
  },
  footerSubtext: {
    fontSize: 12,
    color: '#8E8E93'
  }
});
