console.log('🔧 测试女性退休年龄计算修复...\n');

// 模拟申丽丽的计算过程
function simulateRetirementCalculation(name, birthDate, gender) {
  console.log(`👤 ${name} 退休计算：`);
  console.log(`出生日期: ${birthDate.toLocaleDateString()}`);
  console.log(`性别: ${gender}`);
  
  const today = new Date();
  const age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  const dayDiff = today.getDate() - birthDate.getDate();
  
  let exactAge = age;
  if (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)) {
    exactAge--;
  }
  
  console.log(`当前年龄: ${exactAge}岁`);
  
  // 根据性别确定基础退休年龄
  const baseRetirementAge = gender === '女' ? 55 : 60;
  console.log(`基础退休年龄: ${baseRetirementAge}岁`);
  
  // 计算延迟退休（1971年出生）
  const birthYear = birthDate.getFullYear();
  let totalDelayMonths = 0;
  
  if (birthYear <= 1965) {
    totalDelayMonths = 3;
  } else {
    const baseDelayMonths = 3;
    const yearsAfter1965 = birthYear - 1965;
    let delayMonthsPerYear = 0;
    let maxDelayMonths = 0;
    
    if (birthYear >= 1966 && birthYear <= 1970) {
      delayMonthsPerYear = 2;
      maxDelayMonths = 12;
    } else if (birthYear >= 1971 && birthYear <= 1980) {
      delayMonthsPerYear = 3;
      maxDelayMonths = 36;
    } else if (birthYear >= 1981) {
      delayMonthsPerYear = 4;
      maxDelayMonths = 60;
    }
    
    const additionalDelayMonths = Math.min(
      yearsAfter1965 * delayMonthsPerYear,
      maxDelayMonths - baseDelayMonths
    );
    totalDelayMonths = baseDelayMonths + additionalDelayMonths;
  }
  
  const delayYears = Math.floor(totalDelayMonths / 12);
  const delayMonths = totalDelayMonths % 12;
  const actualRetirementAge = baseRetirementAge + (totalDelayMonths / 12);
  
  console.log(`延迟时间: ${delayYears}年${delayMonths}个月`);
  console.log(`实际退休年龄: ${actualRetirementAge}岁`);
  
  // 计算退休日期
  const retirementDate = new Date(birthDate);
  retirementDate.setFullYear(birthDate.getFullYear() + Math.floor(actualRetirementAge));
  const delayMonthsForDate = Math.round((actualRetirementAge - Math.floor(actualRetirementAge)) * 12);
  retirementDate.setMonth(retirementDate.getMonth() + delayMonthsForDate);
  
  console.log(`退休日期: ${retirementDate.toLocaleDateString()}`);
  
  // 计算剩余天数
  const diffTime = retirementDate.getTime() - today.getTime();
  const daysUntilRetirement = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  console.log(`剩余天数: ${daysUntilRetirement}天`);
  
  return {
    baseRetirementAge,
    delayYears,
    delayMonths,
    actualRetirementAge,
    retirementDate,
    daysUntilRetirement
  };
}

console.log('═'.repeat(60));
console.log('测试申丽丽的退休计算：');
console.log('═'.repeat(60));

const shenLili = simulateRetirementCalculation('申丽丽', new Date('1971-01-01'), '女');

console.log('\n═'.repeat(60));
console.log('对比分析：');
console.log('═'.repeat(60));

console.log(`用户期望剩余天数: 334天`);
console.log(`当前计算剩余天数: ${shenLili.daysUntilRetirement}天`);
console.log(`系统显示剩余天数: 835天`);

console.log('\n🔍 问题分析：');

if (Math.abs(shenLili.daysUntilRetirement - 334) < 100) {
  console.log('✅ 计算结果与用户期望较为接近');
} else {
  console.log('❌ 计算结果与用户期望差距较大');
}

if (Math.abs(shenLili.daysUntilRetirement - 835) < 100) {
  console.log('❌ 计算结果与系统显示接近，说明系统可能还在使用错误逻辑');
} else {
  console.log('✅ 计算结果与系统显示差距大，说明修复可能有效');
}

console.log('\n📋 修复验证：');
console.log(`1. 基础退休年龄: ${shenLili.baseRetirementAge}岁 ${shenLili.baseRetirementAge === 55 ? '✅' : '❌'}`);
console.log(`2. 延迟时间: ${shenLili.delayYears}年${shenLili.delayMonths}个月 ${shenLili.delayYears === 1 && shenLili.delayMonths === 9 ? '✅' : '❌'}`);
console.log(`3. 实际退休年龄: ${shenLili.actualRetirementAge}岁`);
console.log(`4. 退休日期: ${shenLili.retirementDate.toLocaleDateString()}`);

console.log('\n🎯 可能的问题：');
console.log('1. 如果系统仍显示835天，可能是：');
console.log('   - 前端缓存问题，需要刷新');
console.log('   - 数据库中申丽丽的性别信息不正确');
console.log('   - 还有其他地方使用了错误的退休年龄');

console.log('\n2. 如果用户期望334天与计算结果差距大，可能是：');
console.log('   - 延迟退休政策理解有差异');
console.log('   - 计算起始日期不同');
console.log('   - 其他计算细节需要调整');

console.log('\n🔧 建议检查步骤：');
console.log('1. 在雷电模拟器中刷新应用');
console.log('2. 检查申丽丽的性别信息是否为"女"');
console.log('3. 查看退休预警页面的具体显示');
console.log('4. 确认延迟退休政策的具体实施细节');

console.log('\n✅ 女性退休年龄计算修复测试完成！');
