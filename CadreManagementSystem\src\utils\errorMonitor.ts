/**
 * 错误监控和自动修复系统
 * 监控应用运行时错误，并提供自动修复建议
 */

export interface ErrorReport {
  id: string;
  timestamp: Date;
  type: 'database' | 'calculation' | 'import' | 'ui' | 'validation';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  stack?: string;
  context?: any;
  autoFixed?: boolean;
  fixSuggestion?: string;
}

export class ErrorMonitor {
  private static errors: ErrorReport[] = [];
  private static listeners: ((error: ErrorReport) => void)[] = [];

  /**
   * 记录错误
   */
  static logError(
    type: ErrorReport['type'],
    severity: ErrorReport['severity'],
    message: string,
    context?: any,
    stack?: string
  ): string {
    const error: ErrorReport = {
      id: this.generateId(),
      timestamp: new Date(),
      type,
      severity,
      message,
      stack,
      context,
      autoFixed: false
    };

    // 尝试自动修复
    this.attemptAutoFix(error);

    // 记录错误
    this.errors.push(error);
    
    // 通知监听器
    this.listeners.forEach(listener => listener(error));

    // 控制台输出
    const emoji = this.getSeverityEmoji(severity);
    console.log(`${emoji} [${type.toUpperCase()}] ${message}`);
    if (context) {
      console.log('📋 上下文:', context);
    }
    if (error.autoFixed) {
      console.log('🔧 已自动修复');
    } else if (error.fixSuggestion) {
      console.log('💡 修复建议:', error.fixSuggestion);
    }

    return error.id;
  }

  /**
   * 尝试自动修复错误
   */
  private static attemptAutoFix(error: ErrorReport): void {
    switch (error.type) {
      case 'database':
        this.fixDatabaseError(error);
        break;
      case 'calculation':
        this.fixCalculationError(error);
        break;
      case 'import':
        this.fixImportError(error);
        break;
      case 'validation':
        this.fixValidationError(error);
        break;
      case 'ui':
        this.fixUIError(error);
        break;
    }
  }

  /**
   * 修复数据库错误
   */
  private static fixDatabaseError(error: ErrorReport): void {
    if (error.message.includes('no such table')) {
      error.fixSuggestion = '数据库表不存在，建议重新初始化数据库';
    } else if (error.message.includes('database is locked')) {
      error.fixSuggestion = '数据库被锁定，建议重启应用或等待操作完成';
    } else if (error.message.includes('UNIQUE constraint failed')) {
      error.fixSuggestion = '数据重复，建议检查身份证号或其他唯一字段';
      error.autoFixed = true; // 可以通过更新操作自动修复
    } else {
      error.fixSuggestion = '数据库操作失败，建议检查SQL语句和数据完整性';
    }
  }

  /**
   * 修复计算错误
   */
  private static fixCalculationError(error: ErrorReport): void {
    if (error.message.includes('出生日期')) {
      error.fixSuggestion = '出生日期格式错误，建议使用YYYY-MM-DD格式';
    } else if (error.message.includes('延迟退休')) {
      error.fixSuggestion = '延迟退休计算错误，建议检查出生年份和计算逻辑';
    } else if (error.message.includes('年龄')) {
      error.fixSuggestion = '年龄计算错误，建议检查当前日期和出生日期';
    } else {
      error.fixSuggestion = '计算逻辑错误，建议检查输入数据和算法实现';
    }
  }

  /**
   * 修复导入错误
   */
  private static fixImportError(error: ErrorReport): void {
    if (error.message.includes('Excel')) {
      error.fixSuggestion = 'Excel文件读取失败，建议检查文件格式和权限';
    } else if (error.message.includes('字段映射')) {
      error.fixSuggestion = '字段映射错误，建议检查Excel表头和字段对应关系';
    } else if (error.message.includes('数据验证')) {
      error.fixSuggestion = '数据验证失败，建议检查必填字段和数据格式';
    } else {
      error.fixSuggestion = '数据导入失败，建议检查数据源和导入逻辑';
    }
  }

  /**
   * 修复验证错误
   */
  private static fixValidationError(error: ErrorReport): void {
    if (error.message.includes('姓名')) {
      error.fixSuggestion = '姓名不能为空，请填写有效的姓名';
      error.autoFixed = true;
    } else if (error.message.includes('身份证')) {
      error.fixSuggestion = '身份证号格式错误，请检查长度和校验位';
    } else if (error.message.includes('性别')) {
      error.fixSuggestion = '性别字段错误，请使用"男"或"女"';
      error.autoFixed = true;
    } else {
      error.fixSuggestion = '数据验证失败，请检查输入格式和必填字段';
    }
  }

  /**
   * 修复UI错误
   */
  private static fixUIError(error: ErrorReport): void {
    if (error.message.includes('render')) {
      error.fixSuggestion = '组件渲染错误，建议检查props和state';
    } else if (error.message.includes('navigation')) {
      error.fixSuggestion = '导航错误，建议检查路由配置和参数';
    } else {
      error.fixSuggestion = 'UI错误，建议检查组件逻辑和数据绑定';
    }
  }

  /**
   * 获取严重程度表情符号
   */
  private static getSeverityEmoji(severity: ErrorReport['severity']): string {
    switch (severity) {
      case 'low': return '💡';
      case 'medium': return '⚠️';
      case 'high': return '🚨';
      case 'critical': return '🔥';
      default: return '❓';
    }
  }

  /**
   * 生成错误ID
   */
  private static generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  /**
   * 获取所有错误
   */
  static getAllErrors(): ErrorReport[] {
    return [...this.errors];
  }

  /**
   * 获取特定类型的错误
   */
  static getErrorsByType(type: ErrorReport['type']): ErrorReport[] {
    return this.errors.filter(error => error.type === type);
  }

  /**
   * 获取特定严重程度的错误
   */
  static getErrorsBySeverity(severity: ErrorReport['severity']): ErrorReport[] {
    return this.errors.filter(error => error.severity === severity);
  }

  /**
   * 清除所有错误
   */
  static clearErrors(): void {
    this.errors = [];
  }

  /**
   * 添加错误监听器
   */
  static addListener(listener: (error: ErrorReport) => void): void {
    this.listeners.push(listener);
  }

  /**
   * 移除错误监听器
   */
  static removeListener(listener: (error: ErrorReport) => void): void {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  /**
   * 生成错误报告
   */
  static generateReport(): string {
    const totalErrors = this.errors.length;
    const criticalErrors = this.getErrorsBySeverity('critical').length;
    const highErrors = this.getErrorsBySeverity('high').length;
    const autoFixedErrors = this.errors.filter(e => e.autoFixed).length;

    let report = '📊 错误监控报告\n';
    report += '='.repeat(30) + '\n';
    report += `总错误数: ${totalErrors}\n`;
    report += `严重错误: ${criticalErrors}\n`;
    report += `高级错误: ${highErrors}\n`;
    report += `自动修复: ${autoFixedErrors}\n`;
    report += `修复率: ${totalErrors > 0 ? (autoFixedErrors / totalErrors * 100).toFixed(1) : 0}%\n\n`;

    if (this.errors.length > 0) {
      report += '最近错误:\n';
      this.errors.slice(-5).forEach((error, index) => {
        report += `${index + 1}. [${error.type}] ${error.message}\n`;
        if (error.fixSuggestion) {
          report += `   💡 ${error.fixSuggestion}\n`;
        }
      });
    }

    return report;
  }

  /**
   * 系统健康检查
   */
  static performHealthCheck(): {
    status: 'healthy' | 'warning' | 'critical';
    issues: string[];
    suggestions: string[];
  } {
    const issues: string[] = [];
    const suggestions: string[] = [];

    // 检查错误数量
    const recentErrors = this.errors.filter(e =>
      Date.now() - e.timestamp.getTime() < 24 * 60 * 60 * 1000 // 24小时内
    );

    if (recentErrors.length > 10) {
      issues.push('24小时内错误数量过多');
      suggestions.push('建议检查系统稳定性和错误根因');
    }

    // 检查严重错误
    const criticalErrors = this.getErrorsBySeverity('critical');
    if (criticalErrors.length > 0) {
      issues.push(`存在${criticalErrors.length}个严重错误`);
      suggestions.push('立即处理严重错误，确保系统正常运行');
    }

    // 检查自动修复率
    const autoFixRate = this.errors.length > 0 ?
      this.errors.filter(e => e.autoFixed).length / this.errors.length : 1;

    if (autoFixRate < 0.5) {
      issues.push('自动修复率较低');
      suggestions.push('优化错误处理逻辑，提高自动修复能力');
    }

    // 确定系统状态
    let status: 'healthy' | 'warning' | 'critical' = 'healthy';
    if (criticalErrors.length > 0) {
      status = 'critical';
    } else if (issues.length > 0) {
      status = 'warning';
    }

    return { status, issues, suggestions };
  }
}
