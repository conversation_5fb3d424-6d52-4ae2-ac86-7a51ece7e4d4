console.log('🔍 检查数据库结构...\n');

const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// 数据库路径
const dbPath = path.join(__dirname, 'cadre_management.db');

// 连接数据库
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ 连接数据库失败:', err.message);
    return;
  }
  console.log('✅ 成功连接到数据库');
});

// 查看所有表
db.all(`
  SELECT name FROM sqlite_master WHERE type='table'
`, [], (err, tables) => {
  if (err) {
    console.error('❌ 查询表失败:', err.message);
    return;
  }
  
  console.log(`📋 数据库中的表：`);
  tables.forEach(table => {
    console.log(`- ${table.name}`);
  });
  
  // 如果有cadre_info表，查询申丽丽
  if (tables.some(t => t.name === 'cadre_info')) {
    console.log('\n🔍 在cadre_info表中查找申丽丽...');
    
    db.get(`
      SELECT * FROM cadre_info WHERE 姓名 = '申丽丽'
    `, [], (err, row) => {
      if (err) {
        console.error('❌ 查询申丽丽信息失败:', err.message);
        return;
      }
      
      if (row) {
        console.log(`📋 申丽丽的真实数据库信息：`);
        console.log(`姓名: ${row.姓名}`);
        console.log(`性别: ${row.性别}`);
        console.log(`出生日期: ${row.出生日期}`);
        console.log(`现职级: ${row.现职级}`);
        console.log(`职务: ${row.职务}`);
        console.log(`单位: ${row.单位}`);
        console.log(`身份证号: ${row.身份证号}`);
        
        // 分析出生日期
        const birthDateStr = row.出生日期;
        console.log(`\n📅 出生日期分析：`);
        console.log(`原始格式: ${birthDateStr}`);
        
        if (birthDateStr) {
          // 尝试解析日期
          let birthDate;
          const cleanStr = birthDateStr.replace(/[年月日\-\.]/g, '/');
          birthDate = new Date(cleanStr);
          
          if (!isNaN(birthDate.getTime())) {
            const birthYear = birthDate.getFullYear();
            console.log(`解析结果: ${birthDate.toLocaleDateString()}`);
            console.log(`出生年份: ${birthYear}年`);
            
            // 计算当前年龄
            const today = new Date();
            let age = today.getFullYear() - birthDate.getFullYear();
            const monthDiff = today.getMonth() - birthDate.getMonth();
            
            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
              age--;
            }
            
            console.log(`当前年龄: ${age}岁`);
            console.log(`性别: ${row.性别}`);
            
            // 根据真实数据重新计算延迟退休
            function calculateDelayRetirement(birthYear) {
              let totalDelayMonths = 3; // 基础延迟3个月
              
              if (birthYear >= 1966 && birthYear <= 1970) {
                const extraYears = birthYear - 1965;
                const extraMonths = Math.min(extraYears * 2, 12);
                totalDelayMonths += extraMonths;
              } else if (birthYear >= 1971 && birthYear <= 1980) {
                totalDelayMonths += 12; // 先加满1年
                const extraYears = birthYear - 1970;
                const extraMonths = Math.min(extraYears * 3, 24);
                totalDelayMonths += extraMonths;
              } else if (birthYear >= 1981) {
                totalDelayMonths += 36; // 先加满3年
                const extraYears = birthYear - 1980;
                const extraMonths = Math.min(extraYears * 4, 24);
                totalDelayMonths += extraMonths;
              }
              
              const maxDelayMonths = 60;
              totalDelayMonths = Math.min(totalDelayMonths, maxDelayMonths);
              
              return {
                totalDelayMonths,
                delayYears: Math.floor(totalDelayMonths / 12),
                delayMonths: totalDelayMonths % 12
              };
            }
            
            const delayInfo = calculateDelayRetirement(birthYear);
            console.log(`\n⏰ 延迟退休计算：`);
            console.log(`出生年份: ${birthYear}年`);
            
            if (birthYear <= 1965) {
              console.log(`适用规则: 1965年及以前（基础延迟3个月）`);
            } else if (birthYear >= 1966 && birthYear <= 1970) {
              console.log(`适用规则: 1966-1970年（基础3个月 + 每年2个月）`);
              console.log(`计算过程: 基础3个月 + (${birthYear} - 1965) × 2个月 = 3 + ${(birthYear - 1965) * 2} = ${delayInfo.totalDelayMonths}个月`);
            } else if (birthYear >= 1971 && birthYear <= 1980) {
              console.log(`适用规则: 1971-1980年（基础3个月 + 1年 + 每年3个月）`);
              console.log(`计算过程: 基础3个月 + 12个月 + (${birthYear} - 1970) × 3个月 = 15 + ${(birthYear - 1970) * 3} = ${delayInfo.totalDelayMonths}个月`);
            }
            
            console.log(`延迟月数: ${delayInfo.totalDelayMonths}个月`);
            
            // 计算退休日期
            const baseRetirementAge = row.性别 === '女' ? 55 : 60;
            const actualRetirementAge = baseRetirementAge + (delayInfo.totalDelayMonths / 12);
            
            const legalRetirementDate = new Date(birthDate);
            legalRetirementDate.setFullYear(birthDate.getFullYear() + baseRetirementAge);
            
            const actualRetirementDate = new Date(legalRetirementDate);
            actualRetirementDate.setMonth(actualRetirementDate.getMonth() + delayInfo.totalDelayMonths);
            
            const daysToActual = Math.ceil((actualRetirementDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
            
            console.log(`\n🎯 退休计算结果：`);
            console.log(`法定退休年龄: ${baseRetirementAge}岁`);
            console.log(`实际退休年龄: ${actualRetirementAge.toFixed(2)}岁`);
            console.log(`实际退休日期: ${actualRetirementDate.toLocaleDateString()}`);
            console.log(`距离实际退休: ${daysToActual}天`);
            
            console.log(`\n📊 正确的状态判断：`);
            if (age < baseRetirementAge) {
              console.log(`✅ 未到法定退休年龄（${age}岁 < ${baseRetirementAge}岁）`);
              if (daysToActual > 0) {
                console.log(`✅ 未到实际退休日期（还剩${daysToActual}天）`);
                console.log(`📋 应归类为: 近两年退休预警`);
              } else {
                console.log(`❌ 已超过实际退休日期（超过${Math.abs(daysToActual)}天）`);
                console.log(`📋 应归类为: 已退休`);
              }
            } else {
              console.log(`⚠️ 已达到法定退休年龄（${age}岁 >= ${baseRetirementAge}岁）`);
              if (daysToActual > 0) {
                console.log(`✅ 但未到实际退休日期（还剩${daysToActual}天）`);
                console.log(`📋 应归类为: 已超过法定退休年龄，但未到实际退休`);
              } else {
                console.log(`❌ 已超过实际退休日期（超过${Math.abs(daysToActual)}天）`);
                console.log(`📋 应归类为: 已退休`);
              }
            }
          } else {
            console.log('❌ 无法解析出生日期格式');
          }
        } else {
          console.log('❌ 出生日期为空');
        }
        
      } else {
        console.log('❌ 数据库中未找到申丽丽的信息');
        
        // 查看所有女性干部
        console.log('\n🔍 查看所有女性干部：');
        db.all(`
          SELECT 姓名, 性别, 出生日期 FROM cadre_info WHERE 性别 = '女' LIMIT 10
        `, [], (err, rows) => {
          if (err) {
            console.error('❌ 查询女性干部失败:', err.message);
            return;
          }
          
          console.log('姓名     | 性别 | 出生日期');
          console.log('-'.repeat(30));
          rows.forEach(row => {
            console.log(`${row.姓名.padEnd(8)} | ${row.性别.padEnd(2)} | ${row.出生日期 || '无'}`);
          });
          
          // 关闭数据库连接
          db.close();
        });
      }
    });
  } else {
    console.log('❌ 未找到cadre_info表');
    db.close();
  }
});

console.log('🔍 数据库结构检查完成！');
