@echo off
echo === EXPO局域网模式启动 ===
echo.

REM 设置Node.js路径
set PATH=C:\Program Files\nodejs;%PATH%

REM 切换到项目目录
cd /d "%~dp0"

echo 当前目录: %CD%
echo.

REM 获取本机IP地址
echo 正在获取本机IP地址...
for /f "tokens=14" %%a in ('ipconfig ^| findstr /i "IPv4"') do (
    set LOCAL_IP=%%a
    goto :found_ip
)
:found_ip

if "%LOCAL_IP%"=="" (
    echo 无法自动获取IP地址，使用默认配置
    set LOCAL_IP=*************
)

echo 本机IP地址: %LOCAL_IP%
echo.

echo === 启动EXPO局域网模式 ===
echo 端口: 8081
echo 局域网地址: exp://%LOCAL_IP%:8081
echo 本地地址: exp://localhost:8081
echo 模拟器地址: exp://********:8081
echo.

echo 在雷电模拟器的Expo Go中输入以下地址:
echo exp://********:8081
echo.

echo 正在启动服务器（离线模式，避免网络问题）...
npx expo start --offline --port 8081

echo.
echo 服务器已停止
pause
