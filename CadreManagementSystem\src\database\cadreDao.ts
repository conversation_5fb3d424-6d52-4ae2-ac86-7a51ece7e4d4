import { getDatabase } from './database';
import { CadreInfo, AdvancedSearch, PaginatedResult, FilterConditions } from '../types';

export class CadreDao {
  
  // 添加干部信息
  static async addCadre(cadre: CadreInfo): Promise<number> {
    const db = getDatabase();

    try {
      const result = await db.runAsync(
        `INSERT INTO cadres (
          姓名, 性别, 出生日期, 身份证号, 民族, 政治面貌,
          学历, 职务, 职级, 入职日期, 联系方式, 家庭住址, 照片, 备注
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          cadre.姓名 || '',
          cadre.性别 || '',
          cadre.出生日期 || '',
          cadre.身份证号 || '',
          cadre.民族 || '',
          cadre.政治面貌 || '',
          cadre.学历 || '',
          cadre.职务 || '',
          cadre.职级 || '',
          cadre.入职日期 || '',
          cadre.联系方式 || cadre.联系电话 || '',
          cadre.家庭住址 || '',
          cadre.照片 || '',
          cadre.备注 || ''
        ]
      );

      console.log('干部添加成功:', cadre.姓名, 'ID:', result.lastInsertRowId);
      return result.lastInsertRowId;
    } catch (error) {
      console.error('添加干部信息失败:', error);
      throw new Error(`添加干部失败: ${error}`);
    }
  }

  // 批量添加干部信息
  static async batchAddCadres(cadres: CadreInfo[]): Promise<{ success: number; failed: number; errors: string[] }> {
    const db = getDatabase();
    const result = { success: 0, failed: 0, errors: [] as string[] };

    try {
      await db.withTransactionAsync(async () => {
        for (let i = 0; i < cadres.length; i++) {
          const cadre = cadres[i];
          try {
            await this.addCadre(cadre);
            result.success++;
          } catch (error) {
            result.failed++;
            result.errors.push(`${cadre.姓名}: ${error}`);
            console.error(`批量添加第${i + 1}个干部失败:`, error);
          }
        }
      });
    } catch (error) {
      console.error('批量添加干部事务失败:', error);
      throw new Error(`批量添加失败: ${error}`);
    }

    return result;
  }

  // 更新干部信息
  static async updateCadre(id: number, cadre: Partial<CadreInfo>): Promise<boolean> {
    const db = getDatabase();
    
    const fields = Object.keys(cadre).filter(key => key !== 'id');
    const setClause = fields.map(field => `${field} = ?`).join(', ');
    const values = fields.map(key => cadre[key as keyof CadreInfo]);
    
    const sql = `
      UPDATE cadres 
      SET ${setClause}, 更新时间 = CURRENT_TIMESTAMP 
      WHERE id = ?
    `;
    
    try {
      const result = await db.runAsync(sql, [...values, id]);
      return result.changes > 0;
    } catch (error) {
      console.error('更新干部信息失败:', error);
      throw error;
    }
  }

  // 删除干部信息
  static async deleteCadre(id: number): Promise<boolean> {
    const db = getDatabase();
    
    try {
      const result = await db.runAsync('DELETE FROM cadres WHERE id = ?', [id]);
      return result.changes > 0;
    } catch (error) {
      console.error('删除干部信息失败:', error);
      throw error;
    }
  }

  // 根据ID获取干部信息
  static async getCadreById(id: number): Promise<CadreInfo | null> {
    const db = getDatabase();
    
    try {
      const result = await db.getFirstAsync<CadreInfo>(
        'SELECT * FROM cadres WHERE id = ?',
        [id]
      );
      return result || null;
    } catch (error) {
      console.error('获取干部信息失败:', error);
      throw error;
    }
  }

  // 获取所有干部信息
  static async getAllCadres(): Promise<CadreInfo[]> {
    const db = getDatabase();
    
    try {
      const result = await db.getAllAsync<CadreInfo>(
        'SELECT * FROM cadres ORDER BY 创建时间 DESC'
      );
      return result;
    } catch (error) {
      console.error('获取所有干部信息失败:', error);
      throw error;
    }
  }

  // 分页获取干部信息
  static async getCadresPaginated(
    page: number = 1, 
    pageSize: number = 20,
    filters?: FilterConditions
  ): Promise<PaginatedResult<CadreInfo>> {
    const db = getDatabase();
    const offset = (page - 1) * pageSize;
    
    let whereClause = '';
    let params: any[] = [];
    
    if (filters) {
      const conditions: string[] = [];
      
      if (filters.gender) {
        conditions.push('性别 = ?');
        params.push(filters.gender);
      }
      
      if (filters.positionLevel) {
        conditions.push('现职级 = ?');
        params.push(filters.positionLevel);
      }
      
      if (filters.unit) {
        conditions.push('单位 LIKE ?');
        params.push(`%${filters.unit}%`);
      }
      
      if (filters.retirementStatus) {
        conditions.push('退休状态 = ?');
        params.push(filters.retirementStatus);
      }
      
      if (conditions.length > 0) {
        whereClause = 'WHERE ' + conditions.join(' AND ');
      }
    }
    
    try {
      // 获取总数
      const countSql = `SELECT COUNT(*) as total FROM cadres ${whereClause}`;
      const countResult = await db.getFirstAsync<{ total: number }>(countSql, params);
      const total = countResult?.total || 0;
      
      // 获取分页数据
      const dataSql = `
        SELECT * FROM cadres ${whereClause} 
        ORDER BY 创建时间 DESC 
        LIMIT ? OFFSET ?
      `;
      const data = await db.getAllAsync<CadreInfo>(dataSql, [...params, pageSize, offset]);
      
      return {
        data,
        pagination: {
          page,
          pageSize,
          total,
          totalPages: Math.ceil(total / pageSize)
        }
      };
    } catch (error) {
      console.error('分页获取干部信息失败:', error);
      throw error;
    }
  }

  // 高级搜索
  static async advancedSearch(searchParams: AdvancedSearch): Promise<CadreInfo[]> {
    const db = getDatabase();
    
    let whereClause = '';
    let params: any[] = [];
    
    if (searchParams.conditions.length > 0) {
      const conditionStrings = searchParams.conditions.map(condition => {
        switch (condition.operator) {
          case 'equals':
            params.push(condition.value);
            return `${condition.field} = ?`;
          case 'contains':
            params.push(`%${condition.value}%`);
            return `${condition.field} LIKE ?`;
          case 'startsWith':
            params.push(`${condition.value}%`);
            return `${condition.field} LIKE ?`;
          case 'endsWith':
            params.push(`%${condition.value}`);
            return `${condition.field} LIKE ?`;
          case 'greaterThan':
            params.push(condition.value);
            return `${condition.field} > ?`;
          case 'lessThan':
            params.push(condition.value);
            return `${condition.field} < ?`;
          case 'between':
            if (Array.isArray(condition.value)) {
              params.push(condition.value[0], condition.value[1]);
              return `${condition.field} BETWEEN ? AND ?`;
            }
            return '';
          default:
            return '';
        }
      }).filter(Boolean);
      
      whereClause = 'WHERE ' + conditionStrings.join(` ${searchParams.logic} `);
    }
    
    let orderClause = '';
    if (searchParams.sortBy) {
      orderClause = `ORDER BY ${searchParams.sortBy} ${searchParams.sortOrder || 'ASC'}`;
    }
    
    let limitClause = '';
    if (searchParams.limit) {
      limitClause = `LIMIT ${searchParams.limit}`;
      if (searchParams.offset) {
        limitClause += ` OFFSET ${searchParams.offset}`;
      }
    }
    
    const sql = `SELECT * FROM cadres ${whereClause} ${orderClause} ${limitClause}`;
    
    try {
      const result = await db.getAllAsync<CadreInfo>(sql, params);
      return result;
    } catch (error) {
      console.error('高级搜索失败:', error);
      throw error;
    }
  }

  // 根据身份证号查找干部
  static async getCadreByIdCard(idCard: string): Promise<CadreInfo | null> {
    const db = getDatabase();
    
    try {
      const result = await db.getFirstAsync<CadreInfo>(
        'SELECT * FROM cadres WHERE 身份证号 = ?',
        [idCard]
      );
      return result || null;
    } catch (error) {
      console.error('根据身份证号查找干部失败:', error);
      throw error;
    }
  }

  // 批量插入干部信息（用于Excel导入）
  static async batchInsertCadres(cadres: CadreInfo[]): Promise<{ success: number; failed: number; errors: string[] }> {
    const db = getDatabase();
    let successCount = 0;
    let failedCount = 0;
    const errors: string[] = [];

    try {
      await db.withTransactionAsync(async () => {
        for (let i = 0; i < cadres.length; i++) {
          const cadre = cadres[i];

          try {
            // 如果有身份证号，先检查是否已存在
            if (cadre.身份证号) {
              const existing = await db.getFirstAsync<{ id: number }>(
                'SELECT id FROM cadres WHERE 身份证号 = ?',
                [cadre.身份证号]
              );

              if (existing) {
                // 更新现有记录
                const updateFields = Object.keys(cadre)
                  .filter(key => key !== 'id' && cadre[key as keyof CadreInfo])
                  .map(key => `${key} = ?`)
                  .join(', ');

                const updateValues = Object.keys(cadre)
                  .filter(key => key !== 'id' && cadre[key as keyof CadreInfo])
                  .map(key => cadre[key as keyof CadreInfo]);

                if (updateFields) {
                  await db.runAsync(
                    `UPDATE cadres SET ${updateFields}, 更新时间 = CURRENT_TIMESTAMP WHERE id = ?`,
                    [...updateValues, existing.id]
                  );
                }
                successCount++;
                continue;
              }
            }

            // 插入新记录
            const fields = Object.keys(cadre).filter(key => key !== 'id' && cadre[key as keyof CadreInfo]);
            const placeholders = fields.map(() => '?').join(', ');
            const values = fields.map(key => cadre[key as keyof CadreInfo]);

            const sql = `
              INSERT INTO cadres (${fields.join(', ')}, 创建时间, 更新时间)
              VALUES (${placeholders}, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            `;

            await db.runAsync(sql, values);
            successCount++;

          } catch (error) {
            failedCount++;
            const errorMsg = `第${i + 1}行 ${cadre.姓名}: ${error}`;
            errors.push(errorMsg);
            console.warn('插入单条记录失败:', errorMsg);
          }
        }
      });

      return { success: successCount, failed: failedCount, errors };
    } catch (error) {
      console.error('批量插入干部信息失败:', error);
      throw error;
    }
  }

  // 清空所有干部数据
  static async clearAllCadres(): Promise<void> {
    const db = getDatabase();

    try {
      await db.runAsync('DELETE FROM cadres');
      console.log('所有干部数据已清空');
    } catch (error) {
      console.error('清空干部数据失败:', error);
      throw error;
    }
  }

  // 获取统计数据
  static async getStatistics() {
    const db = getDatabase();
    
    try {
      // 总数统计
      const totalResult = await db.getFirstAsync<{ count: number }>(
        'SELECT COUNT(*) as count FROM cadres'
      );
      
      // 按性别统计
      const genderStats = await db.getAllAsync<{ 性别: string; count: number }>(
        'SELECT 性别, COUNT(*) as count FROM cadres WHERE 性别 IS NOT NULL GROUP BY 性别'
      );
      
      // 按职级统计
      const positionStats = await db.getAllAsync<{ 现职级: string; count: number }>(
        'SELECT 现职级, COUNT(*) as count FROM cadres WHERE 现职级 IS NOT NULL GROUP BY 现职级'
      );
      
      // 按单位统计
      const unitStats = await db.getAllAsync<{ 单位: string; count: number }>(
        'SELECT 单位, COUNT(*) as count FROM cadres WHERE 单位 IS NOT NULL GROUP BY 单位'
      );
      
      // 按退休状态统计
      const retirementStats = await db.getAllAsync<{ 退休状态: string; count: number }>(
        'SELECT 退休状态, COUNT(*) as count FROM cadres GROUP BY 退休状态'
      );
      
      return {
        total: totalResult?.count || 0,
        byGender: genderStats.reduce((acc, item) => {
          acc[item.性别] = item.count;
          return acc;
        }, {} as Record<string, number>),
        byPosition: positionStats.reduce((acc, item) => {
          acc[item.现职级] = item.count;
          return acc;
        }, {} as Record<string, number>),
        byUnit: unitStats.reduce((acc, item) => {
          acc[item.单位] = item.count;
          return acc;
        }, {} as Record<string, number>),
        byRetirementStatus: retirementStats.reduce((acc, item) => {
          acc[item.退休状态] = item.count;
          return acc;
        }, {} as Record<string, number>)
      };
    } catch (error) {
      console.error('获取统计数据失败:', error);
      throw error;
    }
  }

  /**
   * 批量更新职级
   */
  static async batchUpdatePosition(oldPosition: string, newPosition: string): Promise<number> {
    const db = getDatabase();

    try {
      console.log(`🔄 开始批量更新职级：${oldPosition} -> ${newPosition}`);

      const result = await db.runAsync(
        'UPDATE cadres SET 现职级 = ? WHERE 现职级 = ?',
        [newPosition, oldPosition]
      );

      console.log(`✅ 批量更新职级完成，影响${result.changes}条记录`);
      return result.changes;
    } catch (error) {
      console.error('批量更新职级失败:', error);
      throw new Error(`批量更新职级失败: ${error}`);
    }
  }
}
