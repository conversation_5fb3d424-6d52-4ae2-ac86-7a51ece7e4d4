const { spawn } = require('child_process');
const path = require('path');

console.log('Testing Expo start...');
console.log('Current directory:', process.cwd());
console.log('Node version:', process.version);

// 检查expo是否可用
const expo = spawn('npx', ['expo', '--version'], {
  stdio: 'pipe',
  shell: true
});

expo.stdout.on('data', (data) => {
  console.log('Expo version:', data.toString().trim());
});

expo.stderr.on('data', (data) => {
  console.error('Expo version error:', data.toString());
});

expo.on('close', (code) => {
  console.log('Expo version check completed with code:', code);
  
  if (code === 0) {
    console.log('Starting Expo server...');
    
    const expoStart = spawn('npx', ['expo', 'start', '--lan', '--port', '8081'], {
      stdio: 'inherit',
      shell: true
    });
    
    expoStart.on('error', (error) => {
      console.error('Failed to start Expo:', error);
    });
    
    expoStart.on('close', (code) => {
      console.log('Expo server closed with code:', code);
    });
  } else {
    console.error('Expo is not available');
  }
});
