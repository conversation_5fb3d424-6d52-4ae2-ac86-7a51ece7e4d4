console.log('🔍 检查Excel文件中申丽丽的真实数据...\n');

const XLSX = require('xlsx');
const path = require('path');

// Excel文件路径
const excelPath = path.join(__dirname, '0606.xlsx');

try {
  console.log(`📂 读取Excel文件: ${excelPath}`);
  
  const workbook = XLSX.readFile(excelPath);
  const sheetName = workbook.SheetNames[0];
  const worksheet = workbook.Sheets[sheetName];
  const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
  
  console.log(`📊 Excel文件包含 ${data.length} 行数据`);
  console.log('📋 表头:', data[0]);
  
  // 查找申丽丽
  const dataRows = data.slice(1);
  let foundShenLiLi = false;
  let shenLiLiData = null;
  
  console.log('\n🔍 查找申丽丽：');
  dataRows.forEach((row, index) => {
    // 尝试不同的列位置查找姓名
    for (let i = 0; i < Math.min(row.length, 5); i++) {
      const name = row[i];
      if (name && typeof name === 'string' && name.includes('申丽丽')) {
        foundShenLiLi = true;
        shenLiLiData = row;
        console.log(`✅ 找到申丽丽 - 行${index + 2}:`, row);
        break;
      }
    }
  });
  
  if (!foundShenLiLi) {
    console.log('❌ 未找到申丽丽，查看所有女性干部：');
    
    // 查找所有女性
    dataRows.forEach((row, index) => {
      for (let i = 0; i < Math.min(row.length, 10); i++) {
        const cell = row[i];
        if (cell && typeof cell === 'string' && cell === '女') {
          console.log(`女性干部 - 行${index + 2}:`, row.slice(0, 10));
          break;
        }
      }
    });
    
    // 查看前10行数据
    console.log('\n📋 前10行数据：');
    dataRows.slice(0, 10).forEach((row, index) => {
      console.log(`${index + 1}:`, row.slice(0, 10));
    });
  } else {
    console.log('\n📋 申丽丽的详细信息：');
    
    // 假设表头结构
    const headers = data[0];
    console.log('表头:', headers);
    
    shenLiLiData.forEach((value, index) => {
      if (value !== undefined && value !== null && value !== '') {
        const header = headers[index] || `列${index + 1}`;
        console.log(`${header}: ${value}`);
      }
    });
    
    // 查找出生日期列
    let birthDateValue = null;
    let birthDateIndex = -1;
    
    // 常见的出生日期列名
    const birthDateHeaders = ['出生日期', '出生年月', '生日', '出生时间'];
    
    birthDateHeaders.forEach(headerName => {
      const index = headers.findIndex(h => h && h.includes(headerName));
      if (index !== -1 && shenLiLiData[index]) {
        birthDateValue = shenLiLiData[index];
        birthDateIndex = index;
        console.log(`\n📅 找到出生日期列: ${headers[index]} = ${birthDateValue}`);
      }
    });
    
    // 如果没找到，尝试查找日期格式的数据
    if (!birthDateValue) {
      console.log('\n🔍 尝试查找日期格式的数据：');
      shenLiLiData.forEach((value, index) => {
        if (value && typeof value === 'string') {
          // 检查是否是日期格式
          if (value.match(/\d{4}[-\/年]\d{1,2}[-\/月]\d{1,2}/) || 
              value.match(/\d{4}-\d{1,2}-\d{1,2}/) ||
              value.match(/\d{4}\/\d{1,2}\/\d{1,2}/)) {
            console.log(`可能的出生日期 - 列${index + 1}(${headers[index] || '未知'}): ${value}`);
            if (!birthDateValue) {
              birthDateValue = value;
              birthDateIndex = index;
            }
          }
        }
      });
    }
    
    if (birthDateValue) {
      console.log(`\n🎯 申丽丽的出生日期: ${birthDateValue}`);
      
      // 解析出生日期
      let birthDate;
      const cleanStr = birthDateValue.toString().replace(/[年月日\-\.]/g, '/');
      birthDate = new Date(cleanStr);
      
      if (!isNaN(birthDate.getTime())) {
        const birthYear = birthDate.getFullYear();
        console.log(`解析结果: ${birthDate.toLocaleDateString()}`);
        console.log(`出生年份: ${birthYear}年`);
        
        // 计算当前年龄
        const today = new Date();
        let age = today.getFullYear() - birthDate.getFullYear();
        const monthDiff = today.getMonth() - birthDate.getMonth();
        
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
          age--;
        }
        
        console.log(`当前年龄: ${age}岁`);
        
        // 查找性别
        let gender = null;
        const genderHeaders = ['性别'];
        genderHeaders.forEach(headerName => {
          const index = headers.findIndex(h => h && h.includes(headerName));
          if (index !== -1 && shenLiLiData[index]) {
            gender = shenLiLiData[index];
            console.log(`性别: ${gender}`);
          }
        });
        
        if (!gender) {
          // 尝试查找"女"字
          shenLiLiData.forEach((value, index) => {
            if (value === '女' || value === '男') {
              gender = value;
              console.log(`找到性别 - 列${index + 1}: ${gender}`);
            }
          });
        }
        
        if (gender) {
          // 计算延迟退休
          function calculateDelayRetirement(birthYear) {
            let totalDelayMonths = 3; // 基础延迟3个月
            
            if (birthYear >= 1966 && birthYear <= 1970) {
              const extraYears = birthYear - 1965;
              const extraMonths = Math.min(extraYears * 2, 12);
              totalDelayMonths += extraMonths;
            } else if (birthYear >= 1971 && birthYear <= 1980) {
              totalDelayMonths += 12; // 先加满1年
              const extraYears = birthYear - 1970;
              const extraMonths = Math.min(extraYears * 3, 24);
              totalDelayMonths += extraMonths;
            } else if (birthYear >= 1981) {
              totalDelayMonths += 36; // 先加满3年
              const extraYears = birthYear - 1980;
              const extraMonths = Math.min(extraYears * 4, 24);
              totalDelayMonths += extraMonths;
            }
            
            const maxDelayMonths = 60;
            totalDelayMonths = Math.min(totalDelayMonths, maxDelayMonths);
            
            return {
              totalDelayMonths,
              delayYears: Math.floor(totalDelayMonths / 12),
              delayMonths: totalDelayMonths % 12
            };
          }
          
          const delayInfo = calculateDelayRetirement(birthYear);
          console.log(`\n⏰ 延迟退休计算：`);
          console.log(`出生年份: ${birthYear}年`);
          
          if (birthYear <= 1965) {
            console.log(`适用规则: 1965年及以前（基础延迟3个月）`);
          } else if (birthYear >= 1966 && birthYear <= 1970) {
            console.log(`适用规则: 1966-1970年（基础3个月 + 每年2个月）`);
            console.log(`计算过程: 基础3个月 + (${birthYear} - 1965) × 2个月 = 3 + ${(birthYear - 1965) * 2} = ${delayInfo.totalDelayMonths}个月`);
          } else if (birthYear >= 1971 && birthYear <= 1980) {
            console.log(`适用规则: 1971-1980年（基础3个月 + 1年 + 每年3个月）`);
            console.log(`计算过程: 基础3个月 + 12个月 + (${birthYear} - 1970) × 3个月 = 15 + ${(birthYear - 1970) * 3} = ${delayInfo.totalDelayMonths}个月`);
          }
          
          console.log(`延迟月数: ${delayInfo.totalDelayMonths}个月`);
          
          // 计算退休日期
          const baseRetirementAge = gender === '女' ? 55 : 60;
          const actualRetirementAge = baseRetirementAge + (delayInfo.totalDelayMonths / 12);
          
          const legalRetirementDate = new Date(birthDate);
          legalRetirementDate.setFullYear(birthDate.getFullYear() + baseRetirementAge);
          
          const actualRetirementDate = new Date(legalRetirementDate);
          actualRetirementDate.setMonth(actualRetirementDate.getMonth() + delayInfo.totalDelayMonths);
          
          const daysToActual = Math.ceil((actualRetirementDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
          
          console.log(`\n🎯 退休计算结果：`);
          console.log(`法定退休年龄: ${baseRetirementAge}岁`);
          console.log(`实际退休年龄: ${actualRetirementAge.toFixed(2)}岁`);
          console.log(`实际退休日期: ${actualRetirementDate.toLocaleDateString()}`);
          console.log(`距离实际退休: ${daysToActual}天`);
          
          console.log(`\n📊 正确的状态判断：`);
          if (age < baseRetirementAge) {
            console.log(`✅ 未到法定退休年龄（${age}岁 < ${baseRetirementAge}岁）`);
            if (daysToActual > 0) {
              console.log(`✅ 未到实际退休日期（还剩${daysToActual}天）`);
              console.log(`📋 应归类为: 近两年退休预警`);
            } else {
              console.log(`❌ 已超过实际退休日期（超过${Math.abs(daysToActual)}天）`);
              console.log(`📋 应归类为: 已退休`);
            }
          } else {
            console.log(`⚠️ 已达到法定退休年龄（${age}岁 >= ${baseRetirementAge}岁）`);
            if (daysToActual > 0) {
              console.log(`✅ 但未到实际退休日期（还剩${daysToActual}天）`);
              console.log(`📋 应归类为: 已超过法定退休年龄，但未到实际退休`);
            } else {
              console.log(`❌ 已超过实际退休日期（超过${Math.abs(daysToActual)}天）`);
              console.log(`📋 应归类为: 已退休`);
            }
          }
          
          console.log(`\n🎯 最终结论：`);
          console.log(`申丽丽（${gender}，${birthDateValue}）：`);
          console.log(`- 当前年龄: ${age}岁`);
          console.log(`- 延迟月数: ${delayInfo.totalDelayMonths}个月`);
          console.log(`- 距离实际退休: ${daysToActual}天`);
          console.log(`- 状态: ${age < baseRetirementAge ? '未到法定退休年龄' : '已超过法定退休年龄'}，${daysToActual > 0 ? '未到实际退休日期' : '已超过实际退休日期'}`);
        } else {
          console.log('❌ 未找到性别信息');
        }
      } else {
        console.log('❌ 无法解析出生日期格式');
      }
    } else {
      console.log('❌ 未找到出生日期信息');
    }
  }
  
} catch (error) {
  console.error('❌ 读取Excel文件失败:', error.message);
}

console.log('\n🔍 Excel文件中申丽丽数据检查完成！');
