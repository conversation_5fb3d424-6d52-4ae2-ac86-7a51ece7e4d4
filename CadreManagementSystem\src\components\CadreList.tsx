import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  Alert,
  RefreshControl,
  TextInput,
  Modal
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { CadreInfo, FilterConditions, PaginatedResult } from '../types';
import { CadreDao } from '../database/cadreDao';

interface CadreListProps {
  onCadrePress: (cadre: CadreInfo) => void;
  onCadreEdit: (cadre: CadreInfo) => void;
  onAddPress: () => void;
  searchQuery?: string;
  filters?: FilterConditions;
}

export const CadreList: React.FC<CadreListProps> = ({
  onCadrePress,
  onCadreEdit,
  onAddPress,
  searchQuery = '',
  filters
}) => {
  const [cadres, setCadres] = useState<CadreInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [showFilters, setShowFilters] = useState(false);
  const [localFilters, setLocalFilters] = useState<FilterConditions>(filters || {});
  const [searchText, setSearchText] = useState(searchQuery);
  const [showSearch, setShowSearch] = useState(false);

  const pageSize = 20;

  // 加载干部数据
  const loadCadres = useCallback(async (pageNum: number = 1, reset: boolean = false) => {
    if (loading && !reset) return;

    setLoading(true);
    try {
      let result: PaginatedResult<CadreInfo>;

      if (searchText.trim()) {
        // 如果有搜索文本，使用搜索功能
        const allCadres = await CadreDao.getAllCadres();
        const filteredCadres = allCadres.filter(cadre =>
          cadre.姓名?.includes(searchText) ||
          cadre.身份证号?.includes(searchText) ||
          cadre.职务?.includes(searchText) ||
          cadre.职级?.includes(searchText) ||
          cadre.部门?.includes(searchText) ||
          cadre.联系方式?.includes(searchText)
        );

        // 手动分页
        const startIndex = (pageNum - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const pageData = filteredCadres.slice(startIndex, endIndex);

        result = {
          data: pageData,
          pagination: {
            page: pageNum,
            pageSize,
            total: filteredCadres.length,
            totalPages: Math.ceil(filteredCadres.length / pageSize)
          }
        };
      } else {
        // 正常分页加载
        result = await CadreDao.getCadresPaginated(
          pageNum,
          pageSize,
          localFilters
        );
      }

      if (reset) {
        setCadres(result.data);
      } else {
        setCadres(prev => [...prev, ...result.data]);
      }

      setHasMore(result.pagination.page < result.pagination.totalPages);
      setPage(pageNum);
    } catch (error) {
      console.error('加载干部数据失败:', error);
      Alert.alert('错误', '加载数据失败，请重试');
    } finally {
      setLoading(false);
    }
  }, [localFilters, loading, searchText]);

  // 初始加载
  useEffect(() => {
    loadCadres(1, true);
  }, [localFilters]);

  // 搜索处理
  const handleSearch = useCallback((text: string) => {
    setSearchText(text);
    // 延迟搜索，避免频繁请求
    const timeoutId = setTimeout(() => {
      loadCadres(1, true);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [loadCadres]);

  // 清除搜索
  const clearSearch = useCallback(() => {
    setSearchText('');
    setShowSearch(false);
    loadCadres(1, true);
  }, [loadCadres]);

  // 下拉刷新
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadCadres(1, true);
    setRefreshing(false);
  }, [loadCadres]);

  // 加载更多
  const loadMore = useCallback(() => {
    if (hasMore && !loading) {
      loadCadres(page + 1);
    }
  }, [hasMore, loading, page, loadCadres]);

  // 删除干部
  const deleteCadre = useCallback(async (cadre: CadreInfo) => {
    Alert.alert(
      '确认删除',
      `确定要删除 ${cadre.姓名} 的信息吗？`,
      [
        { text: '取消', style: 'cancel' },
        {
          text: '删除',
          style: 'destructive',
          onPress: async () => {
            try {
              if (cadre.id) {
                await CadreDao.deleteCadre(cadre.id);
                await onRefresh();
                Alert.alert('成功', '删除成功');
              }
            } catch (error) {
              console.error('删除失败:', error);
              Alert.alert('错误', '删除失败，请重试');
            }
          }
        }
      ]
    );
  }, [onRefresh]);

  // 应用筛选
  const applyFilters = useCallback(() => {
    setLocalFilters({ ...localFilters });
    setShowFilters(false);
  }, [localFilters]);

  // 清除筛选
  const clearFilters = useCallback(() => {
    setLocalFilters({});
    setShowFilters(false);
  }, []);

  // 渲染干部项
  const renderCadreItem = ({ item }: { item: CadreInfo }) => (
    <TouchableOpacity
      style={styles.cadreItem}
      onPress={() => onCadrePress(item)}
    >
      <View style={styles.cadreHeader}>
        <View style={styles.cadreInfo}>
          <Text style={styles.cadreName}>{item.姓名}</Text>
          <Text style={styles.cadrePosition}>
            {item.现职级} | {item.单位}
          </Text>
        </View>
        <View style={styles.cadreActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => onCadrePress(item)}
          >
            <Ionicons name="eye" size={20} color="#007AFF" />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => onCadreEdit(item)}
          >
            <Ionicons name="create" size={20} color="#FF9500" />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => deleteCadre(item)}
          >
            <Ionicons name="trash" size={20} color="#FF3B30" />
          </TouchableOpacity>
        </View>
      </View>
      
      <View style={styles.cadreDetails}>
        <Text style={styles.detailText}>
          性别: {item.性别} | 年龄: {item.身份证号 ? calculateAge(item.身份证号) : '未知'}
        </Text>
        <Text style={styles.detailText}>
          政治面貌: {item.政治面貌} | 联系方式: {item.联系方式}
        </Text>
      </View>
      
      {item.退休状态 !== 'active' && (
        <View style={[styles.statusBadge, getStatusStyle(item.退休状态)]}>
          <Text style={styles.statusText}>{getStatusText(item.退休状态)}</Text>
        </View>
      )}
    </TouchableOpacity>
  );

  // 渲染筛选模态框
  const renderFilterModal = () => (
    <Modal
      visible={showFilters}
      animationType="slide"
      transparent={true}
      onRequestClose={() => setShowFilters(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>筛选条件</Text>
            <TouchableOpacity onPress={() => setShowFilters(false)}>
              <Ionicons name="close" size={24} color="#333" />
            </TouchableOpacity>
          </View>
          
          <View style={styles.filterSection}>
            <Text style={styles.filterLabel}>性别</Text>
            <View style={styles.filterOptions}>
              {['', '男', '女'].map(gender => (
                <TouchableOpacity
                  key={gender}
                  style={[
                    styles.filterOption,
                    localFilters.gender === gender && styles.filterOptionActive
                  ]}
                  onPress={() => setLocalFilters(prev => ({ ...prev, gender }))}
                >
                  <Text style={[
                    styles.filterOptionText,
                    localFilters.gender === gender && styles.filterOptionTextActive
                  ]}>
                    {gender || '全部'}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <View style={styles.filterSection}>
            <Text style={styles.filterLabel}>退休状态</Text>
            <View style={styles.filterOptions}>
              {[
                { value: '', label: '全部' },
                { value: 'active', label: '在职' },
                { value: 'early_warning', label: '预警' },
                { value: 'second_line', label: '二线' },
                { value: 'retired', label: '退休' }
              ].map(status => (
                <TouchableOpacity
                  key={status.value}
                  style={[
                    styles.filterOption,
                    localFilters.retirementStatus === status.value && styles.filterOptionActive
                  ]}
                  onPress={() => setLocalFilters(prev => ({ 
                    ...prev, 
                    retirementStatus: status.value 
                  }))}
                >
                  <Text style={[
                    styles.filterOptionText,
                    localFilters.retirementStatus === status.value && styles.filterOptionTextActive
                  ]}>
                    {status.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <View style={styles.filterSection}>
            <Text style={styles.filterLabel}>单位</Text>
            <TextInput
              style={styles.filterInput}
              placeholder="输入单位名称"
              value={localFilters.unit || ''}
              onChangeText={(text) => setLocalFilters(prev => ({ ...prev, unit: text }))}
            />
          </View>

          <View style={styles.modalActions}>
            <TouchableOpacity style={styles.clearButton} onPress={clearFilters}>
              <Text style={styles.clearButtonText}>清除</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.applyButton} onPress={applyFilters}>
              <Text style={styles.applyButtonText}>应用</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  return (
    <View style={styles.container}>
      {/* 工具栏 */}
      <View style={styles.toolbar}>
        <TouchableOpacity
          style={styles.toolbarButton}
          onPress={() => setShowSearch(!showSearch)}
        >
          <Ionicons name="search" size={20} color="#007AFF" />
          <Text style={styles.toolbarButtonText}>搜索</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.toolbarButton} onPress={() => setShowFilters(true)}>
          <Ionicons name="filter" size={20} color="#007AFF" />
          <Text style={styles.toolbarButtonText}>筛选</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.addButton} onPress={onAddPress}>
          <Ionicons name="add" size={20} color="#FFF" />
          <Text style={styles.addButtonText}>添加</Text>
        </TouchableOpacity>
      </View>

      {/* 搜索栏 */}
      {showSearch && (
        <View style={styles.searchContainer}>
          <View style={styles.searchInputContainer}>
            <Ionicons name="search" size={16} color="#8E8E93" style={styles.searchIcon} />
            <TextInput
              style={styles.searchInput}
              placeholder="搜索姓名、身份证号、职务等..."
              value={searchText}
              onChangeText={handleSearch}
              clearButtonMode="while-editing"
            />
            {searchText.length > 0 && (
              <TouchableOpacity onPress={clearSearch} style={styles.searchClearButton}>
                <Ionicons name="close-circle" size={16} color="#8E8E93" />
              </TouchableOpacity>
            )}
          </View>
        </View>
      )}

      {/* 干部列表 */}
      <FlatList
        data={cadres}
        renderItem={renderCadreItem}
        keyExtractor={(item) => item.id?.toString() || Math.random().toString()}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        onEndReached={loadMore}
        onEndReachedThreshold={0.1}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.listContent}
      />

      {renderFilterModal()}
    </View>
  );
};

// 辅助函数
const calculateAge = (idCard: string): string => {
  if (!idCard || idCard.length !== 18) return '未知';
  
  const birthYear = parseInt(idCard.substring(6, 10));
  const birthMonth = parseInt(idCard.substring(10, 12));
  const birthDay = parseInt(idCard.substring(12, 14));
  
  const today = new Date();
  let age = today.getFullYear() - birthYear;
  
  if (today.getMonth() + 1 < birthMonth || 
      (today.getMonth() + 1 === birthMonth && today.getDate() < birthDay)) {
    age--;
  }
  
  return age.toString();
};

const getStatusStyle = (status?: string) => {
  switch (status) {
    case 'early_warning':
      return { backgroundColor: '#FF9500' };
    case 'second_line':
      return { backgroundColor: '#007AFF' };
    case 'retired':
      return { backgroundColor: '#8E8E93' };
    default:
      return { backgroundColor: '#34C759' };
  }
};

const getStatusText = (status?: string) => {
  switch (status) {
    case 'early_warning':
      return '预警';
    case 'second_line':
      return '二线';
    case 'retired':
      return '退休';
    default:
      return '在职';
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7'
  },
  toolbar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA'
  },
  toolbarButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#007AFF'
  },
  toolbarButtonText: {
    marginLeft: 4,
    color: '#007AFF',
    fontSize: 14,
    fontWeight: '500'
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#007AFF',
    borderRadius: 8
  },
  addButtonText: {
    marginLeft: 4,
    color: '#FFF',
    fontSize: 14,
    fontWeight: '600'
  },
  listContent: {
    padding: 16
  },
  cadreItem: {
    backgroundColor: '#FFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3
  },
  cadreHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8
  },
  cadreInfo: {
    flex: 1
  },
  cadreName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000',
    marginBottom: 4
  },
  cadrePosition: {
    fontSize: 14,
    color: '#8E8E93'
  },
  cadreActions: {
    flexDirection: 'row'
  },
  actionButton: {
    padding: 8,
    marginLeft: 8
  },
  cadreDetails: {
    marginTop: 8
  },
  detailText: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2
  },
  statusBadge: {
    position: 'absolute',
    top: 12,
    right: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12
  },
  statusText: {
    color: '#FFF',
    fontSize: 10,
    fontWeight: '600'
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end'
  },
  modalContent: {
    backgroundColor: '#FFF',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingHorizontal: 20,
    paddingBottom: 40,
    maxHeight: '80%'
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA'
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000'
  },
  filterSection: {
    marginTop: 20
  },
  filterLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#000',
    marginBottom: 12
  },
  filterOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap'
  },
  filterOption: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#E5E5EA',
    marginRight: 8,
    marginBottom: 8
  },
  filterOptionActive: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF'
  },
  filterOptionText: {
    fontSize: 14,
    color: '#666'
  },
  filterOptionTextActive: {
    color: '#FFF'
  },
  filterInput: {
    borderWidth: 1,
    borderColor: '#E5E5EA',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 30
  },
  clearButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#FF3B30',
    marginRight: 8
  },
  clearButtonText: {
    textAlign: 'center',
    color: '#FF3B30',
    fontSize: 16,
    fontWeight: '600'
  },
  applyButton: {
    flex: 1,
    paddingVertical: 12,
    backgroundColor: '#007AFF',
    borderRadius: 8,
    marginLeft: 8
  },
  applyButtonText: {
    textAlign: 'center',
    color: '#FFF',
    fontSize: 16,
    fontWeight: '600'
  },
  // 搜索相关样式
  searchContainer: {
    backgroundColor: '#FFF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA'
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F2F2F7',
    borderRadius: 10,
    paddingHorizontal: 12,
    paddingVertical: 8
  },
  searchIcon: {
    marginRight: 8
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#000'
  },
  searchClearButton: {
    padding: 4,
    marginLeft: 8
  }
});
