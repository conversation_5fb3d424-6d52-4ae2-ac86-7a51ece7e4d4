// 干部信息接口
export interface CadreInfo {
  id?: number;
  序号?: string;
  单位?: string;
  姓名: string;
  职务?: string;
  性别?: string;
  民族?: string;
  籍贯?: string;
  出生日期?: string | number; // 支持Excel日期序列号
  参加工作时间?: string;
  政治面貌?: string;
  入党时间?: string;
  全日制教育?: string;
  毕业院校系及专业?: string;
  在职教育?: string;
  毕业院校系及专业_在职?: string;
  专业技术职务?: string;
  现职级?: string;
  任现职时间?: string;
  任现职级时间?: string;
  工作简历?: string;
  身份证号?: string;
  身份证号码校验正误?: string;
  联系方式?: string;
  获得奖励荣誉情况?: string;
  党纪政纪处分情况?: string;
  备注?: string;
  照片路径?: string;
  退休状态?: 'active' | 'early_warning' | 'second_line' | 'retired' | 'delayed_retirement' | 'transferred';
  创建时间?: string;
  更新时间?: string;
}

// 职级信息接口
export interface PositionLevel {
  id?: number;
  level_name: string;
  level_category?: string;
  sort_order?: number;
  is_active?: number;
  创建时间?: string;
  更新时间?: string;
}

// 退休预警配置接口
export interface RetirementConfig {
  id?: number;
  config_type: string;
  config_name: string;
  config_value: string;
  description?: string;
  创建时间?: string;
  更新时间?: string;
}

// 系统设置接口
export interface SystemSetting {
  id?: number;
  setting_key: string;
  setting_value?: string;
  setting_type?: 'string' | 'number' | 'boolean' | 'json';
  description?: string;
  创建时间?: string;
  更新时间?: string;
}

// 退休预警类型
export type RetirementWarningType = 
  | 'two_year_warning'      // 近两年退休预警
  | 'monthly_warning'       // 月度退休预警
  | 'second_line_warning'   // 退居二线预警
  | 'retired';              // 已退休

// 退休预警信息接口
export interface RetirementWarning {
  cadre: CadreInfo;
  warningType: RetirementWarningType;
  daysUntilRetirement: number;
  urgencyLevel: 'normal' | 'urgent';
  description: string;
}

// 查询条件接口
export interface SearchCondition {
  field: string;
  operator: 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'greaterThan' | 'lessThan' | 'between';
  value: string | number | [string | number, string | number];
}

// 高级查询接口
export interface AdvancedSearch {
  conditions: SearchCondition[];
  logic: 'AND' | 'OR';
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
  limit?: number;
  offset?: number;
}

// 统计数据接口
export interface StatisticsData {
  totalCadres: number;
  activeCadres: number;
  retiredCadres: number;
  twoYearWarning: number;
  monthlyWarning: number;
  secondLineWarning: number;
  byGender: {
    male: number;
    female: number;
  };
  byPositionLevel: Record<string, number>;
  byUnit: Record<string, number>;
}

// Excel导入结果接口
export interface ImportResult {
  success: boolean;
  totalRows: number;
  successRows: number;
  failedRows: number;
  errors: Array<{
    row: number;
    field: string;
    message: string;
  }>;
}

// 导航参数类型
export type RootStackParamList = {
  Home: undefined;
  CadreList: undefined;
  CadreDetail: { cadreId?: number; mode?: 'view' | 'edit' | 'add' };
  RetirementWarning: undefined;
  PositionManagement: undefined;
  Settings: undefined;
  ImportExcel: undefined;
};

// 表单验证错误接口
export interface ValidationError {
  field: string;
  message: string;
}

// 身份证验证结果接口
export interface IdCardValidation {
  isValid: boolean;
  birthDate?: string;
  gender?: string;
  age?: number;
  errors: string[];
}

// 图表数据接口
export interface ChartData {
  labels: string[];
  datasets: Array<{
    data: number[];
    colors?: string[];
  }>;
}

// 饼图数据接口
export interface PieChartData {
  name: string;
  population: number;
  color: string;
  legendFontColor: string;
  legendFontSize: number;
}

// 应用状态接口
export interface AppState {
  isLoading: boolean;
  error: string | null;
  cadres: CadreInfo[];
  positionLevels: PositionLevel[];
  systemSettings: SystemSetting[];
  retirementWarnings: RetirementWarning[];
  statistics: StatisticsData | null;
}

// 操作结果接口
export interface OperationResult<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// 分页参数接口
export interface PaginationParams {
  page: number;
  pageSize: number;
  total?: number;
}

// 分页结果接口
export interface PaginatedResult<T> {
  data: T[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
}

// 筛选选项接口
export interface FilterOption {
  label: string;
  value: string;
  count?: number;
}

// 筛选条件接口
export interface FilterConditions {
  gender?: string;
  positionLevel?: string;
  unit?: string;
  retirementStatus?: string;
  ageRange?: [number, number];
  workYearsRange?: [number, number];
}

// 职级信息类型（新版本，合并了两个定义）
export interface PositionLevel {
  id?: number;
  level_name?: string;             // 旧版本字段名
  name?: string;                   // 新版本字段名
  level?: number;                  // 职级等级（数字越大级别越高）
  level_category?: string;         // 旧版本分类字段
  category?: 'administrative' | 'technical' | 'worker';  // 新版本职级类别
  retirement_age_male?: number;    // 男性退休年龄
  retirement_age_female?: number;  // 女性退休年龄
  description?: string;            // 职级描述
  is_active?: number | boolean;    // 是否启用（兼容两种类型）
  sort_order?: number;             // 排序
  created_at?: string;
  updated_at?: string;
  创建时间?: string;                // 旧版本字段名
  更新时间?: string;                // 旧版本字段名
}

// 退休规则设置类型
export interface RetirementRule {
  id?: number;
  position_level_id?: number;      // 职级ID（可选，为空表示通用规则）
  gender: 'male' | 'female' | 'all';  // 性别
  base_retirement_age: number;     // 基础退休年龄
  delay_start_year: number;        // 延迟退休开始年份
  delay_months_per_year: number;   // 每年延迟月数
  max_retirement_age: number;      // 最大退休年龄
  is_active: boolean;             // 是否启用
  description?: string;           // 规则描述
  created_at?: string;
  updated_at?: string;
}

// 数据清空密码设置
export interface DataClearPassword {
  id?: number;
  password_hash: string;          // 密码哈希
  salt: string;                   // 盐值
  created_at?: string;
  updated_at?: string;
}

// 系统设置类型
export interface SystemSettings {
  id?: number;
  key: string;
  value: string;
  description?: string;
  created_at?: string;
  updated_at?: string;
}
