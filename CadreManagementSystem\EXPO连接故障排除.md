# EXPO连接故障排除指南

## 🔧 当前问题
雷电模拟器显示"Something went wrong"错误，无法连接EXPO开发服务器。

## 🚀 解决方案

### 方案1: 重启EXPO服务器（推荐）
```bash
# 停止当前服务器
Ctrl + C

# 清理缓存并重启
npx expo start --clear --reset-cache
```

### 方案2: 使用LAN模式
```bash
# 使用局域网模式启动
npx expo start --lan
```

### 方案3: 使用隧道模式
```bash
# 使用隧道模式（适用于网络限制环境）
npx expo start --tunnel
```

### 方案4: 手动输入URL
1. 在EXPO开发服务器控制台查看IP地址
2. 在雷电模拟器的Expo Go中手动输入URL
3. 格式：`exp://192.168.x.x:19000`

## 🔍 常见问题检查

### 1. 网络连接检查
- ✅ 确保电脑和雷电模拟器在同一网络
- ✅ 检查防火墙是否阻止了19000端口
- ✅ 确保没有VPN干扰连接

### 2. 雷电模拟器设置
- ✅ 确保雷电模拟器网络设置为"桥接模式"
- ✅ 重启雷电模拟器
- ✅ 确保Expo Go应用是最新版本

### 3. EXPO环境检查
```bash
# 检查EXPO CLI版本
npx expo --version

# 更新EXPO CLI
npm install -g @expo/cli@latest

# 检查项目依赖
npm install
```

## 📱 操作步骤

### 步骤1: 重启EXPO服务器
1. 在命令行按 `Ctrl + C` 停止当前服务器
2. 运行: `npx expo start --clear`
3. 等待服务器启动完成

### 步骤2: 在雷电模拟器中操作
1. 打开雷电模拟器
2. 启动 Expo Go 应用
3. 选择"Scan QR Code"
4. 扫描EXPO开发服务器显示的二维码

### 步骤3: 如果仍然失败
1. 尝试手动输入URL
2. 在Expo Go中选择"Enter URL manually"
3. 输入显示的开发服务器地址

## 🛠️ 高级故障排除

### 清理所有缓存
```bash
# 清理npm缓存
npm cache clean --force

# 清理EXPO缓存
npx expo install --fix

# 删除node_modules重新安装
rmdir /s node_modules
npm install
```

### 检查端口占用
```bash
# Windows检查端口
netstat -ano | findstr :19000

# 如果端口被占用，结束进程
taskkill /PID <进程ID> /F
```

### 重置网络设置
1. 重启路由器
2. 重启电脑网络适配器
3. 重启雷电模拟器

## 📞 紧急解决方案

如果以上方法都无效，请尝试：

1. **使用真机测试**
   - 在手机上安装Expo Go
   - 确保手机和电脑在同一WiFi网络
   - 扫描二维码连接

2. **使用Web版本**
   ```bash
   npx expo start --web
   ```

3. **构建APK文件**
   ```bash
   npx expo build:android
   ```

## 🎯 成功连接的标志

当连接成功时，您应该看到：
- ✅ 雷电模拟器显示应用加载界面
- ✅ EXPO开发服务器显示"Connected to device"
- ✅ 应用正常启动并显示首页

## 📋 预防措施

为避免将来出现连接问题：
1. 定期更新Expo Go应用
2. 保持EXPO CLI为最新版本
3. 确保网络环境稳定
4. 定期清理开发缓存

---

**如果问题仍然存在，请提供更详细的错误信息以便进一步诊断。**
