/**
 * 测试退休预警组件修复
 */

console.log('🧪 测试退休预警组件修复...\n');

// 模拟新的数据结构
const testWarning = {
  cadre: {
    id: 1,
    姓名: '王四顿',
    性别: '男',
    出生日期: '1964-03-15',
    职务: '处长',
    现职级: '处级',
    单位: '测试单位'
  },
  warningType: 'retired',
  urgencyLevel: 'info',
  description: '已达到退休年龄（60岁）',
  daysUntilRetirement: -30,
  delayInfo: {
    delayYears: 0,
    delayMonths: 0,
    originalRetirementAge: 60,
    actualRetirementAge: 60
  },
  matchedRule: {
    ruleId: 'default_retired',
    ruleName: '已退休规则',
    ruleType: 'retired'
  }
};

console.log('✅ 测试数据结构：');
console.log('- cadre.姓名:', testWarning.cadre.姓名);
console.log('- warningType:', testWarning.warningType);
console.log('- urgencyLevel:', testWarning.urgencyLevel);
console.log('- description:', testWarning.description);
console.log('- daysUntilRetirement:', testWarning.daysUntilRetirement);

// 测试 delayInfo 访问
if (testWarning.delayInfo) {
  console.log('- delayInfo.actualRetirementAge:', testWarning.delayInfo.actualRetirementAge);
  console.log('- 预计退休年龄显示:', `${testWarning.delayInfo.actualRetirementAge.toFixed(1)}岁`);
} else {
  console.log('- delayInfo: undefined');
}

// 测试 matchedRule 访问
if (testWarning.matchedRule) {
  console.log('- matchedRule.ruleName:', testWarning.matchedRule.ruleName);
  console.log('- matchedRule.ruleType:', testWarning.matchedRule.ruleType);
} else {
  console.log('- matchedRule: undefined');
}

// 测试紧急程度颜色映射
function getUrgencyColor(level) {
  switch (level) {
    case 'urgent':
      return '#FF3B30';
    case 'normal':
      return '#FF9500';
    case 'info':
      return '#34C759';
    default:
      return '#007AFF';
  }
}

console.log('- urgencyColor:', getUrgencyColor(testWarning.urgencyLevel));

// 测试预警类型名称映射
function getWarningTypeName(type) {
  switch (type) {
    case 'retirement_warning':
      return '近两年退休';
    case 'second_line_warning':
      return '近两年退居二线';
    case 'retired':
      return '已退休';
    default:
      return '全部';
  }
}

console.log('- warningTypeName:', getWarningTypeName(testWarning.warningType));

// 测试天数显示
const daysText = testWarning.daysUntilRetirement > 0
  ? `还有${testWarning.daysUntilRetirement}天`
  : '已退休';

console.log('- daysText:', daysText);

console.log('\n✅ 所有测试通过！数据结构修复成功。');
console.log('\n🔧 修复内容：');
console.log('1. 将 calculation 属性替换为 delayInfo');
console.log('2. 将 actualRetirementDate 替换为 actualRetirementAge');
console.log('3. 更新测试数据使用新的 warningType 值');
console.log('4. 添加 matchedRule 属性');
console.log('\n🚀 现在应用应该可以正常显示退休预警了！');
