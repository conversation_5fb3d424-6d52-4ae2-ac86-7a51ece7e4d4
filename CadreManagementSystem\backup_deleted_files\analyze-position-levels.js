const sqlite3 = require('sqlite3').verbose();
const path = require('path');

console.log('🔍 分析数据库中的职级情况...\n');

// 数据库路径
const dbPath = path.join(__dirname, 'cadre_management.db');

const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ 连接数据库失败:', err.message);
    return;
  }
  console.log('✅ 成功连接到数据库');
});

// 首先查看数据库中的所有表
db.all("SELECT name FROM sqlite_master WHERE type='table'", [], (err, tables) => {
  if (err) {
    console.error('❌ 查询表列表失败:', err.message);
    return;
  }

  console.log('📋 数据库中的表：');
  tables.forEach(table => {
    console.log(`  - ${table.name}`);
  });
  console.log('');

  // 查看干部表的结构
  if (tables.some(t => t.name === 'cadres')) {
    db.all("PRAGMA table_info(cadres)", [], (err, columns) => {
      if (err) {
        console.error('❌ 查询cadres表结构失败:', err.message);
        return;
      }

      console.log('🔧 cadres表结构：');
      columns.forEach(col => {
        console.log(`  ${col.name} (${col.type})`);
      });
      console.log('');
    });
  }
});

// 1. 查看干部总数
db.get("SELECT COUNT(*) as total FROM cadres", [], (err, row) => {
  if (err) {
    console.error('❌ 查询干部总数失败:', err.message);
    return;
  }
  console.log(`📊 数据库中共有 ${row.total} 名干部\n`);
});

// 2. 统计所有不同的现职级及其人数
console.log('📋 现职级统计：');
console.log('═'.repeat(60));

db.all(`
  SELECT 现职级, COUNT(*) as count 
  FROM cadres 
  WHERE 现职级 IS NOT NULL AND 现职级 != '' 
  GROUP BY 现职级 
  ORDER BY count DESC, 现职级
`, [], (err, rows) => {
  if (err) {
    console.error('❌ 查询职级统计失败:', err.message);
    return;
  }
  
  console.log('序号 | 职级名称                    | 人数');
  console.log('-'.repeat(60));
  
  let totalWithPosition = 0;
  rows.forEach((row, index) => {
    console.log(`${(index + 1).toString().padStart(2)} | ${row.现职级.padEnd(25)} | ${row.count.toString().padStart(4)}`);
    totalWithPosition += row.count;
  });
  
  console.log('-'.repeat(60));
  console.log(`总计：${rows.length} 个不同职级，${totalWithPosition} 人有职级信息\n`);
  
  // 3. 查看没有职级信息的干部
  db.get(`
    SELECT COUNT(*) as count 
    FROM cadres 
    WHERE 现职级 IS NULL OR 现职级 = ''
  `, [], (err, row) => {
    if (err) {
      console.error('❌ 查询无职级干部失败:', err.message);
      return;
    }
    
    if (row.count > 0) {
      console.log(`⚠️  有 ${row.count} 名干部没有职级信息\n`);
    }
  });
});

// 4. 查看职级管理表中的配置
console.log('🔧 职级管理表配置：');
console.log('═'.repeat(80));

db.all(`
  SELECT id, name, level, category, retirement_age_male, retirement_age_female, description, is_active
  FROM position_levels_new 
  ORDER BY category, sort_order, level DESC
`, [], (err, rows) => {
  if (err) {
    console.error('❌ 查询职级管理表失败:', err.message);
    return;
  }
  
  console.log('ID | 职级名称           | 等级 | 类别 | 男退休 | 女退休 | 状态 | 说明');
  console.log('-'.repeat(80));
  
  rows.forEach(row => {
    const status = row.is_active ? '启用' : '禁用';
    const category = row.category === 'administrative' ? '行政' : 
                    row.category === 'technical' ? '技术' : '工人';
    console.log(`${row.id.toString().padStart(2)} | ${row.name.padEnd(15)} | ${row.level.toString().padStart(2)} | ${category.padEnd(2)} | ${(row.retirement_age_male || '').toString().padStart(4)} | ${(row.retirement_age_female || '').toString().padStart(4)} | ${status} | ${row.description || ''}`);
  });
  
  console.log(`\n职级管理表中共有 ${rows.length} 个职级配置\n`);
});

// 5. 检查哪些职级正在被使用（针对要删除的职级）
console.log('🔍 检查职级使用情况：');
console.log('═'.repeat(60));

// 查看"中层正职"和"中层副职"的使用情况
const checkPositions = ['中层正职', '中层副职'];

checkPositions.forEach(position => {
  db.all(`
    SELECT 姓名, 单位, 职务, 现职级 
    FROM cadres 
    WHERE 现职级 = ? 
    ORDER BY 单位, 姓名
  `, [position], (err, rows) => {
    if (err) {
      console.error(`❌ 查询${position}使用情况失败:`, err.message);
      return;
    }
    
    console.log(`\n📌 职级"${position}"正在被 ${rows.length} 名干部使用：`);
    if (rows.length > 0) {
      console.log('姓名     | 单位                 | 职务');
      console.log('-'.repeat(50));
      rows.forEach(row => {
        console.log(`${row.姓名.padEnd(8)} | ${(row.单位 || '').padEnd(18)} | ${row.职务 || ''}`);
      });
    } else {
      console.log('✅ 该职级未被使用，可以安全删除');
    }
  });
});

// 6. 分析退休预警相关数据
setTimeout(() => {
  console.log('\n🎯 退休预警相关分析：');
  console.log('═'.repeat(60));
  
  // 查看1965年出生的干部
  db.all(`
    SELECT 姓名, 性别, 出生日期, 现职级, 单位
    FROM cadres 
    WHERE 出生日期 LIKE '1965%'
    ORDER BY 出生日期
  `, [], (err, rows) => {
    if (err) {
      console.error('❌ 查询1965年出生干部失败:', err.message);
      return;
    }
    
    console.log(`\n📅 1965年出生的干部（${rows.length}人）：`);
    console.log('姓名     | 性别 | 出生日期   | 现职级           | 单位');
    console.log('-'.repeat(70));
    rows.forEach(row => {
      console.log(`${row.姓名.padEnd(8)} | ${row.性别.padEnd(2)} | ${row.出生日期.padEnd(10)} | ${(row.现职级 || '').padEnd(15)} | ${row.单位 || ''}`);
    });
  });
  
  // 查看王张荣的具体信息
  db.get(`
    SELECT * FROM cadres WHERE 姓名 = '王张荣'
  `, [], (err, row) => {
    if (err) {
      console.error('❌ 查询王张荣信息失败:', err.message);
      return;
    }
    
    if (row) {
      console.log(`\n👤 王张荣详细信息：`);
      console.log(`姓名: ${row.姓名}`);
      console.log(`性别: ${row.性别}`);
      console.log(`出生日期: ${row.出生日期}`);
      console.log(`现职级: ${row.现职级}`);
      console.log(`单位: ${row.单位}`);
      console.log(`职务: ${row.职务}`);
      console.log(`退休状态: ${row.退休状态}`);
      
      // 计算退休时间
      if (row.出生日期) {
        const birthDate = new Date(row.出生日期);
        const today = new Date();
        const age = today.getFullYear() - birthDate.getFullYear();
        const monthDiff = today.getMonth() - birthDate.getMonth();
        const dayDiff = today.getDate() - birthDate.getDate();
        
        console.log(`当前年龄: ${age}岁${monthDiff}个月${dayDiff}天`);
        
        // 计算到60岁的时间
        const retirement60 = new Date(birthDate);
        retirement60.setFullYear(retirement60.getFullYear() + 60);
        const daysTo60 = Math.ceil((retirement60 - today) / (1000 * 60 * 60 * 24));
        console.log(`距离60岁还有: ${daysTo60}天`);
        
        // 1965年10月出生应延迟3个月
        const delayedRetirement = new Date(retirement60);
        delayedRetirement.setMonth(delayedRetirement.getMonth() + 3);
        const daysToDelayed = Math.ceil((delayedRetirement - today) / (1000 * 60 * 60 * 24));
        console.log(`延迟3个月后退休时间: ${delayedRetirement.toLocaleDateString()}`);
        console.log(`距离延迟退休还有: ${daysToDelayed}天`);
      }
    } else {
      console.log('❌ 未找到王张荣的信息');
    }
  });
  
  // 统计近两年退休人员
  db.all(`
    SELECT 姓名, 性别, 出生日期, 现职级
    FROM cadres 
    WHERE 出生日期 IS NOT NULL AND 出生日期 != ''
    ORDER BY 出生日期
  `, [], (err, rows) => {
    if (err) {
      console.error('❌ 查询退休人员失败:', err.message);
      return;
    }
    
    const today = new Date();
    const twoYearsLater = new Date();
    twoYearsLater.setFullYear(twoYearsLater.getFullYear() + 2);
    
    let nearRetirementCount = 0;
    const nearRetirementList = [];
    
    rows.forEach(row => {
      if (row.出生日期) {
        const birthDate = new Date(row.出生日期);
        const retirementAge = row.性别 === '女' ? 55 : 60;
        const retirementDate = new Date(birthDate);
        retirementDate.setFullYear(retirementDate.getFullYear() + retirementAge);
        
        // 检查是否在近两年内退休
        if (retirementDate <= twoYearsLater && retirementDate >= today) {
          nearRetirementCount++;
          nearRetirementList.push({
            name: row.姓名,
            gender: row.性别,
            birthDate: row.出生日期,
            position: row.现职级,
            retirementDate: retirementDate.toLocaleDateString()
          });
        }
      }
    });
    
    console.log(`\n📊 近两年退休人员统计: ${nearRetirementCount}人`);
    if (nearRetirementList.length > 0) {
      console.log('姓名     | 性别 | 出生日期   | 预计退休日期 | 现职级');
      console.log('-'.repeat(65));
      nearRetirementList.forEach(person => {
        console.log(`${person.name.padEnd(8)} | ${person.gender.padEnd(2)} | ${person.birthDate.padEnd(10)} | ${person.retirementDate.padEnd(12)} | ${person.position || ''}`);
      });
    }
  });
  
  // 关闭数据库连接
  setTimeout(() => {
    db.close((err) => {
      if (err) {
        console.error('❌ 关闭数据库失败:', err.message);
      } else {
        console.log('\n✅ 数据库分析完成');
      }
    });
  }, 2000);
}, 1000);
