# Simple retirement calculation test

Write-Host "=== Retirement Calculation Test ===" -ForegroundColor Green

# Test cases
$testCases = @(
    @{ Name = "Li Caimao"; BirthYear = 1965; Gender = "Male"; Expected = 3 },
    @{ Name = "Wu Haizhu"; BirthYear = 1966; Gender = "Female"; Expected = 5 },
    @{ Name = "Wang Sidun"; BirthYear = 1964; Gender = "Male"; Expected = 0 }
)

# Delay calculation function
function Calculate-Delay {
    param([int]$BirthYear)
    
    if ($BirthYear -le 1964) { return 0 }
    
    $totalMonths = 0
    
    if ($BirthYear -eq 1965) {
        $totalMonths = 3
    } elseif ($BirthYear -ge 1966 -and $BirthYear -le 1970) {
        $totalMonths = 3
        $extraYears = $BirthYear - 1965
        $extraMonths = [Math]::Min($extraYears * 2, 12)
        $totalMonths += $extraMonths
    }
    
    return [Math]::Min($totalMonths, 60)
}

# Run tests
foreach ($test in $testCases) {
    Write-Host "Testing: $($test.Name)" -ForegroundColor Cyan
    Write-Host "Birth Year: $($test.BirthYear)"
    
    $delay = Calculate-Delay -BirthYear $test.BirthYear
    
    Write-Host "Calculated delay: $delay months"
    Write-Host "Expected delay: $($test.Expected) months"
    
    if ($delay -eq $test.Expected) {
        Write-Host "PASS" -ForegroundColor Green
    } else {
        Write-Host "FAIL" -ForegroundColor Red
    }
    
    Write-Host "---"
}

Write-Host "Test completed!" -ForegroundColor Green
