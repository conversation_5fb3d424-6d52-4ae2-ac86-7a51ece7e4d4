import { getDatabase } from './database';
import { PositionLevel } from '../types';

export class PositionLevelDao {
  // 获取所有职级
  static async getAllPositionLevels(): Promise<PositionLevel[]> {
    const db = getDatabase();
    
    try {
      const result = await db.getAllAsync(`
        SELECT * FROM position_levels_new
        WHERE is_active = 1
        ORDER BY category, sort_order, level DESC
      `);
      
      return result as PositionLevel[];
    } catch (error) {
      console.error('获取职级列表失败:', error);
      throw new Error(`获取职级列表失败: ${error}`);
    }
  }

  // 根据类别获取职级
  static async getPositionLevelsByCategory(category: string): Promise<PositionLevel[]> {
    const db = getDatabase();
    
    try {
      const result = await db.getAllAsync(`
        SELECT * FROM position_levels_new
        WHERE category = ? AND is_active = 1
        ORDER BY sort_order, level DESC
      `, [category]);
      
      return result as PositionLevel[];
    } catch (error) {
      console.error('获取职级列表失败:', error);
      throw new Error(`获取职级列表失败: ${error}`);
    }
  }

  // 添加职级
  static async addPositionLevel(positionLevel: PositionLevel): Promise<number> {
    const db = getDatabase();
    
    try {
      const result = await db.runAsync(`
        INSERT INTO position_levels_new (
          name, level, category, retirement_age_male, retirement_age_female,
          description, is_active, sort_order
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        positionLevel.name,
        positionLevel.level,
        positionLevel.category,
        positionLevel.retirement_age_male || null,
        positionLevel.retirement_age_female || null,
        positionLevel.description || '',
        positionLevel.is_active ? 1 : 0,
        positionLevel.sort_order
      ]);
      
      console.log('职级添加成功:', positionLevel.name, 'ID:', result.lastInsertRowId);
      return result.lastInsertRowId;
    } catch (error) {
      console.error('添加职级失败:', error);
      throw new Error(`添加职级失败: ${error}`);
    }
  }

  // 更新职级
  static async updatePositionLevel(positionLevel: PositionLevel): Promise<void> {
    const db = getDatabase();
    
    try {
      await db.runAsync(`
        UPDATE position_levels_new SET
          name = ?, level = ?, category = ?, retirement_age_male = ?,
          retirement_age_female = ?, description = ?, is_active = ?,
          sort_order = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `, [
        positionLevel.name,
        positionLevel.level,
        positionLevel.category,
        positionLevel.retirement_age_male || null,
        positionLevel.retirement_age_female || null,
        positionLevel.description || '',
        positionLevel.is_active ? 1 : 0,
        positionLevel.sort_order,
        positionLevel.id
      ]);
      
      console.log('职级更新成功:', positionLevel.name);
    } catch (error) {
      console.error('更新职级失败:', error);
      throw new Error(`更新职级失败: ${error}`);
    }
  }

  // 删除职级（软删除）
  static async deletePositionLevel(id: number, force: boolean = false): Promise<void> {
    const db = getDatabase();

    try {
      // 获取职级名称
      const position = await this.getPositionLevelById(id);
      if (!position) {
        throw new Error('职级不存在');
      }

      // 检查是否有干部使用此职级
      const cadreCount = await db.getFirstAsync(`
        SELECT COUNT(*) as count FROM cadres WHERE 现职级 = ?
      `, [position.name]);

      if (cadreCount && (cadreCount as any).count > 0 && !force) {
        const usageDetails = await this.getPositionUsageDetails(position.name);
        const errorMessage = `该职级正在被 ${(cadreCount as any).count} 名干部使用，无法删除。\n使用人员：${usageDetails.slice(0, 5).map(u => u.姓名).join('、')}${usageDetails.length > 5 ? '等' : ''}`;
        throw new Error(errorMessage);
      }

      await db.runAsync(`
        UPDATE position_levels_new SET is_active = 0, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `, [id]);

      console.log('职级删除成功:', id);
    } catch (error) {
      console.error('删除职级失败:', error);
      throw error;
    }
  }

  // 获取职级使用情况详情
  static async getPositionUsageDetails(positionName: string): Promise<Array<{姓名: string, 单位: string, 职务: string}>> {
    const db = getDatabase();

    try {
      const result = await db.getAllAsync(`
        SELECT 姓名, 单位, 职务 FROM cadres
        WHERE 现职级 = ?
        ORDER BY 单位, 姓名
      `, [positionName]);

      return result as Array<{姓名: string, 单位: string, 职务: string}>;
    } catch (error) {
      console.error('获取职级使用详情失败:', error);
      throw new Error(`获取职级使用详情失败: ${error}`);
    }
  }

  // 批量替换职级
  static async batchReplacePosition(oldPosition: string, newPosition: string): Promise<number> {
    const db = getDatabase();

    try {
      console.log(`🔄 开始批量替换职级：${oldPosition} -> ${newPosition}`);

      const result = await db.runAsync(
        'UPDATE cadres SET 现职级 = ? WHERE 现职级 = ?',
        [newPosition, oldPosition]
      );

      console.log(`✅ 批量替换职级完成，影响${result.changes}条记录`);
      return result.changes;
    } catch (error) {
      console.error('批量替换职级失败:', error);
      throw new Error(`批量替换职级失败: ${error}`);
    }
  }

  // 强制删除职级（会将使用该职级的干部职级设为空）
  static async forceDeletePositionLevel(id: number): Promise<number> {
    const db = getDatabase();

    try {
      // 获取职级名称
      const position = await this.getPositionLevelById(id);
      if (!position) {
        throw new Error('职级不存在');
      }

      // 先将使用该职级的干部职级设为空
      const updateResult = await db.runAsync(
        'UPDATE cadres SET 现职级 = NULL WHERE 现职级 = ?',
        [position.name]
      );

      // 然后删除职级
      await db.runAsync(`
        UPDATE position_levels_new SET is_active = 0, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `, [id]);

      console.log(`强制删除职级成功: ${position.name}，影响${updateResult.changes}名干部`);
      return updateResult.changes;
    } catch (error) {
      console.error('强制删除职级失败:', error);
      throw error;
    }
  }

  // 获取所有现职级统计（从干部表中）
  static async getCurrentPositionStatistics(): Promise<Array<{现职级: string, count: number}>> {
    const db = getDatabase();

    try {
      const result = await db.getAllAsync(`
        SELECT 现职级, COUNT(*) as count
        FROM cadres
        WHERE 现职级 IS NOT NULL AND 现职级 != ''
        GROUP BY 现职级
        ORDER BY count DESC, 现职级
      `);

      return result as Array<{现职级: string, count: number}>;
    } catch (error) {
      console.error('获取现职级统计失败:', error);
      throw new Error(`获取现职级统计失败: ${error}`);
    }
  }

  // 获取职级详情
  static async getPositionLevelById(id: number): Promise<PositionLevel | null> {
    const db = getDatabase();
    
    try {
      const result = await db.getFirstAsync(`
        SELECT * FROM position_levels_new WHERE id = ?
      `, [id]);
      
      return result as PositionLevel || null;
    } catch (error) {
      console.error('获取职级详情失败:', error);
      throw new Error(`获取职级详情失败: ${error}`);
    }
  }

  // 根据名称获取职级
  static async getPositionLevelByName(name: string): Promise<PositionLevel | null> {
    const db = getDatabase();
    
    try {
      const result = await db.getFirstAsync(`
        SELECT * FROM position_levels_new WHERE name = ? AND is_active = 1
      `, [name]);
      
      return result as PositionLevel || null;
    } catch (error) {
      console.error('获取职级详情失败:', error);
      throw new Error(`获取职级详情失败: ${error}`);
    }
  }

  // 批量初始化默认职级
  static async initializeDefaultPositionLevels(): Promise<void> {
    const db = getDatabase();
    
    const defaultPositions = [
      // 行政职级
      { name: '正部级', level: 10, category: 'administrative', retirement_age_male: 65, retirement_age_female: 60, sort_order: 1 },
      { name: '副部级', level: 9, category: 'administrative', retirement_age_male: 65, retirement_age_female: 60, sort_order: 2 },
      { name: '正厅级', level: 8, category: 'administrative', retirement_age_male: 60, retirement_age_female: 55, sort_order: 3 },
      { name: '副厅级', level: 7, category: 'administrative', retirement_age_male: 60, retirement_age_female: 55, sort_order: 4 },
      { name: '正处级', level: 6, category: 'administrative', retirement_age_male: 60, retirement_age_female: 55, sort_order: 5 },
      { name: '副处级', level: 5, category: 'administrative', retirement_age_male: 60, retirement_age_female: 55, sort_order: 6 },
      { name: '正科级', level: 4, category: 'administrative', retirement_age_male: 60, retirement_age_female: 55, sort_order: 7 },
      { name: '副科级', level: 3, category: 'administrative', retirement_age_male: 60, retirement_age_female: 55, sort_order: 8 },
      { name: '科员', level: 2, category: 'administrative', retirement_age_male: 60, retirement_age_female: 55, sort_order: 9 },
      { name: '办事员', level: 1, category: 'administrative', retirement_age_male: 60, retirement_age_female: 55, sort_order: 10 },
      
      // 技术职级
      { name: '正高级', level: 8, category: 'technical', retirement_age_male: 65, retirement_age_female: 60, sort_order: 11 },
      { name: '副高级', level: 7, category: 'technical', retirement_age_male: 60, retirement_age_female: 55, sort_order: 12 },
      { name: '中级', level: 6, category: 'technical', retirement_age_male: 60, retirement_age_female: 55, sort_order: 13 },
      { name: '初级', level: 5, category: 'technical', retirement_age_male: 60, retirement_age_female: 55, sort_order: 14 },
      
      // 工人职级
      { name: '高级技师', level: 5, category: 'worker', retirement_age_male: 60, retirement_age_female: 50, sort_order: 15 },
      { name: '技师', level: 4, category: 'worker', retirement_age_male: 60, retirement_age_female: 50, sort_order: 16 },
      { name: '高级工', level: 3, category: 'worker', retirement_age_male: 60, retirement_age_female: 50, sort_order: 17 },
      { name: '中级工', level: 2, category: 'worker', retirement_age_male: 60, retirement_age_female: 50, sort_order: 18 },
      { name: '初级工', level: 1, category: 'worker', retirement_age_male: 60, retirement_age_female: 50, sort_order: 19 },
    ];

    try {
      await db.withTransactionAsync(async () => {
        for (const position of defaultPositions) {
          // 检查是否已存在
          const existing = await db.getFirstAsync(`
            SELECT id FROM position_levels_new WHERE name = ?
          `, [position.name]);

          if (!existing) {
            await db.runAsync(`
              INSERT INTO position_levels_new (
                name, level, category, retirement_age_male, retirement_age_female,
                description, is_active, sort_order
              ) VALUES (?, ?, ?, ?, ?, ?, 1, ?)
            `, [
              position.name,
              position.level,
              position.category,
              position.retirement_age_male,
              position.retirement_age_female,
              `${position.category === 'administrative' ? '行政' : position.category === 'technical' ? '技术' : '工人'}职级`,
              position.sort_order
            ]);
          }
        }
      });
      
      console.log('默认职级数据初始化成功');
    } catch (error) {
      console.error('初始化默认职级失败:', error);
      throw new Error(`初始化默认职级失败: ${error}`);
    }
  }
}
