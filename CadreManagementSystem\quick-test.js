/**
 * 快速功能测试脚本
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 快速功能测试开始...');
console.log('');

// 测试1: 检查关键文件是否存在
console.log('📋 测试1: 检查关键文件');
const keyFiles = [
  '0606.xlsx',
  'src/utils/configurableRetirementCalculator.ts',
  'src/utils/retirementCalculator.ts',
  'src/utils/excelImporter.ts',
  'package.json'
];

keyFiles.forEach(file => {
  const exists = fs.existsSync(path.join(__dirname, file));
  console.log(`${exists ? '✅' : '❌'} ${file}`);
});
console.log('');

// 测试2: 检查女性退休年龄修复
console.log('📋 测试2: 检查女性退休年龄修复');
try {
  const configCalculatorPath = path.join(__dirname, 'src/utils/configurableRetirementCalculator.ts');
  const configContent = fs.readFileSync(configCalculatorPath, 'utf8');
  
  if (configContent.includes('return 55;') && configContent.includes('干部管理系统中所有女性都按干部标准')) {
    console.log('✅ configurableRetirementCalculator.ts 已修复');
  } else {
    console.log('❌ configurableRetirementCalculator.ts 需要修复');
  }
  
  const retirementCalculatorPath = path.join(__dirname, 'src/utils/retirementCalculator.ts');
  const retirementContent = fs.readFileSync(retirementCalculatorPath, 'utf8');
  
  if (retirementContent.includes("return 'cadre';") && retirementContent.includes('干部管理系统中所有女性都按干部标准')) {
    console.log('✅ retirementCalculator.ts 已修复');
  } else {
    console.log('❌ retirementCalculator.ts 需要修复');
  }
} catch (error) {
  console.log('❌ 文件读取失败:', error.message);
}
console.log('');

// 测试3: 检查清理效果
console.log('📋 测试3: 检查清理效果');
const backupExists = fs.existsSync(path.join(__dirname, 'backup_deleted_files'));
console.log(`${backupExists ? '✅' : '❌'} 备份目录存在`);

if (backupExists) {
  const backupFiles = fs.readdirSync(path.join(__dirname, 'backup_deleted_files'));
  console.log(`✅ 已备份 ${backupFiles.length} 个文件`);
}
console.log('');

// 测试4: 检查依赖包
console.log('📋 测试4: 检查依赖包');
try {
  const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, 'package.json'), 'utf8'));
  const deps = packageJson.dependencies || {};
  
  const requiredDeps = ['expo', 'expo-sqlite', 'xlsx', 'react-native'];
  requiredDeps.forEach(dep => {
    console.log(`${deps[dep] ? '✅' : '❌'} ${dep}: ${deps[dep] || '未安装'}`);
  });
} catch (error) {
  console.log('❌ package.json 读取失败');
}
console.log('');

console.log('🎯 快速测试完成！');
console.log('');
console.log('📱 下一步: 启动应用进行实际测试');
console.log('1. 确保EXPO服务器正在运行');
console.log('2. 在雷电模拟器中打开Expo Go');
console.log('3. 扫描二维码连接应用');
console.log('4. 测试Excel导入和退休预警功能');
