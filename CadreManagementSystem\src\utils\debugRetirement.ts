/**
 * 调试退休预警功能
 */

import { CadreDao } from '../database/cadreDao';
import { RetirementCalculator } from './retirementCalculator';

export class DebugRetirement {
  
  /**
   * 调试数据库中的干部数据
   */
  static async debugCadreData() {
    try {
      console.log('=== 开始调试干部数据 ===');
      
      // 获取所有干部数据
      const cadres = await CadreDao.getAllCadres();
      console.log(`总共获取到 ${cadres.length} 条干部数据`);
      
      if (cadres.length === 0) {
        console.log('❌ 数据库中没有干部数据');
        return;
      }
      
      // 专门测试王四顿和许国泰
      console.log('\n=== 专门测试王四顿和许国泰 ===');
      const testNames = ['王四顿', '许国泰'];

      for (const testName of testNames) {
        const testCadre = cadres.find(c => c.姓名 === testName);
        if (testCadre) {
          console.log(`\n🎯 找到测试对象: ${testName}`);
          this.debugSingleCadre(testCadre);
        } else {
          console.log(`\n❌ 未找到: ${testName}`);
        }
      }

      // 检查前5条数据的关键字段
      console.log('\n=== 检查前5条数据的关键字段 ===');
      const sampleCadres = cadres.slice(0, 5);

      sampleCadres.forEach((cadre, index) => {
        console.log(`\n--- 第${index + 1}条数据 ---`);
        this.debugSingleCadre(cadre);
      });

      // 统计预警情况
      console.log('\n=== 预警统计 ===');
      const warnings = RetirementCalculator.generateWarnings(cadres);
      console.log(`总预警数: ${warnings.length}`);

      const stats = {
        monthly: warnings.filter(w => w.warningType === 'monthly_warning').length,
        twoYear: warnings.filter(w => w.warningType === 'two_year_warning').length,
        secondLine: warnings.filter(w => w.warningType === 'second_line_warning').length,
        retired: warnings.filter(w => w.warningType === 'retired').length,
      };

      console.log(`月度预警: ${stats.monthly}人`);
      console.log(`近两年退休: ${stats.twoYear}人`);
      console.log(`退居二线: ${stats.secondLine}人`);
      console.log(`已退休: ${stats.retired}人`);
      
      // 统计出生日期字段情况
      console.log('\n=== 出生日期字段统计 ===');
      let validBirthDates = 0;
      let emptyBirthDates = 0;
      let invalidBirthDates = 0;
      
      cadres.forEach(cadre => {
        if (!cadre.出生日期) {
          emptyBirthDates++;
        } else {
          const calculation = RetirementCalculator.calculateRetirement(cadre);
          if (calculation) {
            validBirthDates++;
          } else {
            invalidBirthDates++;
            console.log(`无效出生日期: ${cadre.姓名} - ${cadre.出生日期}`);
          }
        }
      });
      
      console.log(`有效出生日期: ${validBirthDates}条`);
      console.log(`空出生日期: ${emptyBirthDates}条`);
      console.log(`无效出生日期: ${invalidBirthDates}条`);
      
      // 生成预警信息
      console.log('\n=== 生成预警信息 ===');
      const warnings = RetirementCalculator.generateWarnings(cadres);
      console.log(`生成预警信息: ${warnings.length}条`);
      
      if (warnings.length > 0) {
        console.log('\n预警类型统计:');
        const warningStats = warnings.reduce((stats, warning) => {
          stats[warning.warningType] = (stats[warning.warningType] || 0) + 1;
          return stats;
        }, {} as Record<string, number>);
        
        Object.entries(warningStats).forEach(([type, count]) => {
          console.log(`  ${type}: ${count}条`);
        });
        
        // 显示前3条预警详情
        console.log('\n前3条预警详情:');
        warnings.slice(0, 3).forEach((warning, index) => {
          console.log(`\n${index + 1}. ${warning.cadre.姓名}`);
          console.log(`   类型: ${warning.warningType}`);
          console.log(`   紧急程度: ${warning.urgencyLevel}`);
          console.log(`   描述: ${warning.description}`);
          console.log(`   距离退休: ${warning.daysUntilRetirement}天`);
        });
      } else {
        console.log('❌ 没有生成任何预警信息');
      }
      
      console.log('\n=== 调试完成 ===');
      
    } catch (error) {
      console.error('调试过程中出错:', error);
    }
  }
  
  /**
   * 测试退休计算算法
   */
  static testRetirementCalculation() {
    console.log('\n=== 测试退休计算算法 ===');
    
    // 测试数据
    const testCases = [
      {
        姓名: '测试男性1965年',
        性别: '男',
        出生日期: '1965-08-15',
        现职级: '处级'
      },
      {
        姓名: '测试男性1970年',
        性别: '男',
        出生日期: '1970-03-20',
        现职级: '科级'
      },
      {
        姓名: '测试女性1968年干部',
        性别: '女',
        出生日期: '1968-12-10',
        现职级: '处级'
      },
      {
        姓名: '测试女性1975年工人',
        性别: '女',
        出生日期: '1975-06-05',
        现职级: '工人'
      }
    ];
    
    testCases.forEach(testCase => {
      console.log(`\n--- 测试: ${testCase.姓名} ---`);
      const calculation = RetirementCalculator.calculateRetirement(testCase);
      
      if (calculation) {
        console.log(`✅ 计算成功:`);
        console.log(`  原退休年龄: ${calculation.originalRetirementAge}岁`);
        console.log(`  延迟月数: ${calculation.delayMonths}个月`);
        console.log(`  实际退休年龄: ${calculation.actualRetirementAge.toFixed(1)}岁`);
        console.log(`  预计退休日期: ${calculation.actualRetirementDate.toLocaleDateString()}`);
        console.log(`  距离退休: ${calculation.daysUntilRetirement}天`);
        console.log(`  说明: ${calculation.description}`);
        
        const warning = RetirementCalculator.generateWarning(testCase);
        if (warning) {
          console.log(`  预警类型: ${warning.warningType}`);
          console.log(`  紧急程度: ${warning.urgencyLevel}`);
          console.log(`  预警描述: ${warning.description}`);
        } else {
          console.log(`  无预警`);
        }
      } else {
        console.log(`❌ 计算失败`);
      }
    });
  }
}
