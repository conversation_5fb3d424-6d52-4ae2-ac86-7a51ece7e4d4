import { getDatabase } from './database';
import { CadreInfo } from '../types';

export type CadreStatus = 'active' | 'early_warning' | 'second_line' | 'second_line_retired' | 'retired' | 'delayed_retirement' | 'transferred';

export interface CadreStatusUpdate {
  id: number;
  status: CadreStatus;
  reason?: string;
  updateTime: string;
}

export class CadreStatusDao {
  
  /**
   * 更新干部退休状态
   */
  static async updateCadreStatus(cadreId: number, status: CadreStatus, reason?: string): Promise<void> {
    const db = getDatabase();
    
    try {
      await db.runAsync(
        'UPDATE cadres SET 退休状态 = ?, 更新时间 = CURRENT_TIMESTAMP WHERE id = ?',
        [status, cadreId]
      );
      
      // 记录状态变更日志
      await this.logStatusChange(cadreId, status, reason);
      
      console.log(`干部状态更新成功: ID=${cadreId}, 新状态=${status}`);
    } catch (error) {
      console.error('更新干部状态失败:', error);
      throw new Error(`更新干部状态失败: ${error}`);
    }
  }

  /**
   * 批量更新干部状态
   */
  static async batchUpdateCadreStatus(updates: CadreStatusUpdate[]): Promise<void> {
    const db = getDatabase();
    
    try {
      await db.withTransactionAsync(async () => {
        for (const update of updates) {
          await db.runAsync(
            'UPDATE cadres SET 退休状态 = ?, 更新时间 = CURRENT_TIMESTAMP WHERE id = ?',
            [update.status, update.id]
          );
          
          await this.logStatusChange(update.id, update.status, update.reason);
        }
      });
      
      console.log(`批量更新干部状态成功: ${updates.length}条记录`);
    } catch (error) {
      console.error('批量更新干部状态失败:', error);
      throw new Error(`批量更新干部状态失败: ${error}`);
    }
  }

  /**
   * 获取指定状态的干部列表
   */
  static async getCadresByStatus(status: CadreStatus): Promise<CadreInfo[]> {
    const db = getDatabase();
    
    try {
      const cadres = await db.getAllAsync<CadreInfo>(
        'SELECT * FROM cadres WHERE 退休状态 = ? ORDER BY 姓名',
        [status]
      );
      
      return cadres;
    } catch (error) {
      console.error('获取指定状态干部失败:', error);
      throw new Error(`获取指定状态干部失败: ${error}`);
    }
  }

  /**
   * 获取干部状态统计
   */
  static async getStatusStatistics(): Promise<Record<CadreStatus, number>> {
    const db = getDatabase();
    
    try {
      const stats = await db.getAllAsync<{ 退休状态: CadreStatus; count: number }>(
        'SELECT 退休状态, COUNT(*) as count FROM cadres GROUP BY 退休状态'
      );
      
      const result: Record<CadreStatus, number> = {
        active: 0,
        early_warning: 0,
        second_line: 0,
        second_line_retired: 0,
        retired: 0,
        delayed_retirement: 0,
        transferred: 0
      };
      
      stats.forEach(stat => {
        if (stat.退休状态 in result) {
          result[stat.退休状态] = stat.count;
        }
      });
      
      return result;
    } catch (error) {
      console.error('获取状态统计失败:', error);
      throw new Error(`获取状态统计失败: ${error}`);
    }
  }

  /**
   * 记录状态变更日志
   */
  private static async logStatusChange(cadreId: number, status: CadreStatus, reason?: string): Promise<void> {
    const db = getDatabase();
    
    try {
      // 创建状态变更日志表（如果不存在）
      await db.execAsync(`
        CREATE TABLE IF NOT EXISTS cadre_status_logs (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          cadre_id INTEGER NOT NULL,
          old_status TEXT,
          new_status TEXT NOT NULL,
          reason TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (cadre_id) REFERENCES cadres (id)
        )
      `);
      
      // 获取旧状态
      const oldStatusResult = await db.getFirstAsync<{ 退休状态: string }>(
        'SELECT 退休状态 FROM cadres WHERE id = ?',
        [cadreId]
      );
      
      // 插入日志记录
      await db.runAsync(
        'INSERT INTO cadre_status_logs (cadre_id, old_status, new_status, reason) VALUES (?, ?, ?, ?)',
        [cadreId, oldStatusResult?.退休状态 || 'unknown', status, reason || '']
      );
    } catch (error) {
      console.error('记录状态变更日志失败:', error);
      // 不抛出错误，避免影响主要功能
    }
  }

  /**
   * 获取干部的最新状态变更备注
   */
  static async getCadreLatestStatusReason(cadreId: number, status: CadreStatus): Promise<string | null> {
    const db = getDatabase();

    try {
      const result = await db.getFirstAsync<{ reason: string }>(
        'SELECT reason FROM cadre_status_logs WHERE cadre_id = ? AND new_status = ? ORDER BY created_at DESC LIMIT 1',
        [cadreId, status]
      );

      return result?.reason || null;
    } catch (error) {
      console.error('获取干部状态备注失败:', error);
      return null;
    }
  }

  /**
   * 获取需要提醒的干部（剩余1-3天）
   */
  static async getCadresNeedingReminder(): Promise<CadreInfo[]> {
    const db = getDatabase();

    try {
      // 这里需要结合退休预警计算逻辑
      // 暂时返回空数组，后续在退休预警组件中实现
      return [];
    } catch (error) {
      console.error('获取需要提醒的干部失败:', error);
      throw new Error(`获取需要提醒的干部失败: ${error}`);
    }
  }

  /**
   * 获取状态显示名称
   */
  static getStatusDisplayName(status: CadreStatus): string {
    const statusNames: Record<CadreStatus, string> = {
      active: '在职',
      early_warning: '预警',
      second_line: '退居二线',
      second_line_retired: '已退居二线',
      retired: '已退休',
      delayed_retirement: '延迟退休',
      transferred: '调动干部'
    };
    
    return statusNames[status] || '未知';
  }

  /**
   * 获取状态颜色
   */
  static getStatusColor(status: CadreStatus): string {
    const statusColors: Record<CadreStatus, string> = {
      active: '#34C759',      // 绿色 - 在职
      early_warning: '#FF9500', // 橙色 - 预警
      second_line: '#007AFF',   // 蓝色 - 退居二线
      second_line_retired: '#5AC8FA', // 浅蓝色 - 已退居二线
      retired: '#8E8E93',       // 灰色 - 已退休
      delayed_retirement: '#FF3B30', // 红色 - 延迟退休
      transferred: '#5856D6'    // 紫色 - 调动干部
    };
    
    return statusColors[status] || '#8E8E93';
  }
}
