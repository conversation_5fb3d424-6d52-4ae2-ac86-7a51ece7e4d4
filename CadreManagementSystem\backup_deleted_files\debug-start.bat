@echo off
echo === Expo Development Server Debug Start ===
echo.

REM Set Node.js path
set PATH=C:\Program Files\nodejs;%PATH%
echo PATH updated: %PATH%
echo.

REM Check Node.js installation
echo Checking Node.js...
node --version
if %errorlevel% neq 0 (
    echo ERROR: Node.js not found!
    pause
    exit /b 1
)

echo Checking npm...
npm --version
if %errorlevel% neq 0 (
    echo ERROR: npm not found!
    pause
    exit /b 1
)

REM Change to project directory
echo Changing to project directory...
cd /d "%~dp0"
echo Current directory: %CD%
echo.

REM Check if package.json exists
if not exist package.json (
    echo ERROR: package.json not found!
    pause
    exit /b 1
)

REM Check if node_modules exists
if not exist node_modules (
    echo Installing dependencies...
    npm install
)

REM Start Expo development server
echo Starting Expo server...
echo Command: npx expo start
npx expo start

echo.
echo Expo server stopped.
pause
