import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  TextInput,
  Modal,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useTheme } from '../src/contexts/ThemeContext';
import { DataClearDao } from '../src/database/dataClearDao';

export default function DataManagementScreen() {
  const { theme } = useTheme();
  const [hasPassword, setHasPassword] = useState(false);
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [showSetPasswordModal, setShowSetPasswordModal] = useState(false);
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [dataStats, setDataStats] = useState({
    cadreCount: 0,
    positionLevelCount: 0,
    retirementRuleCount: 0,
  });
  const [showRestoreModal, setShowRestoreModal] = useState(false);
  const [restoreJsonData, setRestoreJsonData] = useState('');

  useEffect(() => {
    checkPasswordStatus();
    loadDataStatistics();
  }, []);

  const checkPasswordStatus = async () => {
    try {
      const hasPass = await DataClearDao.hasPassword();
      setHasPassword(hasPass);
    } catch (error) {
      console.error('检查密码状态失败:', error);
    }
  };

  const loadDataStatistics = async () => {
    try {
      const stats = await DataClearDao.getDataStatistics();
      setDataStats(stats);
    } catch (error) {
      console.error('加载数据统计失败:', error);
    }
  };

  const handleClearData = () => {
    if (!hasPassword) {
      // 首次使用，需要设置密码
      setShowSetPasswordModal(true);
    } else {
      // 已有密码，需要验证
      setShowPasswordModal(true);
    }
  };

  const handleSetPassword = async () => {
    if (password.length < 6) {
      Alert.alert('错误', '密码长度至少6位');
      return;
    }

    if (password !== confirmPassword) {
      Alert.alert('错误', '两次输入的密码不一致');
      return;
    }

    try {
      setLoading(true);
      await DataClearDao.setPassword(password);
      setHasPassword(true);
      setShowSetPasswordModal(false);
      setPassword('');
      setConfirmPassword('');
      
      Alert.alert(
        '密码设置成功',
        '现在将清空所有干部数据，请确认？',
        [
          { text: '取消', style: 'cancel' },
          { text: '确认清空', style: 'destructive', onPress: performDataClear }
        ]
      );
    } catch (error) {
      console.error('设置密码失败:', error);
      Alert.alert('错误', '设置密码失败');
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyPassword = async () => {
    if (!password) {
      Alert.alert('错误', '请输入密码');
      return;
    }

    try {
      setLoading(true);
      const isValid = await DataClearDao.verifyPassword(password);
      
      if (isValid) {
        setShowPasswordModal(false);
        setPassword('');
        
        Alert.alert(
          '确认清空数据',
          `即将清空以下数据：\n• 干部信息：${dataStats.cadreCount}条\n\n此操作不可恢复，请确认？`,
          [
            { text: '取消', style: 'cancel' },
            { text: '确认清空', style: 'destructive', onPress: performDataClear }
          ]
        );
      } else {
        Alert.alert('错误', '密码错误');
      }
    } catch (error) {
      console.error('验证密码失败:', error);
      Alert.alert('错误', '验证密码失败');
    } finally {
      setLoading(false);
    }
  };

  const performDataClear = async () => {
    try {
      setLoading(true);
      await DataClearDao.clearAllData();
      
      Alert.alert(
        '清空成功',
        '所有干部数据已清空，现在可以导入新的Excel数据',
        [
          { 
            text: '去导入数据', 
            onPress: () => router.push('/import-excel')
          },
          { 
            text: '返回首页', 
            onPress: () => router.push('/(tabs)')
          }
        ]
      );
      
      // 重新加载统计数据
      loadDataStatistics();
    } catch (error) {
      console.error('清空数据失败:', error);
      Alert.alert('错误', '清空数据失败');
    } finally {
      setLoading(false);
    }
  };

  const handleExportData = async () => {
    try {
      setLoading(true);
      const jsonData = await DataClearDao.exportDataToJson();
      
      // 在实际应用中，这里应该保存到文件或分享
      Alert.alert(
        '导出成功',
        '数据已导出为JSON格式（在实际应用中会保存到文件）',
        [{ text: '确定' }]
      );
      
      console.log('导出的数据:', jsonData.substring(0, 200) + '...');
    } catch (error) {
      console.error('导出数据失败:', error);
      Alert.alert('错误', '导出数据失败');
    } finally {
      setLoading(false);
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 16,
      backgroundColor: theme.colors.primary,
      paddingTop: 50,
    },
    backButton: {
      marginRight: 16,
    },
    headerTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: '#FFF',
    },
    content: {
      flex: 1,
      padding: 16,
    },
    section: {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 16,
      marginBottom: 16,
      elevation: 2,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    sectionTitle: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 12,
    },
    statItem: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: 8,
      borderBottomWidth: 1,
      borderBottomColor: '#F0F0F0',
    },
    statLabel: {
      fontSize: 14,
      color: theme.colors.text,
    },
    statValue: {
      fontSize: 14,
      fontWeight: '600',
      color: theme.colors.primary,
    },
    button: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      padding: 16,
      borderRadius: 8,
      marginBottom: 12,
    },
    dangerButton: {
      backgroundColor: '#FF3B30',
    },
    secondaryButton: {
      backgroundColor: theme.colors.secondary,
    },
    buttonText: {
      color: '#FFF',
      fontSize: 16,
      fontWeight: '600',
      marginLeft: 8,
    },
    warningText: {
      fontSize: 14,
      color: '#FF9500',
      textAlign: 'center',
      marginBottom: 16,
      lineHeight: 20,
    },
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    modalContent: {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 20,
      width: '90%',
      maxWidth: 400,
    },
    modalTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 16,
      textAlign: 'center',
    },
    input: {
      borderWidth: 1,
      borderColor: '#E5E5EA',
      borderRadius: 8,
      paddingHorizontal: 12,
      paddingVertical: 10,
      fontSize: 16,
      color: theme.colors.text,
      marginBottom: 12,
    },
    modalActions: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: 20,
    },
    modalButton: {
      flex: 1,
      paddingVertical: 12,
      borderRadius: 8,
      alignItems: 'center',
      marginHorizontal: 8,
    },
    cancelButton: {
      backgroundColor: '#8E8E93',
    },
    confirmButton: {
      backgroundColor: theme.colors.primary,
    },
    modalButtonText: {
      color: '#FFF',
      fontSize: 16,
      fontWeight: '600',
    },
    loadingContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      padding: 20,
    },
    loadingText: {
      marginLeft: 12,
      fontSize: 16,
      color: theme.colors.text,
    },
  });

  return (
    <View style={styles.container}>
      {/* 头部 */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="arrow-back" size={24} color="#FFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>数据管理</Text>
      </View>

      <ScrollView style={styles.content}>
        {/* 数据统计 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>数据统计</Text>
          <View style={styles.statItem}>
            <Text style={styles.statLabel}>干部信息</Text>
            <Text style={styles.statValue}>{dataStats.cadreCount} 条</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statLabel}>职级配置</Text>
            <Text style={styles.statValue}>{dataStats.positionLevelCount} 个</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statLabel}>退休规则</Text>
            <Text style={styles.statValue}>{dataStats.retirementRuleCount} 条</Text>
          </View>
        </View>

        {/* 数据操作 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>数据操作</Text>
          
          <TouchableOpacity 
            style={[styles.button, styles.secondaryButton]}
            onPress={handleExportData}
            disabled={loading}
          >
            <Ionicons name="download" size={20} color="#FFF" />
            <Text style={styles.buttonText}>导出数据备份</Text>
          </TouchableOpacity>

          <Text style={styles.warningText}>
            ⚠️ 数据清空操作将删除所有干部信息，此操作不可恢复！{'\n'}
            {hasPassword ? '需要输入密码验证' : '首次使用需要设置密码'}
          </Text>

          <TouchableOpacity 
            style={[styles.button, styles.dangerButton]}
            onPress={handleClearData}
            disabled={loading}
          >
            <Ionicons name="trash" size={20} color="#FFF" />
            <Text style={styles.buttonText}>清空所有数据</Text>
          </TouchableOpacity>
        </View>

        {/* 加载状态 */}
        {loading && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.colors.primary} />
            <Text style={styles.loadingText}>处理中...</Text>
          </View>
        )}
      </ScrollView>

      {/* 设置密码模态框 */}
      <Modal
        visible={showSetPasswordModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowSetPasswordModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>设置数据清空密码</Text>
            <Text style={{ textAlign: 'center', marginBottom: 16, color: theme.colors.textSecondary }}>
              为了保护数据安全，请设置一个密码用于数据清空操作
            </Text>
            
            <TextInput
              style={styles.input}
              placeholder="请输入密码（至少6位）"
              value={password}
              onChangeText={setPassword}
              secureTextEntry
            />
            
            <TextInput
              style={styles.input}
              placeholder="请确认密码"
              value={confirmPassword}
              onChangeText={setConfirmPassword}
              secureTextEntry
            />

            <View style={styles.modalActions}>
              <TouchableOpacity 
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => setShowSetPasswordModal(false)}
              >
                <Text style={styles.modalButtonText}>取消</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={[styles.modalButton, styles.confirmButton]}
                onPress={handleSetPassword}
                disabled={loading}
              >
                <Text style={styles.modalButtonText}>设置并清空</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* 验证密码模态框 */}
      <Modal
        visible={showPasswordModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowPasswordModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>验证密码</Text>
            <Text style={{ textAlign: 'center', marginBottom: 16, color: theme.colors.textSecondary }}>
              请输入数据清空密码以继续操作
            </Text>
            
            <TextInput
              style={styles.input}
              placeholder="请输入密码"
              value={password}
              onChangeText={setPassword}
              secureTextEntry
            />

            <View style={styles.modalActions}>
              <TouchableOpacity 
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => setShowPasswordModal(false)}
              >
                <Text style={styles.modalButtonText}>取消</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={[styles.modalButton, styles.confirmButton]}
                onPress={handleVerifyPassword}
                disabled={loading}
              >
                <Text style={styles.modalButtonText}>确认</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}
