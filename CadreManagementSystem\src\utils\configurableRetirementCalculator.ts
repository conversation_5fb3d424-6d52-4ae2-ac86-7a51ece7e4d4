import { CadreInfo } from "../types";

export interface RetirementWarning {
  cadre: CadreInfo;
  warningType: string;
  warningLevel: string;
  message: string;
  daysRemaining: number;
  actualRetirementAge: number;
  delayInfo: {
    delayYears: number;
    delayMonths: number;
    totalDelayMonths: number;
  };
}

export class ConfigurableRetirementCalculator {
  static async generateWarnings(cadres: CadreInfo[]): Promise<RetirementWarning[]> {
    console.log("开始生成退休预警，共" + cadres.length + "个人员...");
    
    const warnings: RetirementWarning[] = [];
    const today = new Date();
    
    for (const cadre of cadres) {
      try {
        const warning = this.analyzeSingleCadre(cadre, today);
        if (warning) {
          warnings.push(warning);
        }
      } catch (error) {
        console.error("处理人员 " + cadre.姓名 + " 时出错:", error);
      }
    }
    
    console.log("完成分析" + cadres.length + "个人员，生成" + warnings.length + "个预警");
    return warnings;
  }

  private static analyzeSingleCadre(cadre: CadreInfo, today: Date): RetirementWarning | null {
    console.log(`🔍 分析干部: ${cadre.姓名} (性别:${cadre.性别}, 出生日期:${cadre.出生日期})`);

    const birthDate = this.parseBirthDate(cadre.出生日期);
    if (!birthDate) {
      console.log(`❌ ${cadre.姓名}: 出生日期解析失败 '${cadre.出生日期}'`);
      return null;
    }

    const age = this.calculateAge(birthDate, today);
    const birthYear = birthDate.getFullYear();
    const baseRetirementAge = this.getBaseRetirementAge(cadre);
    const delayInfo = this.calculateDelayRetirement(birthYear);
    const actualRetirementAge = baseRetirementAge + (delayInfo.totalDelayMonths / 12);

    const retirementDate = new Date(birthDate);
    retirementDate.setFullYear(retirementDate.getFullYear() + Math.floor(actualRetirementAge));
    retirementDate.setMonth(retirementDate.getMonth() + Math.round((actualRetirementAge % 1) * 12));

    const daysRemaining = Math.ceil((retirementDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

    console.log(`📊 ${cadre.姓名}: 年龄${age}岁, 基础退休${baseRetirementAge}岁, 延迟${delayInfo.totalDelayMonths}个月, 实际退休${actualRetirementAge.toFixed(1)}岁, 距离${daysRemaining}天`);

    return this.checkWarningConditions(cadre, age, daysRemaining, actualRetirementAge, delayInfo);
  }

  private static calculateDelayRetirement(birthYear: number) {
    if (birthYear <= 1964) {
      return { delayYears: 0, delayMonths: 0, totalDelayMonths: 0 };
    }

    let totalDelayMonths = 0;
    
    if (birthYear === 1965) {
      totalDelayMonths = 3;
    } else if (birthYear >= 1966 && birthYear <= 1970) {
      totalDelayMonths = 3;
      const extraYears = birthYear - 1965;
      const extraMonths = Math.min(extraYears * 2, 12);
      totalDelayMonths += extraMonths;
    } else if (birthYear >= 1971 && birthYear <= 1980) {
      totalDelayMonths = 3 + 12;
      const extraYears = birthYear - 1970;
      const extraMonths = Math.min(extraYears * 3, 24);
      totalDelayMonths += extraMonths;
    } else if (birthYear >= 1981) {
      totalDelayMonths = 3 + 12 + 24;
      const extraYears = birthYear - 1980;
      const extraMonths = Math.min(extraYears * 4, 24);
      totalDelayMonths += extraMonths;
    }
    
    totalDelayMonths = Math.min(totalDelayMonths, 60);

    return {
      delayYears: Math.floor(totalDelayMonths / 12),
      delayMonths: totalDelayMonths % 12,
      totalDelayMonths
    };
  }

  private static checkWarningConditions(
    cadre: CadreInfo,
    age: number,
    daysRemaining: number,
    actualRetirementAge: number,
    delayInfo: any
  ): RetirementWarning | null {
    const gender = cadre.性别;
    const position = cadre.现职级 || cadre.职务 || "";

    // 判断是否为中层管理人员
    const isMiddleManagement = position.includes("中层正职") ||
                              position.includes("中层副职") ||
                              position.includes("正处") ||
                              position.includes("副处");

    // 4. 已退休预警 - 最高优先级（超过法定年龄+延迟退休年龄）
    if (daysRemaining <= 0) {
      const message = "已退休预警（" + age + "岁，已超过退休年龄" + Math.abs(daysRemaining) + "天）";
      console.log("⚠️ " + cadre.姓名 + ": " + message);

      return {
        cadre,
        warningType: "retired",
        warningLevel: "very_urgent",
        message,
        daysRemaining: Math.abs(daysRemaining),
        actualRetirementAge,
        delayInfo
      };
    }

    // 3. 已退居二线预警 - 中层管理人员，男≥58岁，女≥54岁，≤实际退休年龄（优先级高于近两年退休）
    if (isMiddleManagement) {
      let shouldWarnAlreadySecondLine = false;
      if (gender === "男" && age >= 58 && age <= actualRetirementAge) {
        shouldWarnAlreadySecondLine = true;
      } else if (gender === "女" && age >= 54 && age <= actualRetirementAge) {
        shouldWarnAlreadySecondLine = true;
      }

      if (shouldWarnAlreadySecondLine) {
        const message = "已退居二线预警（" + age + "岁，" + position + "）";
        console.log("⚠️ " + cadre.姓名 + ": " + message);

        return {
          cadre,
          warningType: "already_second_line",
          warningLevel: "urgent",
          message,
          daysRemaining,
          actualRetirementAge,
          delayInfo
        };
      }
    }

    // 1. 近两年退休预警 - 距离实际退休时间≤730天，年龄范围：男≥58岁，女≥53岁，≤实际退休年龄，且不是中层管理
    const isNearRetirement = daysRemaining <= 730 && (
      (gender === "男" && age >= 58 && age <= actualRetirementAge) ||
      (gender === "女" && age >= 53 && age <= actualRetirementAge)
    ) && !isMiddleManagement;

    if (isNearRetirement) {
      const message = "近两年退休预警（" + age + "岁，还剩" + daysRemaining + "天退休）";
      console.log("⚠️ " + cadre.姓名 + ": " + message);

      return {
        cadre,
        warningType: "near_retirement",
        warningLevel: this.getWarningLevel(daysRemaining),
        message,
        daysRemaining,
        actualRetirementAge,
        delayInfo
      };
    }

    // 2. 近两年退居二线预警 - 中层管理人员，男56-58岁，女52-54岁
    if (isMiddleManagement) {
      let shouldWarnSecondLine = false;
      if (gender === "男" && age >= 56 && age <= 58) {
        shouldWarnSecondLine = true;
      } else if (gender === "女" && age >= 52 && age <= 54) {
        shouldWarnSecondLine = true;
      }

      if (shouldWarnSecondLine) {
        const message = "近两年退居二线预警（" + age + "岁，" + position + "）";
        console.log("⚠️ " + cadre.姓名 + ": " + message);

        return {
          cadre,
          warningType: "near_second_line",
          warningLevel: "medium",
          message,
          daysRemaining,
          actualRetirementAge,
          delayInfo
        };
      }
    }

    console.log(`✅ ${cadre.姓名}: 无预警 (年龄${age}岁，不符合预警条件)`);
    return null;
  }



  private static getWarningLevel(daysRemaining: number): string {
    if (daysRemaining <= 90) return "very_urgent";
    if (daysRemaining <= 180) return "urgent";
    return "normal";
  }

  private static getBaseRetirementAge(cadre: CadreInfo): number {
    const gender = cadre.性别;

    if (gender === "男") {
      return 60;
    } else if (gender === "女") {
      // 🔧 重要修复：干部管理系统中所有女性都按干部标准（55岁）退休
      // 不再区分工人，避免错误地按50岁计算
      return 55;

      /* 原逻辑已注释，保留备用
      const position = cadre.现职级 || cadre.职务 || "";
      const isCadre = position.includes("职") || position.includes("长") ||
                      position.includes("主任") || position.includes("副主任") ||
                      position.includes("正职") || position.includes("副职");
      return isCadre ? 55 : 50;
      */
    }

    return 60;
  }

  private static parseBirthDate(birthDateStr: string): Date | null {
    if (!birthDateStr) return null;
    
    try {
      const cleanStr = birthDateStr.replace(/[年月日\-\.\/]/g, "-");
      const parts = cleanStr.split("-").filter(p => p.length > 0);
      
      if (parts.length >= 2) {
        const year = parseInt(parts[0]);
        const month = parseInt(parts[1]) - 1;
        const day = parts.length >= 3 ? parseInt(parts[2]) : 1;
        
        if (year > 1900 && year < 2100 && month >= 0 && month < 12) {
          return new Date(year, month, day);
        }
      }
    } catch (error) {
      console.error("解析出生日期失败:", birthDateStr, error);
    }
    
    return null;
  }

  private static calculateAge(birthDate: Date, currentDate: Date): number {
    const age = currentDate.getFullYear() - birthDate.getFullYear();
    const monthDiff = currentDate.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && currentDate.getDate() < birthDate.getDate())) {
      return age - 1;
    }
    
    return age;
  }
}
