/**
 * 深度检查导入的数据
 */
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, 'cadre_management.db');

console.log('🔍 深度检查导入的1171条数据...');

const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ 连接数据库失败:', err.message);
    return;
  }
  console.log('✅ 已连接到数据库');
});

// 检查数据总数
db.get("SELECT COUNT(*) as count FROM cadres", [], (err, result) => {
  if (err) {
    console.error('❌ 查询总数失败:', err.message);
    return;
  }
  
  console.log(`\n📊 数据库总记录数: ${result.count}`);
  
  // 检查出生日期字段的数据质量
  db.all(`
    SELECT 
      COUNT(*) as total,
      COUNT(出生日期) as has_birth_date,
      COUNT(CASE WHEN 出生日期 IS NOT NULL AND 出生日期 != '' THEN 1 END) as valid_birth_date
    FROM cadres
  `, [], (err, stats) => {
    if (err) {
      console.error('❌ 查询统计失败:', err.message);
      return;
    }
    
    const stat = stats[0];
    console.log(`\n📋 出生日期字段统计:`);
    console.log(`  总记录数: ${stat.total}`);
    console.log(`  有出生日期字段: ${stat.has_birth_date}`);
    console.log(`  有效出生日期: ${stat.valid_birth_date}`);
    console.log(`  无效出生日期: ${stat.total - stat.valid_birth_date}`);
    
    // 查看前10条记录的关键字段
    db.all(`
      SELECT 姓名, 性别, 出生日期, 现职级, 职务, 单位
      FROM cadres 
      WHERE 出生日期 IS NOT NULL AND 出生日期 != ''
      ORDER BY 出生日期
      LIMIT 10
    `, [], (err, rows) => {
      if (err) {
        console.error('❌ 查询样本数据失败:', err.message);
        return;
      }
      
      console.log(`\n📋 前10条有效数据样本:`);
      console.log('姓名     | 性别 | 出生日期   | 现职级/职务');
      console.log('-'.repeat(50));
      
      const today = new Date();
      let shouldHaveWarnings = [];
      
      rows.forEach(row => {
        const position = row.现职级 || row.职务 || '';
        console.log(`${row.姓名.padEnd(8)} | ${row.性别.padEnd(2)} | ${row.出生日期.padEnd(10)} | ${position}`);
        
        // 计算年龄和退休预警
        try {
          const birthDate = new Date(row.出生日期);
          if (!isNaN(birthDate.getTime())) {
            const age = today.getFullYear() - birthDate.getFullYear();
            const monthDiff = today.getMonth() - birthDate.getMonth();
            let currentAge = age;
            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
              currentAge--;
            }
            
            // 确定退休年龄
            let retirementAge = 60; // 默认男性
            if (row.性别 === '女') {
              retirementAge = position.includes('工人') || position.includes('操作') ? 50 : 55;
            }
            
            const yearsToRetirement = retirementAge - currentAge;
            
            if (yearsToRetirement <= 2 && yearsToRetirement > -5) {
              shouldHaveWarnings.push({
                姓名: row.姓名,
                年龄: currentAge,
                退休年龄: retirementAge,
                距离退休: yearsToRetirement
              });
            }
          }
        } catch (e) {
          console.log(`    ❌ ${row.姓名}: 日期解析错误`);
        }
      });
      
      console.log(`\n⚠️ 前10条中应该有预警的: ${shouldHaveWarnings.length}人`);
      shouldHaveWarnings.forEach(person => {
        console.log(`  - ${person.姓名}: ${person.年龄}岁, 距离${person.退休年龄}岁退休还有${person.距离退休.toFixed(1)}年`);
      });
      
      // 检查年龄分布
      db.all(`
        SELECT 
          CASE 
            WHEN 出生日期 LIKE '196%' THEN '1960年代'
            WHEN 出生日期 LIKE '197%' THEN '1970年代'
            WHEN 出生日期 LIKE '198%' THEN '1980年代'
            WHEN 出生日期 LIKE '199%' THEN '1990年代'
            WHEN 出生日期 LIKE '200%' THEN '2000年代'
            ELSE '其他'
          END as decade,
          COUNT(*) as count
        FROM cadres 
        WHERE 出生日期 IS NOT NULL AND 出生日期 != ''
        GROUP BY decade
        ORDER BY decade
      `, [], (err, ageStats) => {
        if (err) {
          console.error('❌ 查询年龄分布失败:', err.message);
          return;
        }
        
        console.log(`\n📊 出生年代分布:`);
        ageStats.forEach(stat => {
          console.log(`  ${stat.decade}: ${stat.count}人`);
        });
        
        // 查找最接近退休的人员
        db.all(`
          SELECT 姓名, 性别, 出生日期, 现职级, 职务
          FROM cadres 
          WHERE 出生日期 IS NOT NULL AND 出生日期 != ''
          AND (
            (性别 = '男' AND 出生日期 < '1967-01-01') OR
            (性别 = '女' AND 出生日期 < '1972-01-01')
          )
          ORDER BY 出生日期
          LIMIT 20
        `, [], (err, nearRetirement) => {
          if (err) {
            console.error('❌ 查询接近退休人员失败:', err.message);
            return;
          }
          
          console.log(`\n🎯 最接近退休的20人:`);
          console.log('姓名     | 性别 | 出生日期   | 年龄 | 职务');
          console.log('-'.repeat(60));
          
          nearRetirement.forEach(person => {
            const birthDate = new Date(person.出生日期);
            const age = today.getFullYear() - birthDate.getFullYear();
            const position = person.现职级 || person.职务 || '';
            console.log(`${person.姓名.padEnd(8)} | ${person.性别.padEnd(2)} | ${person.出生日期.padEnd(10)} | ${age.toString().padEnd(2)} | ${position}`);
          });
          
          console.log(`\n🔍 分析结论:`);
          console.log(`1. 数据库中有${result.count}条记录`);
          console.log(`2. 有效出生日期: ${stat.valid_birth_date}条`);
          console.log(`3. 最接近退休: ${nearRetirement.length}人`);
          console.log(`4. 如果预警仍为0，问题可能在于:`);
          console.log(`   - ConfigurableRetirementCalculator的日期解析`);
          console.log(`   - 延迟退休计算逻辑`);
          console.log(`   - 预警条件判断`);
          
          db.close();
        });
      });
    });
  });
});
