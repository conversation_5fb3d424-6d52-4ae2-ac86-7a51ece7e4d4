// 测试应用基本功能
const { exec } = require('child_process');
const path = require('path');

console.log('🚀 开始测试智慧干部信息管理系统...');

// 检查依赖
console.log('📦 检查项目依赖...');
exec('npm list --depth=0', (error, stdout, stderr) => {
  if (error) {
    console.error('❌ 依赖检查失败:', error);
    return;
  }
  console.log('✅ 依赖检查完成');
  
  // 检查关键文件
  const fs = require('fs');
  const criticalFiles = [
    'src/database/database.ts',
    'src/contexts/ThemeContext.tsx',
    'src/components/ThemePicker.tsx',
    'app/(tabs)/index.tsx',
    'app/(tabs)/settings.tsx'
  ];
  
  console.log('📁 检查关键文件...');
  let allFilesExist = true;
  
  criticalFiles.forEach(file => {
    if (fs.existsSync(file)) {
      console.log(`✅ ${file}`);
    } else {
      console.log(`❌ ${file} - 文件不存在`);
      allFilesExist = false;
    }
  });
  
  if (allFilesExist) {
    console.log('🎉 所有关键文件检查通过！');
    console.log('');
    console.log('📱 雷电模拟器测试步骤：');
    console.log('1. 确保雷电模拟器已启动并开启USB调试');
    console.log('2. 运行命令: npm start');
    console.log('3. 在模拟器中安装Expo Go应用');
    console.log('4. 扫描二维码或输入开发服务器地址');
    console.log('5. 测试主题切换功能：设置 → 主题设置');
    console.log('');
    console.log('🎨 主题测试重点：');
    console.log('- 高端主题 (深色奢华)');
    console.log('- 商务主题 (专业蓝色)');
    console.log('- 简约主题 (清新简洁)');
    console.log('- 经典主题 (传统稳重)');
  } else {
    console.log('❌ 部分关键文件缺失，请检查项目完整性');
  }
});
