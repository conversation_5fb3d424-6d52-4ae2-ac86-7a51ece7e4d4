/**
 * 退休预警计算引擎
 * 根据国家渐进式延迟退休政策计算退休时间和预警信息
 */

import { CadreInfo } from '../types';

// 延迟退休配置表
interface DelayConfig {
  birthYearStart: number;
  birthYearEnd: number;
  delayMonthsPerYear: number;
  maxDelayYears: number;
}

// 延迟退休配置（根据您提供的算法）
const DELAY_CONFIGS: DelayConfig[] = [
  {
    birthYearStart: 0,
    birthYearEnd: 1965,
    delayMonthsPerYear: 0,  // 不延迟
    maxDelayYears: 0
  },
  {
    birthYearStart: 1966,
    birthYearEnd: 1970,
    delayMonthsPerYear: 2,  // 每年延迟2个月
    maxDelayYears: 1
  },
  {
    birthYearStart: 1971,
    birthYearEnd: 1980,
    delayMonthsPerYear: 3,  // 每年延迟3个月
    maxDelayYears: 3
  },
  {
    birthYearStart: 1981,
    birthYearEnd: 9999,
    delayMonthsPerYear: 4,  // 每年延迟4个月
    maxDelayYears: 5
  }
];

// 原法定退休年龄
const ORIGINAL_RETIREMENT_AGES = {
  male: 60,           // 男性干部/职工
  female_cadre: 55,   // 女干部/技术岗
  female_worker: 50   // 女工人/基层岗
};

// 退休计算结果
export interface RetirementCalculation {
  originalRetirementAge: number;      // 原法定退休年龄
  originalRetirementDate: Date;       // 原退休日期
  delayMonths: number;               // 延迟月数
  delayYears: number;                // 延迟年数
  actualRetirementAge: number;       // 实际退休年龄
  actualRetirementDate: Date;        // 实际退休日期
  daysUntilRetirement: number;       // 距离退休天数
  description: string;               // 计算说明
}

// 预警级别
export type WarningLevel = 'urgent' | 'normal' | 'info';

// 预警类型
export type WarningType = 'two_year_warning' | 'monthly_warning' | 'second_line_warning' | 'retired';

// 预警信息
export interface RetirementWarningInfo {
  cadre: CadreInfo;
  warningType: WarningType;
  urgencyLevel: WarningLevel;
  description: string;
  daysUntilRetirement: number;
  calculation: RetirementCalculation;
}

export class RetirementCalculator {
  
  /**
   * 解析出生日期
   */
  private static parseBirthDate(birthDateStr: string | number): Date | null {
    if (!birthDateStr && birthDateStr !== 0) {
      console.log('❌ 出生日期为空');
      return null;
    }

    // 处理Excel日期序列号
    if (typeof birthDateStr === 'number') {
      console.log(`🔍 解析Excel日期序列号：${birthDateStr}`);
      try {
        // Excel日期序列号转换为JavaScript日期
        // Excel的日期基准是1900年1月1日，但实际上是1899年12月30日
        const excelDate = new Date((birthDateStr - 25569) * 86400 * 1000);
        if (!isNaN(excelDate.getTime()) && excelDate.getFullYear() > 1900 && excelDate.getFullYear() < 2100) {
          console.log(`✅ Excel日期解析成功：${excelDate.toLocaleDateString()}`);
          return excelDate;
        }
      } catch (e) {
        console.log(`❌ Excel日期解析失败：${e}`);
      }
    }

    // 清理字符串
    const cleanStr = birthDateStr.toString().trim();
    console.log(`🔍 解析出生日期字符串：'${cleanStr}'`);

    // 支持多种日期格式
    const formats = [
      /^(\d{4})-(\d{1,2})-(\d{1,2})$/,     // 1975-08-15
      /^(\d{4})\/(\d{1,2})\/(\d{1,2})$/,   // 1975/08/15
      /^(\d{4})\.(\d{1,2})\.(\d{1,2})$/,   // 1975.08.15
      /^(\d{4})年(\d{1,2})月(\d{1,2})日$/,  // 1975年08月15日
      /^(\d{1,2})-(\d{1,2})-(\d{4})$/,     // 15-08-1975
      /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/,   // 15/08/1975
      /^(\d{8})$/                          // 19750815
    ];

    for (let i = 0; i < formats.length; i++) {
      const format = formats[i];
      const match = cleanStr.match(format);
      if (match) {
        let year, month, day;

        if (i < 4) {
          // 年-月-日格式
          year = parseInt(match[1]);
          month = parseInt(match[2]) - 1; // JavaScript月份从0开始
          day = parseInt(match[3]);
        } else if (i < 6) {
          // 日-月-年格式
          day = parseInt(match[1]);
          month = parseInt(match[2]) - 1;
          year = parseInt(match[3]);
        } else {
          // 8位数字格式 YYYYMMDD
          const str = match[1];
          year = parseInt(str.substring(0, 4));
          month = parseInt(str.substring(4, 6)) - 1;
          day = parseInt(str.substring(6, 8));
        }

        const date = new Date(year, month, day);
        if (!isNaN(date.getTime()) && year > 1900 && year < 2100) {
          console.log(`✅ 解析成功：${date.toLocaleDateString()}`);
          return date;
        }
      }
    }

    // 尝试直接解析
    try {
      const date = new Date(cleanStr);
      if (!isNaN(date.getTime()) && date.getFullYear() > 1900 && date.getFullYear() < 2100) {
        console.log(`✅ 直接解析成功：${date.toLocaleDateString()}`);
        return date;
      }
    } catch (e) {
      // 忽略错误
    }

    console.log(`❌ 无法解析出生日期：'${cleanStr}'`);
    return null;
  }

  /**
   * 确定女性干部类型（干部还是工人）
   */
  private static getFemaleType(cadre: CadreInfo): 'cadre' | 'worker' {
    const position = cadre.现职级 || cadre.职务 || '';
    
    // 根据职级判断是否为干部
    const cadreKeywords = ['处级', '科级', '主任', '副主任', '书记', '副书记', '长', '副', '主管', '经理', '总监'];
    const workerKeywords = ['工人', '操作员', '技工', '普工', '班组'];
    
    // 优先检查工人关键词
    if (workerKeywords.some(keyword => position.includes(keyword))) {
      return 'worker';
    }
    
    // 检查干部关键词
    if (cadreKeywords.some(keyword => position.includes(keyword))) {
      return 'cadre';
    }
    
    // 默认按干部处理
    return 'cadre';
  }

  /**
   * 获取延迟退休配置
   */
  private static getDelayConfig(birthYear: number): DelayConfig {
    for (const config of DELAY_CONFIGS) {
      if (birthYear >= config.birthYearStart && birthYear <= config.birthYearEnd) {
        return config;
      }
    }
    return DELAY_CONFIGS[0]; // 默认不延迟
  }

  /**
   * 计算退休信息
   */
  static calculateRetirement(cadre: CadreInfo): RetirementCalculation | null {
    const birthDate = this.parseBirthDate(cadre.出生日期 || '');
    if (!birthDate) {
      console.log(`❌ ${cadre.姓名}: 无法解析出生日期 '${cadre.出生日期}'`);
      return null;
    }

    const birthYear = birthDate.getFullYear();
    const gender = cadre.性别?.toLowerCase();
    
    // 确定原法定退休年龄
    let originalRetirementAge: number;
    if (gender === '男' || gender === 'male') {
      originalRetirementAge = ORIGINAL_RETIREMENT_AGES.male;
    } else if (gender === '女' || gender === 'female') {
      const femaleType = this.getFemaleType(cadre);
      originalRetirementAge = femaleType === 'cadre' 
        ? ORIGINAL_RETIREMENT_AGES.female_cadre 
        : ORIGINAL_RETIREMENT_AGES.female_worker;
    } else {
      // 默认按男性处理
      originalRetirementAge = ORIGINAL_RETIREMENT_AGES.male;
    }

    // 计算原退休日期
    const originalRetirementDate = new Date(birthDate);
    originalRetirementDate.setFullYear(birthDate.getFullYear() + originalRetirementAge);

    // 获取延迟配置
    const delayConfig = this.getDelayConfig(birthYear);
    
    // 计算延迟月数
    const baseYear = 1965; // 基准年份
    const delayYearsFromBase = Math.max(0, birthYear - baseYear);
    const totalDelayMonths = Math.min(
      delayYearsFromBase * delayConfig.delayMonthsPerYear,
      delayConfig.maxDelayYears * 12
    );

    // 计算实际退休日期
    const actualRetirementDate = new Date(originalRetirementDate);
    actualRetirementDate.setMonth(actualRetirementDate.getMonth() + totalDelayMonths);

    // 计算实际退休年龄
    const actualRetirementAge = originalRetirementAge + (totalDelayMonths / 12);

    // 计算距离退休天数
    const today = new Date();
    const daysUntilRetirement = Math.ceil(
      (actualRetirementDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)
    );

    // 生成说明
    let description = `原退休年龄${originalRetirementAge}岁`;
    if (totalDelayMonths > 0) {
      const delayYears = Math.floor(totalDelayMonths / 12);
      const delayMonths = totalDelayMonths % 12;
      let delayStr = '';
      if (delayYears > 0) delayStr += `${delayYears}年`;
      if (delayMonths > 0) delayStr += `${delayMonths}个月`;
      description += `，延迟${delayStr}，实际退休年龄${actualRetirementAge.toFixed(1)}岁`;
    }

    return {
      originalRetirementAge,
      originalRetirementDate,
      delayMonths: totalDelayMonths,
      delayYears: totalDelayMonths / 12,
      actualRetirementAge,
      actualRetirementDate,
      daysUntilRetirement,
      description
    };
  }

  /**
   * 生成退休预警信息
   */
  static generateWarning(cadre: CadreInfo): RetirementWarningInfo | null {
    console.log(`🔍 分析干部：${cadre.姓名}`);

    const calculation = this.calculateRetirement(cadre);
    if (!calculation) {
      console.log(`❌ ${cadre.姓名}：无法计算退休信息（出生日期格式错误或缺失）`);
      return null;
    }

    const { daysUntilRetirement, actualRetirementDate, actualRetirementAge } = calculation;
    console.log(`📊 ${cadre.姓名}：实际退休年龄${actualRetirementAge.toFixed(1)}岁，距离退休${daysUntilRetirement}天`);
    
    // 已退休
    if (daysUntilRetirement <= 0) {
      return {
        cadre,
        warningType: 'retired',
        urgencyLevel: 'info',
        description: `已于${actualRetirementDate.toLocaleDateString()}退休`,
        daysUntilRetirement: 0,
        calculation
      };
    }

    // 退居二线预警（距离退休2-5年）
    if (daysUntilRetirement <= 365 * 5 && daysUntilRetirement > 365 * 2) {
      return {
        cadre,
        warningType: 'second_line_warning',
        urgencyLevel: 'normal',
        description: `距离退休${Math.ceil(daysUntilRetirement / 365)}年，建议考虑退居二线安排`,
        daysUntilRetirement,
        calculation
      };
    }

    // 近两年退休预警
    if (daysUntilRetirement <= 365 * 2 && daysUntilRetirement > 90) {
      return {
        cadre,
        warningType: 'two_year_warning',
        urgencyLevel: 'normal',
        description: `距离退休${Math.ceil(daysUntilRetirement / 365)}年，请提前做好退休准备`,
        daysUntilRetirement,
        calculation
      };
    }

    // 月度预警（3个月内退休）
    if (daysUntilRetirement <= 90) {
      return {
        cadre,
        warningType: 'monthly_warning',
        urgencyLevel: 'urgent',
        description: `距离退休仅剩${daysUntilRetirement}天，请立即办理退休手续`,
        daysUntilRetirement,
        calculation
      };
    }

    return null;
  }

  /**
   * 批量生成预警信息
   */
  static generateWarnings(cadres: CadreInfo[]): RetirementWarningInfo[] {
    console.log(`🔍 退休计算器：开始分析${cadres.length}条干部数据...`);

    const warnings: RetirementWarningInfo[] = [];
    let processedCount = 0;
    let warningCount = 0;

    for (const cadre of cadres) {
      processedCount++;
      console.log(`📊 处理第${processedCount}条：${cadre.姓名} (性别:${cadre.性别}, 出生日期:${cadre.出生日期})`);

      const warning = this.generateWarning(cadre);
      if (warning) {
        warningCount++;
        console.log(`⚠️ 生成预警：${cadre.姓名} - ${warning.description}`);
        warnings.push(warning);
      } else {
        console.log(`✅ 无需预警：${cadre.姓名}`);
      }
    }

    console.log(`📈 预警统计：处理${processedCount}条数据，生成${warningCount}条预警`);

    // 按紧急程度和退休时间排序
    warnings.sort((a, b) => {
      // 紧急程度排序
      const urgencyOrder = { urgent: 0, normal: 1, info: 2 };
      const urgencyDiff = urgencyOrder[a.urgencyLevel] - urgencyOrder[b.urgencyLevel];
      if (urgencyDiff !== 0) return urgencyDiff;

      // 退休时间排序（越早退休越靠前）
      return a.daysUntilRetirement - b.daysUntilRetirement;
    });

    console.log(`🎯 退休计算器：完成分析，返回${warnings.length}条预警信息`);
    return warnings;
  }
}
