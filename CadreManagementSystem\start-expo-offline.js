/**
 * 离线启动EXPO开发服务器
 * 绕过网络检查问题
 */

const { spawn } = require('child_process');
const fs = require('fs');

console.log('🚀 启动离线EXPO开发服务器...');
console.log('================================');
console.log('');

// 设置环境变量禁用网络检查
process.env.EXPO_NO_TELEMETRY = '1';
process.env.EXPO_NO_CACHE = '1';
process.env.EXPO_OFFLINE = '1';
process.env.EXPO_NO_DOCTOR = '1';

// 创建临时的expo配置来跳过版本检查
const tempConfig = {
  "cli": {
    "version": "0.24.15",
    "requiresOnlineCheck": false
  }
};

try {
  fs.writeFileSync('.expo/settings.json', JSON.stringify(tempConfig, null, 2));
  console.log('✅ 已创建离线配置');
} catch (error) {
  // 忽略错误，继续启动
}

console.log('🔧 正在启动Metro bundler...');
console.log('');

// 直接启动Metro bundler
const metro = spawn('npx', ['@expo/cli', 'start', '--no-dev-client', '--host', '*************', '--port', '19000'], {
  stdio: ['pipe', 'pipe', 'pipe'],
  shell: true,
  env: {
    ...process.env,
    EXPO_NO_TELEMETRY: '1',
    EXPO_NO_CACHE: '1',
    EXPO_OFFLINE: '1',
    EXPO_NO_DOCTOR: '1'
  }
});

let serverStarted = false;
let connectionUrl = '';

metro.stdout.on('data', (data) => {
  const output = data.toString();
  console.log('📤 EXPO:', output);
  
  // 检查启动成功的标志
  if (output.includes('Metro') || output.includes('Bundler') || output.includes('exp://')) {
    if (!serverStarted) {
      serverStarted = true;
      console.log('');
      console.log('🎉 EXPO服务器启动成功！');
      console.log('');
    }
  }
  
  // 提取连接信息
  if (output.includes('exp://')) {
    const lines = output.split('\n');
    for (const line of lines) {
      if (line.includes('exp://')) {
        connectionUrl = line.trim();
        console.log('🔗 连接地址:', connectionUrl);
      }
    }
  }
  
  // 如果包含IP地址，显示连接信息
  if (output.includes('*************')) {
    console.log('');
    console.log('📱 请在雷电模拟器中：');
    console.log('1. 打开 Expo Go 应用');
    console.log('2. 选择 "Enter URL manually"');
    console.log('3. 输入: exp://*************:19000');
    console.log('4. 点击 "Connect"');
    console.log('');
  }
});

metro.stderr.on('data', (data) => {
  const error = data.toString();
  
  // 忽略网络相关错误
  if (error.includes('fetch failed') || error.includes('TypeError: fetch failed')) {
    console.log('⚠️ 忽略网络检查错误，继续启动...');
    return;
  }
  
  console.log('🚨 EXPO错误:', error);
  
  // 分析其他错误
  if (error.includes('EADDRINUSE')) {
    console.log('💡 解决方案: 端口被占用，正在尝试其他端口...');
  } else if (error.includes('ENOENT')) {
    console.log('💡 解决方案: 命令未找到，请检查EXPO CLI安装');
  }
});

metro.on('close', (code) => {
  console.log(`📋 EXPO进程结束，退出码: ${code}`);
  if (code !== 0 && !serverStarted) {
    console.log('❌ EXPO启动失败');
    console.log('🔄 正在尝试备用方案...');
    startBackupServer();
  }
});

metro.on('error', (err) => {
  console.log('🚨 EXPO启动错误:', err.message);
  startBackupServer();
});

// 备用启动方案
function startBackupServer() {
  console.log('🔄 启动备用Metro服务器...');
  
  const backup = spawn('npx', ['metro', 'start', '--host', '*************', '--port', '19000'], {
    stdio: ['pipe', 'pipe', 'pipe'],
    shell: true
  });

  backup.stdout.on('data', (data) => {
    console.log('📤 Metro:', data.toString());
    
    if (!serverStarted) {
      serverStarted = true;
      console.log('');
      console.log('🎉 备用服务器启动成功！');
      console.log('🔗 连接地址: exp://*************:19000');
      console.log('');
      console.log('📱 请在雷电模拟器中：');
      console.log('1. 打开 Expo Go 应用');
      console.log('2. 选择 "Enter URL manually"');
      console.log('3. 输入: exp://*************:19000');
      console.log('4. 点击 "Connect"');
      console.log('');
    }
  });

  backup.stderr.on('data', (data) => {
    console.log('🚨 Metro错误:', data.toString());
  });
}

// 超时检查
setTimeout(() => {
  if (!serverStarted) {
    console.log('');
    console.log('⚠️ 服务器启动时间较长，但这是正常的');
    console.log('💡 请耐心等待，首次启动需要构建缓存');
    console.log('');
    console.log('🔗 如果看到此消息，请尝试连接: exp://*************:19000');
    console.log('');
  }
}, 30000);

console.log('⏳ 正在启动服务器，请稍候...');
console.log('💡 首次启动可能需要1-2分钟来构建缓存');
console.log('');
