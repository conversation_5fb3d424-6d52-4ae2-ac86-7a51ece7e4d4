/**
 * 测试数据初始化器
 * 用于在应用中初始化测试数据
 */
import { getDatabase } from './database';
import { CadreInfo } from '../types';

export class TestDataInitializer {
  /**
   * 初始化测试数据
   */
  static async initializeTestData(): Promise<void> {
    const db = getDatabase();
    
    try {
      console.log('🔧 开始初始化测试数据...');
      
      // 检查是否已有数据
      const existingData = await db.getFirstAsync<{ count: number }>(
        'SELECT COUNT(*) as count FROM cadres'
      );
      
      if (existingData && existingData.count > 0) {
        console.log(`📊 数据库中已有 ${existingData.count} 条记录，跳过初始化`);
        return;
      }
      
      // 测试数据
      const testCadres: Partial<CadreInfo>[] = [
        {
          姓名: '李财茂',
          性别: '男',
          出生日期: '1965-10-15',
          现职级: '处长',
          单位: '测试单位A',
          职务: '处长',
          民族: '汉族',
          政治面貌: '中共党员',
          参加工作时间: '1985-07-01',
          全日制教育: '本科',
          专业技术职务: '高级工程师'
        },
        {
          姓名: '武海珠',
          性别: '女',
          出生日期: '1966-05-20',
          现职级: '副处长',
          单位: '测试单位A',
          职务: '副处长',
          民族: '汉族',
          政治面貌: '中共党员',
          参加工作时间: '1988-08-01',
          全日制教育: '本科',
          专业技术职务: '工程师'
        },
        {
          姓名: '王四顿',
          性别: '男',
          出生日期: '1964-03-15',
          现职级: '处长',
          单位: '测试单位B',
          职务: '处长',
          民族: '汉族',
          政治面貌: '中共党员',
          参加工作时间: '1984-07-01',
          全日制教育: '本科',
          专业技术职务: '高级工程师'
        },
        {
          姓名: '张建国',
          性别: '男',
          出生日期: '1965-04-01',
          现职级: '主任',
          单位: '测试单位C',
          职务: '主任',
          民族: '汉族',
          政治面貌: '中共党员',
          参加工作时间: '1985-08-01',
          全日制教育: '本科',
          专业技术职务: '工程师'
        },
        {
          姓名: '刘秀英',
          性别: '女',
          出生日期: '1975-12-25',
          现职级: '操作员',
          单位: '测试单位D',
          职务: '操作员',
          民族: '汉族',
          政治面貌: '群众',
          参加工作时间: '1995-07-01',
          全日制教育: '高中',
          专业技术职务: '技术员'
        },
        {
          姓名: '赵六',
          性别: '女',
          出生日期: '1990-07-25',
          现职级: '科员',
          单位: '测试单位E',
          职务: '科员',
          民族: '汉族',
          政治面貌: '共青团员',
          参加工作时间: '2012-07-01',
          全日制教育: '本科',
          专业技术职务: '助理工程师'
        },
        {
          姓名: '钱七',
          性别: '男',
          出生日期: '1978-11-30',
          现职级: '副主任',
          单位: '测试单位F',
          职务: '副主任',
          民族: '汉族',
          政治面貌: '中共党员',
          参加工作时间: '2000-07-01',
          全日制教育: '本科',
          专业技术职务: '工程师'
        }
      ];
      
      // 批量插入数据
      await db.withTransactionAsync(async () => {
        for (const cadre of testCadres) {
          await db.runAsync(`
            INSERT INTO cadres (
              姓名, 性别, 出生日期, 现职级, 单位, 职务, 民族, 政治面貌, 
              参加工作时间, 全日制教育, 专业技术职务
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `, [
            cadre.姓名, cadre.性别, cadre.出生日期, cadre.现职级, cadre.单位, 
            cadre.职务, cadre.民族, cadre.政治面貌, cadre.参加工作时间, 
            cadre.全日制教育, cadre.专业技术职务
          ]);
          
          console.log(`✅ 插入测试数据: ${cadre.姓名}`);
        }
      });
      
      // 验证插入结果
      const finalCount = await db.getFirstAsync<{ count: number }>(
        'SELECT COUNT(*) as count FROM cadres'
      );
      
      console.log(`✅ 测试数据初始化完成，共插入 ${finalCount?.count || 0} 条记录`);
      
    } catch (error) {
      console.error('❌ 初始化测试数据失败:', error);
      throw error;
    }
  }
  
  /**
   * 清空所有测试数据
   */
  static async clearTestData(): Promise<void> {
    const db = getDatabase();
    
    try {
      await db.runAsync('DELETE FROM cadres');
      console.log('✅ 测试数据已清空');
    } catch (error) {
      console.error('❌ 清空测试数据失败:', error);
      throw error;
    }
  }
}
