/**
 * 调试退休预警问题
 */
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, 'cadre_management.db');

console.log('🔍 调试退休预警问题...');

const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ 连接数据库失败:', err.message);
    return;
  }
  console.log('✅ 已连接到数据库');
});

// 查找55岁以上的男性和50岁以上的女性
db.all(`
  SELECT 姓名, 性别, 出生日期, 现职级, 职务, 单位
  FROM cadres 
  WHERE 出生日期 IS NOT NULL AND 出生日期 != ''
  ORDER BY 出生日期
`, [], (err, rows) => {
  if (err) {
    console.error('❌ 查询数据失败:', err.message);
    return;
  }
  
  console.log(`\n📊 数据库中共有 ${rows.length} 条记录`);
  
  const today = new Date();
  let shouldHaveWarnings = [];
  
  rows.forEach(row => {
    const birthDate = new Date(row.出生日期);
    if (isNaN(birthDate.getTime())) {
      console.log(`❌ ${row.姓名}: 出生日期格式错误 '${row.出生日期}'`);
      return;
    }
    
    // 计算年龄
    const age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    let currentAge = age;
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      currentAge--;
    }
    
    // 确定退休年龄
    let retirementAge = 60; // 默认男性
    if (row.性别 === '女') {
      const position = row.现职级 || row.职务 || '';
      retirementAge = position.includes('工人') || position.includes('操作') ? 50 : 55;
    }
    
    // 计算距离退休年数
    const yearsToRetirement = retirementAge - currentAge;
    
    // 检查是否应该有预警（两年内退休）
    if (yearsToRetirement <= 2 && yearsToRetirement > 0) {
      shouldHaveWarnings.push({
        姓名: row.姓名,
        性别: row.性别,
        出生日期: row.出生日期,
        当前年龄: currentAge,
        退休年龄: retirementAge,
        距离退休: yearsToRetirement,
        现职级: row.现职级,
        职务: row.职务,
        单位: row.单位
      });
    }
  });
  
  console.log(`\n⚠️ 应该有退休预警的人员（两年内退休）：${shouldHaveWarnings.length}人`);
  
  if (shouldHaveWarnings.length > 0) {
    console.log('\n📋 详细列表:');
    console.log('姓名     | 性别 | 年龄 | 退休年龄 | 距离退休 | 现职级');
    console.log('-'.repeat(60));
    
    shouldHaveWarnings.forEach(person => {
      console.log(`${person.姓名.padEnd(8)} | ${person.性别.padEnd(2)} | ${person.当前年龄.toString().padEnd(2)} | ${person.退休年龄.toString().padEnd(4)} | ${person.距离退休.toFixed(1).padEnd(4)}年 | ${person.现职级 || person.职务 || ''}`);
    });
    
    console.log('\n🔍 分析ConfigurableRetirementCalculator的问题:');
    console.log('1. 字段名问题：使用cadre.出生年月而不是cadre.出生日期');
    console.log('2. 预警条件：只检查730天内（2年内），这是正确的');
    console.log('3. 延迟退休计算可能有问题');
    
    // 测试第一个人的计算
    const testPerson = shouldHaveWarnings[0];
    console.log(`\n🧪 测试${testPerson.姓名}的计算:`);
    
    const birthDate = new Date(testPerson.出生日期);
    const birthYear = birthDate.getFullYear();
    
    console.log(`  出生年份: ${birthYear}`);
    console.log(`  当前年龄: ${testPerson.当前年龄}岁`);
    console.log(`  基础退休年龄: ${testPerson.退休年龄}岁`);
    
    // 模拟延迟退休计算
    let totalDelayMonths = 0;
    if (birthYear <= 1964) {
      totalDelayMonths = 0;
    } else if (birthYear === 1965) {
      totalDelayMonths = 3;
    } else if (birthYear >= 1966 && birthYear <= 1970) {
      totalDelayMonths = 3;
      const extraYears = birthYear - 1965;
      const extraMonths = Math.min(extraYears * 2, 12);
      totalDelayMonths += extraMonths;
    } else if (birthYear >= 1971 && birthYear <= 1980) {
      totalDelayMonths = 3 + 12;
      const extraYears = birthYear - 1970;
      const extraMonths = Math.min(extraYears * 3, 24);
      totalDelayMonths += extraMonths;
    } else if (birthYear >= 1981) {
      totalDelayMonths = 3 + 12 + 24;
      const extraYears = birthYear - 1980;
      const extraMonths = Math.min(extraYears * 4, 24);
      totalDelayMonths += extraMonths;
    }
    
    const actualRetirementAge = testPerson.退休年龄 + (totalDelayMonths / 12);
    const retirementDate = new Date(birthDate);
    retirementDate.setFullYear(retirementDate.getFullYear() + Math.floor(actualRetirementAge));
    retirementDate.setMonth(retirementDate.getMonth() + Math.round((actualRetirementAge % 1) * 12));
    
    const daysRemaining = Math.ceil((retirementDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    
    console.log(`  延迟月数: ${totalDelayMonths}个月`);
    console.log(`  实际退休年龄: ${actualRetirementAge.toFixed(1)}岁`);
    console.log(`  实际退休日期: ${retirementDate.toLocaleDateString()}`);
    console.log(`  距离退休天数: ${daysRemaining}天`);
    console.log(`  是否在730天内: ${daysRemaining <= 730 ? '是' : '否'}`);
    
    if (daysRemaining <= 730) {
      console.log('  ✅ 应该生成预警');
    } else {
      console.log('  ❌ 不会生成预警（超过730天）');
    }
  } else {
    console.log('❌ 没有找到应该有预警的人员');
    console.log('可能的原因:');
    console.log('1. 所有人距离退休超过2年');
    console.log('2. 出生日期数据有问题');
    console.log('3. 延迟退休计算导致退休时间推迟太多');
  }
  
  db.close();
});
