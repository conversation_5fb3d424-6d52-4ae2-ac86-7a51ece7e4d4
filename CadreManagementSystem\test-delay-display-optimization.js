/**
 * 测试延迟信息的智能显示优化
 */

console.log('🎯 测试延迟信息的智能显示优化...\n');

// 模拟延迟信息智能显示函数
function formatDelayInfo(delayYears, delayMonths) {
  if (delayYears > 0) {
    // 大于1年：显示X年X个月
    return `${delayYears}年${delayMonths}个月`;
  } else {
    // 小于1年：只显示X个月
    return `${delayMonths}个月`;
  }
}

// 测试不同延迟时间的显示格式
const delayTestCases = [
  { name: '无延迟', years: 0, months: 0 },
  { name: '延迟2个月', years: 0, months: 2 },
  { name: '延迟6个月', years: 0, months: 6 },
  { name: '延迟11个月', years: 0, months: 11 },
  { name: '延迟1年0个月', years: 1, months: 0 },
  { name: '延迟1年3个月', years: 1, months: 3 },
  { name: '延迟2年6个月', years: 2, months: 6 },
  { name: '延迟5年0个月', years: 5, months: 0 }
];

console.log('📊 延迟信息显示格式测试：\n');

delayTestCases.forEach((testCase, index) => {
  const formattedDelay = formatDelayInfo(testCase.years, testCase.months);
  
  console.log(`${index + 1}. ${testCase.name}:`);
  console.log(`   原始数据: ${testCase.years}年${testCase.months}个月`);
  console.log(`   📱 显示格式: 延迟${formattedDelay}（剩余[红色]X天[/红色]）`);
  
  // 显示逻辑说明
  if (testCase.years > 0) {
    console.log(`   💡 逻辑: 大于1年，显示年月`);
  } else {
    console.log(`   💡 逻辑: 小于1年，仅显示月份`);
  }
  
  console.log('');
});

// 模拟完整的个人信息卡片显示
console.log('👥 完整卡片显示示例：\n');

const cardExamples = [
  {
    name: '韦树林',
    unit: '晋圣公司',
    position: '纪委副书记兼信访案管室主任',
    urgencyLevel: '紧急',
    warningType: 'retirement_warning',
    delayYears: 0,
    delayMonths: 2,
    actualRetirementDays: 229,
    legalRetirementDays: 444
  },
  {
    name: '测试人员A',
    unit: '测试公司A',
    position: '测试职务A',
    urgencyLevel: '一般',
    warningType: 'second_line_warning',
    delayYears: 0,
    delayMonths: 6,
    actualRetirementDays: 1460,
    secondLineDays: 177
  },
  {
    name: '测试人员B',
    unit: '测试公司B',
    position: '测试职务B',
    urgencyLevel: '紧急',
    warningType: 'retirement_warning',
    delayYears: 1,
    delayMonths: 3,
    actualRetirementDays: 500,
    legalRetirementDays: 600
  },
  {
    name: '测试人员C',
    unit: '测试公司C',
    position: '测试职务C',
    urgencyLevel: '一般',
    warningType: 'second_line_warning',
    delayYears: 2,
    delayMonths: 0,
    actualRetirementDays: 1800,
    secondLineDays: 90
  }
];

cardExamples.forEach((person, index) => {
  console.log(`${index + 1}. ${person.name}的卡片显示：`);
  console.log(`┌─────────────────────────────────────────────────┐`);
  console.log(`│ ${person.name.padEnd(25)} [${person.urgencyLevel}] │`);
  console.log(`│ ${person.unit.padEnd(35)} │`);
  console.log(`│ ${person.position.padEnd(35)} │`);
  console.log(`│                                                 │`);
  
  if (person.warningType === 'retirement_warning') {
    const delayFormat = formatDelayInfo(person.delayYears, person.delayMonths);
    console.log(`│ 距法定退休年龄剩余1年2个月（剩余🔴${person.legalRetirementDays}天🔴）     │`);
    console.log(`│ 预计延迟退休年龄: 60.2岁                     │`);
    console.log(`│ 延迟${delayFormat}（剩余🔴${person.actualRetirementDays}天🔴）${' '.repeat(Math.max(0, 20 - delayFormat.length))} │`);
  } else {
    const delayFormat = formatDelayInfo(person.delayYears, person.delayMonths);
    console.log(`│ 距退居二线剩余3个月（剩余🔴${person.secondLineDays}天🔴）           │`);
    console.log(`│ 预计延迟退休年龄: 60.5岁                     │`);
    console.log(`│ 延迟${delayFormat}（剩余🔴${person.actualRetirementDays}天🔴）${' '.repeat(Math.max(0, 20 - delayFormat.length))} │`);
  }
  
  console.log(`└─────────────────────────────────────────────────┘`);
  console.log('');
});

console.log('✅ 测试完成！');
console.log('\n🎨 延迟信息优化总结：');
console.log('1. 📊 智能显示：');
console.log('   - 小于1年：延迟6个月（剩余X天）');
console.log('   - 大于1年：延迟1年3个月（剩余X天）');
console.log('   - 避免显示"延迟0年2个月"这种冗余格式');

console.log('\n2. 🎯 显示规则：');
console.log('   - delayYears > 0：显示"X年X个月"');
console.log('   - delayYears = 0：仅显示"X个月"');
console.log('   - 剩余天数始终用红色背景高亮');

console.log('\n3. 📱 实际效果：');
console.log('   - "延迟0年2个月" → "延迟2个月"');
console.log('   - "延迟0年6个月" → "延迟6个月"');
console.log('   - "延迟1年3个月" → "延迟1年3个月"（保持不变）');

console.log('\n🚀 现在延迟信息显示更加简洁明了！');
