@echo off
echo Starting Expo development server with debug...
set PATH=C:\Program Files\nodejs;%PATH%
cd /d F:\APPapk\CadreManagementSystem
echo Current directory: %CD%
echo Node version:
node --version
echo NPX version:
npx --version
echo Checking network configuration...
ipconfig | findstr "IPv4"
echo.
echo Starting Expo with LAN mode and debug output...
npx expo start --lan --port 8081 --clear --verbose
pause
