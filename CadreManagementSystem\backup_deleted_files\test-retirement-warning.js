/**
 * 退休预警功能测试脚本
 * 用于验证退休预警计算是否正常工作
 */

// 模拟退休计算逻辑
function calculateRetirementAge(birthYear, gender, position) {
  const baseRetirementAge = {
    '男': 60,
    '女': position === '干部' ? 55 : 50
  };

  const baseAge = baseRetirementAge[gender] || 60;
  
  // 延迟退休计算
  if (birthYear <= 1965) {
    return baseAge; // 无延迟
  } else if (birthYear <= 1970) {
    const delayMonths = Math.min((birthYear - 1965) * 2, 12);
    return baseAge + delayMonths / 12;
  } else if (birthYear <= 1980) {
    const delayMonths = Math.min(12 + (birthYear - 1970) * 3, 48);
    return baseAge + delayMonths / 12;
  } else {
    const delayMonths = Math.min(48 + (birthYear - 1980) * 4, 108);
    return baseAge + delayMonths / 12;
  }
}

function calculateAge(birthDate) {
  const today = new Date();
  const birth = new Date(birthDate);
  let age = today.getFullYear() - birth.getFullYear();
  const monthDiff = today.getMonth() - birth.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--;
  }
  
  return age;
}

function testRetirementWarning() {
  console.log('🧪 开始测试退休预警功能...\n');

  // 测试数据
  const testCadres = [
    {
      name: '王四顿',
      birthDate: '1964-03-15',
      gender: '男',
      position: '干部'
    },
    {
      name: '许国泰',
      birthDate: '1969-08-20',
      gender: '男',
      position: '干部'
    },
    {
      name: '张三',
      birthDate: '1980-05-10',
      gender: '女',
      position: '干部'
    },
    {
      name: '李四',
      birthDate: '1990-12-01',
      gender: '男',
      position: '工人'
    }
  ];

  testCadres.forEach(cadre => {
    const birthYear = new Date(cadre.birthDate).getFullYear();
    const currentAge = calculateAge(cadre.birthDate);
    const retirementAge = calculateRetirementAge(birthYear, cadre.gender, cadre.position);
    const secondLineAge = retirementAge - 5; // 退居二线年龄
    
    console.log(`👤 ${cadre.name}:`);
    console.log(`   出生日期: ${cadre.birthDate} (${birthYear}年)`);
    console.log(`   性别: ${cadre.gender}, 职位: ${cadre.position}`);
    console.log(`   当前年龄: ${currentAge}岁`);
    console.log(`   退居二线年龄: ${secondLineAge}岁`);
    console.log(`   正式退休年龄: ${retirementAge}岁`);
    
    // 判断预警类型
    if (currentAge >= retirementAge) {
      console.log(`   ⚠️ 状态: 已达到退休年龄 (紧急预警)`);
    } else if (currentAge >= secondLineAge) {
      console.log(`   ⚠️ 状态: 已达到退居二线年龄 (总预警)`);
    } else if (currentAge >= secondLineAge - 1) {
      console.log(`   ⚠️ 状态: 即将退居二线 (一般预警)`);
    } else {
      console.log(`   ✅ 状态: 正常`);
    }
    
    console.log('');
  });

  console.log('🎯 测试完成！');
  console.log('📝 预期结果:');
  console.log('   - 王四顿: 应该显示"已达到退休年龄"(紧急预警)');
  console.log('   - 许国泰: 应该显示"已达到退居二线年龄"(总预警)');
  console.log('   - 其他人员: 根据年龄显示相应状态');
}

// 运行测试
testRetirementWarning();
