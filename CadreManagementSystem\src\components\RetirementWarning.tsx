import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  RefreshControl,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { CadreInfo } from '../types';
import { CadreDao } from '../database/cadreDao';
import { ConfigurableRetirementCalculator, RetirementWarning as RetirementWarningData } from '../utils/configurableRetirementCalculator';
import { CadreDetailModal } from './CadreDetailModal';
import { CadreStatusModal } from './CadreStatusModal';
import { CadreStatusDao } from '../database/cadreStatusDao';

const { width } = Dimensions.get('window');

// 筛选类型
type FilterType = 'all' | 'near_retirement' | 'near_second_line' | 'already_second_line' | 'retired';

interface RetirementWarningProps {
  onCadrePress: (cadre: CadreInfo) => void;
  initialFilter?: FilterType;
}

export const RetirementWarning: React.FC<RetirementWarningProps> = ({
  onCadrePress,
  initialFilter = 'all'
}) => {
  const [warnings, setWarnings] = useState<RetirementWarningData[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedType, setSelectedType] = useState<FilterType>(initialFilter);

  // 弹窗状态
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [statusModalVisible, setStatusModalVisible] = useState(false);
  const [selectedCadre, setSelectedCadre] = useState<CadreInfo | null>(null);

  useEffect(() => {
    loadWarnings();
  }, []);

  useEffect(() => {
    if (initialFilter !== 'all') {
      setSelectedType(initialFilter);
    }
  }, [initialFilter]);

  // 加载退休预警数据
  const loadWarnings = async () => {
    try {
      console.log('🔍 退休预警组件：开始加载数据...');
      setLoading(true);

      const cadres = await CadreDao.getAllCadres();
      console.log(`📊 退休预警组件：获取到${cadres.length}条干部数据`);

      // 使用可配置退休计算引擎生成预警
      console.log('🔍 退休预警组件：开始生成预警...');
      const warningList = await ConfigurableRetirementCalculator.generateWarnings(cadres);
      console.log(`⚠️ 退休预警组件：生成${warningList.length}条预警信息`);

      if (warningList.length > 0) {
        console.log('📋 预警详情:');
        warningList.slice(0, 3).forEach((warning, index) => {
          console.log(`${index + 1}. ${warning.cadre.姓名} - ${warning.warningType} - ${warning.message}`);
        });
      }

      setWarnings(warningList);
    } catch (error) {
      console.error('❌ 加载退休预警数据失败:', error);
      setWarnings([]);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadWarnings();
    setRefreshing(false);
  };

  // 获取预警类型名称
  const getWarningTypeName = (type: FilterType): string => {
    switch (type) {
      case 'near_retirement': return '近两年退休';
      case 'near_second_line': return '近两年退居二线';
      case 'already_second_line': return '已退居二线';
      case 'retired': return '已退休';
      default: return '全部';
    }
  };

  // 根据剩余天数重新计算预警级别
  const calculateUrgencyLevel = useCallback((daysRemaining: number): string => {
    const absDays = Math.abs(daysRemaining);

    // 已退休或即将退休（3个月内）
    if (daysRemaining <= 0 || absDays <= 90) {
      return 'very_urgent';
    }
    // 3-6个月
    else if (absDays <= 180) {
      return 'urgent';
    }
    // 6个月以上
    else {
      return 'normal';
    }
  }, []);

  // 过滤和排序预警数据
  const filteredWarnings = useMemo(() => {
    // 先过滤
    const filtered = selectedType === 'all'
      ? warnings
      : warnings.filter(w => {
          // 基础类型匹配
          if (w.warningType !== selectedType) {
            return false;
          }

          const cadre = w.cadre;
          const manualStatus = cadre.退休状态;

          // 如果没有手动状态，按自动计算结果显示
          if (!manualStatus || manualStatus === 'active') {
            return true;
          }

          // 如果有手动状态，需要检查是否与当前筛选类型匹配
          switch (selectedType) {
            case 'near_retirement':
            case 'near_second_line':
              // 近两年预警：排除手动标记为其他状态的人员
              return false;

            case 'already_second_line':
              // 已退居二线筛选项：只显示手动标记为已退居二线的，或者没有手动状态的
              return manualStatus === 'second_line_retired';

            case 'retired':
              // 已退休筛选项：只显示手动标记为已退休的，或者没有手动状态且确实达到退休条件的
              return manualStatus === 'retired' || (!manualStatus || manualStatus === 'active');

            default:
              return true;
          }
        });

    // 再排序：按预警级别排序（非常紧急 > 紧急 > 一般）
    return filtered.sort((a, b) => {
      // 重新计算预警级别
      const urgencyA = calculateUrgencyLevel(a.daysRemaining);
      const urgencyB = calculateUrgencyLevel(b.daysRemaining);

      // 定义预警级别优先级：非常紧急 > 紧急 > 一般
      const urgencyOrder = {
        'very_urgent': 0,  // 非常紧急（≤90天）
        'urgent': 1,       // 紧急（91-180天）
        'normal': 2        // 一般（>180天）
      };

      const priorityA = urgencyOrder[urgencyA] ?? 3;
      const priorityB = urgencyOrder[urgencyB] ?? 3;

      // 首先按预警级别排序
      if (priorityA !== priorityB) {
        return priorityA - priorityB;
      }

      // 预警级别相同时，按剩余天数排序（天数越少越靠前）
      return Math.abs(a.daysRemaining) - Math.abs(b.daysRemaining);
    });
  }, [warnings, selectedType, calculateUrgencyLevel]);

  // 统计数据
  const stats = {
    total: warnings.length,
    retirement: warnings.filter(w => {
      if (w.warningType !== 'near_retirement') return false;
      const manualStatus = w.cadre.退休状态;
      return !manualStatus || manualStatus === 'active';
    }).length,
    secondLine: warnings.filter(w => {
      if (w.warningType !== 'near_second_line') return false;
      const manualStatus = w.cadre.退休状态;
      return !manualStatus || manualStatus === 'active';
    }).length,
    secondLineRetired: warnings.filter(w => {
      if (w.warningType !== 'already_second_line') return false;
      const manualStatus = w.cadre.退休状态;
      return !manualStatus || manualStatus === 'active' || manualStatus === 'second_line_retired';
    }).length,
    retired: warnings.filter(w => {
      if (w.warningType !== 'retired') return false;
      const manualStatus = w.cadre.退休状态;
      return !manualStatus || manualStatus === 'active' || manualStatus === 'retired';
    }).length,
  };

  // 处理干部点击
  const handleCadrePress = (cadre: CadreInfo) => {
    setSelectedCadre(cadre);
    setDetailModalVisible(true);
  };

  // 处理干部长按
  const handleCadreLongPress = (cadre: CadreInfo) => {
    setSelectedCadre(cadre);
    setStatusModalVisible(true);
  };

  // 处理状态更新
  const handleStatusUpdate = async (cadreId: string, newStatus: string) => {
    try {
      await CadreStatusDao.updateCadreStatus(cadreId, newStatus as any);
      await loadWarnings(); // 重新加载数据
      Alert.alert('成功', '干部状态已更新');
    } catch (error) {
      console.error('更新干部状态失败:', error);
      Alert.alert('错误', '更新干部状态失败');
    }
  };

  // 解析出生日期
  const parseBirthDate = (dateStr: string): Date | null => {
    if (!dateStr) return null;

    try {
      // 尝试多种日期格式
      const formats = [
        /^(\d{4})-(\d{1,2})-(\d{1,2})$/,
        /^(\d{4})\/(\d{1,2})\/(\d{1,2})$/,
        /^(\d{4})\.(\d{1,2})\.(\d{1,2})$/,
        /^(\d{4})年(\d{1,2})月(\d{1,2})日$/
      ];

      for (const format of formats) {
        const match = dateStr.match(format);
        if (match) {
          const year = parseInt(match[1]);
          const month = parseInt(match[2]) - 1; // JavaScript月份从0开始
          const day = parseInt(match[3]);
          return new Date(year, month, day);
        }
      }

      // 如果都不匹配，尝试直接解析
      const date = new Date(dateStr);
      return isNaN(date.getTime()) ? null : date;
    } catch (error) {
      console.error('解析出生日期失败:', dateStr, error);
      return null;
    }
  };

  // 格式化出生日期显示
  const formatBirthDate = (dateStr: string): string => {
    const date = parseBirthDate(dateStr);
    if (!date) return dateStr || '未知';

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}/${month}/${day}`;
  };

  // 获取紧急程度颜色
  const getUrgencyColor = (level: string): string => {
    switch (level) {
      case 'very_urgent': return '#FF3B30';
      case 'urgent': return '#FF9500';
      case 'normal': return '#007AFF';
      default: return '#8E8E93';
    }
  };

  // 获取紧急程度文本
  const getUrgencyText = (level: string): string => {
    switch (level) {
      case 'very_urgent': return '非常紧急';
      case 'urgent': return '紧急';
      case 'normal': return '一般';
      default: return '提醒';
    }
  };

  // 计算字体大小
  const calculateFontSize = (unit: string, position: string): number => {
    const totalLength = (unit || '').length + (position || '').length + 1; // +1 for separator
    if (totalLength > 20) return 11;
    if (totalLength > 15) return 12;
    return 13;
  };

  // 渲染预警卡片底部信息
  const renderWarningFooter = (warning: RetirementWarningData) => {
    const { warningType, daysRemaining, actualRetirementAge, delayInfo } = warning;

    // 计算延迟退休信息
    const totalDelayMonths = delayInfo?.totalDelayMonths || 0;
    const delayYears = Math.floor(totalDelayMonths / 12);
    const delayMonths = totalDelayMonths % 12;

    if (warningType === 'retired') {
      // 已退休显示
      const retiredDays = Math.abs(daysRemaining);
      return (
        <>
          <Text style={styles.retirementAgeText}>
            实际退休年龄: {actualRetirementAge.toFixed(1)}岁
          </Text>
          {totalDelayMonths > 0 && (
            <Text style={styles.delayInfoText}>
              延迟{totalDelayMonths >= 12 ? `${delayYears}年${delayMonths}个月` : `${totalDelayMonths}个月`}退休
            </Text>
          )}
          <View style={styles.timeInfoRow}>
            <Text style={styles.timeInfoText}>已退休</Text>
            <Text style={styles.daysHighlight}>
              {retiredDays}天
            </Text>
          </View>
        </>
      );
    } else {
      // 其他预警类型显示
      return (
        <>
          <Text style={styles.retirementAgeText}>
            预计延迟退休年龄: {actualRetirementAge.toFixed(1)}岁
          </Text>
          {totalDelayMonths > 0 && (
            <Text style={styles.delayInfoText}>
              延迟{totalDelayMonths >= 12 ? `${delayYears}年${delayMonths}个月` : `${totalDelayMonths}个月`}退休
            </Text>
          )}
          <View style={styles.timeInfoRow}>
            <Text style={styles.timeInfoText}>
              {daysRemaining > 0 ? '剩余' : '已超过'}
            </Text>
            <Text style={styles.daysHighlight}>
              {Math.abs(daysRemaining)}天
            </Text>
          </View>
        </>
      );
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>正在加载退休预警数据...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* 统计卡片 */}
      <View style={styles.statsContainer}>
        <View style={styles.statCard}>
          <Text style={[styles.statNumber, styles.statNumberRetirement]}>{stats.retirement}</Text>
          <Text style={[styles.statLabel, styles.statLabelPrimary]}>近两年退休</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={[styles.statNumber, styles.statNumberSecondLine]}>{stats.secondLine}</Text>
          <Text style={[styles.statLabel, styles.statLabelPrimary]}>近两年退居二线</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={[styles.statNumber, styles.statNumberSecondLineRetired]}>{stats.secondLineRetired}</Text>
          <Text style={[styles.statLabel, styles.statLabelSecondary]}>已退居二线</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={[styles.statNumber, styles.statNumberRetired]}>{stats.retired}</Text>
          <Text style={[styles.statLabel, styles.statLabelSecondary]}>已退休</Text>
        </View>
      </View>

      {/* 筛选按钮 */}
      <View style={styles.filterContainer}>
        {(['near_retirement', 'near_second_line', 'already_second_line', 'retired'] as const).map((type) => (
          <TouchableOpacity
            key={type}
            style={[
              styles.filterButton,
              selectedType === type && styles.filterButtonActive
            ]}
            onPress={() => setSelectedType(type)}
          >
            <Text style={[
              styles.filterButtonText,
              selectedType === type && styles.filterButtonTextActive
            ]}>
              {getWarningTypeName(type)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* 排序说明 */}
      {filteredWarnings.length > 0 && (
        <View style={styles.sortIndicator}>
          <Ionicons name="funnel-outline" size={16} color="#666" />
          <Text style={styles.sortText}>
            按预警级别排序：非常紧急 → 紧急 → 一般
          </Text>
        </View>
      )}

      {/* 预警列表 */}
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        <View style={styles.warningList}>
          {filteredWarnings.length === 0 ? (
            <View style={styles.emptyContainer}>
              <Ionicons name="information-circle-outline" size={48} color="#999" />
              <Text style={styles.emptyText}>
                {selectedType === 'all' ? '暂无退休预警信息' : `暂无${getWarningTypeName(selectedType)}信息`}
              </Text>
            </View>
          ) : (
            filteredWarnings.map((warning, index) => {
              // 重新计算预警级别
              const actualUrgencyLevel = calculateUrgencyLevel(warning.daysRemaining);

              return (
                <TouchableOpacity
                  key={index}
                  style={styles.warningCard}
                  onPress={() => handleCadrePress(warning.cadre)}
                  onLongPress={() => handleCadreLongPress(warning.cadre)}
                >
                  <View style={styles.warningHeader}>
                    <View style={styles.warningInfo}>
                      <View style={styles.nameRow}>
                        <Text style={styles.cadreName}>{warning.cadre.姓名}</Text>
                        <Text style={[
                          styles.birthDate,
                          { color: getUrgencyColor(actualUrgencyLevel) }
                        ]}>
                          {formatBirthDate(warning.cadre.出生日期)}
                        </Text>
                      </View>
                      <View style={styles.unitPositionRow}>
                        <Text
                          style={[
                            styles.unitPositionText,
                            { fontSize: calculateFontSize(warning.cadre.单位, warning.cadre.职务) }
                          ]}
                          numberOfLines={1}
                          ellipsizeMode="tail"
                        >
                          {`${warning.cadre.单位 || '未知单位'}|${warning.cadre.职务 || '未知职务'}`}
                        </Text>
                      </View>
                    </View>
                    <View style={[
                      styles.urgencyBadge,
                      { backgroundColor: getUrgencyColor(actualUrgencyLevel) }
                    ]}>
                      <Text style={styles.urgencyText}>
                        {getUrgencyText(actualUrgencyLevel)}
                      </Text>
                    </View>
                  </View>
                  <View style={styles.warningFooter}>
                    {renderWarningFooter(warning)}
                  </View>
                </TouchableOpacity>
              );
            })
          )}
        </View>
      </ScrollView>

      {/* 弹窗 */}
      {selectedCadre && (
        <>
          <CadreDetailModal
            visible={detailModalVisible}
            cadre={selectedCadre}
            onClose={() => {
              setDetailModalVisible(false);
              setSelectedCadre(null);
            }}
          />
          <CadreStatusModal
            visible={statusModalVisible}
            cadre={selectedCadre}
            onClose={() => {
              setStatusModalVisible(false);
              setSelectedCadre(null);
            }}
            onStatusUpdate={handleStatusUpdate}
          />
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 15,
    paddingVertical: 15,
    backgroundColor: '#fff',
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  statCard: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 15,
    paddingHorizontal: 8,
    backgroundColor: '#f8f9fa',
    marginHorizontal: 4,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  statNumber: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 6,
  },
  statNumberRetirement: {
    color: '#FF3B30',
  },
  statNumberSecondLine: {
    color: '#FF9500',
  },
  statNumberSecondLineRetired: {
    color: '#8E8E93',
  },
  statNumberRetired: {
    color: '#8E8E93',
  },
  statLabel: {
    fontSize: 13,
    textAlign: 'center',
    fontWeight: '500',
    color: '#333',
    lineHeight: 16,
  },
  statLabelPrimary: {
    color: '#333',
  },
  statLabelSecondary: {
    color: '#666',
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 15,
    paddingVertical: 10,
    backgroundColor: '#fff',
    marginBottom: 10,
  },
  filterButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginHorizontal: 4,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    alignItems: 'center',
  },
  filterButtonActive: {
    backgroundColor: '#007AFF',
  },
  filterButtonText: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
  filterButtonTextActive: {
    color: '#fff',
  },
  sortIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 8,
    backgroundColor: '#f8f9fa',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  sortText: {
    marginLeft: 6,
    fontSize: 13,
    color: '#666',
    fontStyle: 'italic',
  },
  scrollView: {
    flex: 1,
  },
  warningList: {
    paddingHorizontal: 15,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    marginTop: 10,
    fontSize: 16,
    color: '#999',
    textAlign: 'center',
  },
  warningCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 15,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  warningHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 10,
  },
  warningInfo: {
    flex: 1,
    marginRight: 10,
  },
  nameRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  cadreName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  birthDate: {
    fontSize: 14,
    fontWeight: '500',
  },
  unitPositionRow: {
    marginBottom: 4,
  },
  unitPositionText: {
    color: '#666',
    lineHeight: 18,
  },
  urgencyBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  urgencyText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  warningFooter: {
    marginTop: 8,
  },
  retirementAgeText: {
    fontSize: 14,
    color: '#333',
    marginBottom: 4,
  },
  delayInfoText: {
    fontSize: 13,
    color: '#666',
    marginBottom: 4,
  },
  timeInfoRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeInfoText: {
    fontSize: 14,
    color: '#333',
    marginRight: 4,
  },
  daysHighlight: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#FF3B30',
  },
});

export default RetirementWarning;
