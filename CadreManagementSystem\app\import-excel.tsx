import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Platform
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useTheme } from '../src/contexts/ThemeContext';
import { ExcelImporter } from '../src/utils/excelImporter';
import { CadreDao } from '../src/database/cadreDao';

export default function ImportExcelScreen() {
  const { theme } = useTheme();
  const [importing, setImporting] = useState(false);
  const [importResult, setImportResult] = useState<any>(null);

  const handleImportExcel = async () => {
    try {
      setImporting(true);
      setImportResult(null);

      console.log('开始Excel导入流程...');

      // 导入Excel文件
      const result = await ExcelImporter.selectAndImportExcel();

      console.log('导入结果:', result);

      if (result.success) {
        setImportResult(result);

        Alert.alert(
          '导入完成',
          `成功导入 ${result.successRows} 条记录\n失败 ${result.failedRows} 条记录`,
          [
            {
              text: '查看详情',
              onPress: () => showImportDetails(result)
            },
            {
              text: '确定',
              style: 'default'
            }
          ]
        );
      } else {
        console.error('导入失败:', result.errors);
        Alert.alert('导入失败', result.errors?.[0]?.message || '未知错误');
        setImportResult(result);
      }
    } catch (error) {
      console.error('导入Excel失败:', error);
      Alert.alert('导入失败', error instanceof Error ? error.message : '未知错误');
    } finally {
      setImporting(false);
    }
  };

  const showImportDetails = (result: any) => {
    const details = [
      `总行数: ${result.totalRows}`,
      `成功解析: ${result.successRows}`,
      `解析失败: ${result.failedRows}`
    ].join('\n');

    let errorDetails = '';
    if (result.errors && result.errors.length > 0) {
      errorDetails = '\n\n错误详情:\n' + result.errors.slice(0, 5).map((err: any) =>
        `第${err.row}行 ${err.field}: ${err.message}`
      ).join('\n');
      if (result.errors.length > 5) {
        errorDetails += '\n...';
      }
    }

    Alert.alert('导入详情', details + errorDetails);
  };

  const handleDownloadTemplate = async () => {
    try {
      Alert.alert(
        '下载模板',
        Platform.OS === 'web'
          ? '在Web环境下，模板数据已自动加载到导入功能中'
          : '模板下载功能开发中...',
        [{ text: '确定' }]
      );
    } catch (error) {
      Alert.alert('下载失败', '模板下载失败');
    }
  };

  const handleClearData = async () => {
    Alert.alert(
      '清空数据',
      '确定要清空所有干部数据吗？此操作不可恢复！',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '确定清空',
          style: 'destructive',
          onPress: async () => {
            try {
              setImporting(true);
              await CadreDao.clearAllCadres();
              Alert.alert('成功', '所有干部数据已清空，现在可以重新导入');
            } catch (error) {
              Alert.alert('错误', '清空数据失败: ' + error);
            } finally {
              setImporting(false);
            }
          }
        }
      ]
    );
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 16,
      backgroundColor: theme.colors.primary,
      paddingTop: 50,
    },
    backButton: {
      marginRight: 16,
    },
    headerTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: '#FFF',
    },
    content: {
      flex: 1,
      padding: 16,
    },
    section: {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 16,
      marginBottom: 16,
      elevation: 2,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    sectionTitle: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 12,
    },
    button: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.primary,
      padding: 16,
      borderRadius: 8,
      marginBottom: 12,
    },
    buttonSecondary: {
      backgroundColor: theme.colors.secondary,
    },
    buttonDanger: {
      backgroundColor: '#FF3B30',
    },
    buttonText: {
      color: '#FFF',
      fontSize: 16,
      fontWeight: '600',
      marginLeft: 8,
    },
    infoText: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      lineHeight: 20,
      marginBottom: 8,
    },
    resultContainer: {
      backgroundColor: theme.colors.surface,
      borderRadius: 8,
      padding: 16,
      marginTop: 16,
    },
    resultTitle: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 8,
    },
    resultText: {
      fontSize: 14,
      color: theme.colors.text,
      marginBottom: 4,
    },
    loadingContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      padding: 20,
    },
    loadingText: {
      marginLeft: 12,
      fontSize: 16,
      color: theme.colors.text,
    },
  });

  return (
    <View style={styles.container}>
      {/* 头部 */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="arrow-back" size={24} color="#FFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Excel导入</Text>
      </View>

      <ScrollView style={styles.content}>
        {/* 使用说明 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>使用说明</Text>
          <Text style={styles.infoText}>
            1. 请使用标准的Excel模板格式
          </Text>
          <Text style={styles.infoText}>
            2. 必填字段：姓名
          </Text>
          <Text style={styles.infoText}>
            3. 身份证号码会自动验证格式
          </Text>
          <Text style={styles.infoText}>
            4. 支持批量导入，建议单次不超过1000条记录
          </Text>
        </View>

        {/* 操作按钮 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>操作</Text>
          
          <TouchableOpacity 
            style={[styles.button, styles.buttonSecondary]}
            onPress={handleDownloadTemplate}
          >
            <Ionicons name="download" size={20} color="#FFF" />
            <Text style={styles.buttonText}>下载模板</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.button}
            onPress={handleImportExcel}
            disabled={importing}
          >
            <Ionicons name="cloud-upload" size={20} color="#FFF" />
            <Text style={styles.buttonText}>
              {importing ? '导入中...' : '选择文件导入'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.button, styles.buttonDanger]}
            onPress={handleClearData}
            disabled={importing}
          >
            <Ionicons name="trash" size={20} color="#FFF" />
            <Text style={styles.buttonText}>清空所有数据</Text>
          </TouchableOpacity>
        </View>

        {/* 加载状态 */}
        {importing && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.colors.primary} />
            <Text style={styles.loadingText}>正在导入数据...</Text>
          </View>
        )}

        {/* 导入结果 */}
        {importResult && (
          <View style={styles.resultContainer}>
            <Text style={styles.resultTitle}>导入结果</Text>
            <Text style={styles.resultText}>
              总行数: {importResult.totalRows}
            </Text>
            <Text style={styles.resultText}>
              成功解析: {importResult.successRows}
            </Text>
            <Text style={styles.resultText}>
              解析失败: {importResult.failedRows}
            </Text>
            <Text style={[styles.resultText, { color: importResult.success ? '#34C759' : '#FF3B30' }]}>
              状态: {importResult.success ? '导入成功' : '导入失败'}
            </Text>
            {importResult.errors && importResult.errors.length > 0 && (
              <Text style={[styles.resultText, { color: '#FF3B30', fontSize: 12 }]}>
                错误: {importResult.errors[0].message}
              </Text>
            )}
          </View>
        )}
      </ScrollView>
    </View>
  );
}
