import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Switch,
  ActivityIndicator
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { RetirementConfigDao } from '../src/database/retirementConfigDao';
import { 
  RetirementConfig, 
  SecondLineRule, 
  RetirementRule, 
  RetiredRule,
  DelayRetirementConfig 
} from '../src/types/retirementConfig';

export default function RetirementSettingsScreen() {
  const [config, setConfig] = useState<RetirementConfig | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadConfig();
  }, []);

  const loadConfig = async () => {
    try {
      console.log('🔧 加载退休配置...');
      const retirementConfig = await RetirementConfigDao.getConfig();
      setConfig(retirementConfig);
      console.log('✅ 退休配置加载成功');
    } catch (error) {
      console.error('❌ 加载退休配置失败:', error);
      Alert.alert('错误', '加载退休配置失败');
    } finally {
      setLoading(false);
    }
  };

  const toggleRuleEnabled = async (ruleType: string, ruleId: string, enabled: boolean) => {
    try {
      if (ruleType === 'second_line') {
        await RetirementConfigDao.updateSecondLineRule(ruleId, { enabled });
      } else if (ruleType === 'retirement') {
        await RetirementConfigDao.updateRetirementRule(ruleId, { enabled });
      }
      await loadConfig();
      Alert.alert('成功', '规则状态已更新');
    } catch (error) {
      Alert.alert('错误', '更新规则失败');
    }
  };

  const resetToDefault = () => {
    Alert.alert(
      '重置确认',
      '确定要重置为默认配置吗？这将清除所有自定义设置。',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '确定',
          style: 'destructive',
          onPress: async () => {
            await RetirementConfigDao.resetToDefault();
            await loadConfig();
            Alert.alert('成功', '已重置为默认配置');
          }
        }
      ]
    );
  };

  const renderSecondLineRules = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>退居二线规则</Text>
      <Text style={styles.sectionDescription}>
        基于职级和年龄的退居二线预警规则
      </Text>
      
      {config?.secondLineRules.map((rule) => (
        <View key={rule.id} style={styles.ruleCard}>
          <View style={styles.ruleHeader}>
            <Text style={styles.ruleName}>{rule.name}</Text>
            <Switch
              value={rule.enabled}
              onValueChange={(enabled) => toggleRuleEnabled('second_line', rule.id, enabled)}
            />
          </View>
          <Text style={styles.ruleDescription}>{rule.description}</Text>
          <Text style={styles.ruleDetails}>
            年龄范围: {rule.minAge}-{rule.maxAge}岁
          </Text>
          <Text style={styles.ruleDetails}>
            适用职级: {rule.positionLevels.join(', ')}
          </Text>
        </View>
      ))}
    </View>
  );

  const renderRetirementRules = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>退休预警规则</Text>
      <Text style={styles.sectionDescription}>
        基于年龄的退休预警规则，结合延迟退休计算
      </Text>
      
      {config?.retirementRules.map((rule) => (
        <View key={rule.id} style={styles.ruleCard}>
          <View style={styles.ruleHeader}>
            <Text style={styles.ruleName}>{rule.name}</Text>
            <Switch
              value={rule.enabled}
              onValueChange={(enabled) => toggleRuleEnabled('retirement', rule.id, enabled)}
            />
          </View>
          <Text style={styles.ruleDescription}>{rule.description}</Text>
          <Text style={styles.ruleDetails}>
            年龄范围: {rule.minAge}-{rule.maxAge}岁
          </Text>
        </View>
      ))}
    </View>
  );

  const renderRetiredRules = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>已退休规则</Text>
      <Text style={styles.sectionDescription}>
        确定已退休人员的规则
      </Text>
      
      {config?.retiredRules.map((rule) => (
        <View key={rule.id} style={styles.ruleCard}>
          <View style={styles.ruleHeader}>
            <Text style={styles.ruleName}>{rule.name}</Text>
            <Switch value={rule.enabled} disabled />
          </View>
          <Text style={styles.ruleDescription}>{rule.description}</Text>
          <Text style={styles.ruleDetails}>
            最小年龄: {rule.minAge}岁
          </Text>
        </View>
      ))}
    </View>
  );

  const renderDelayConfigs = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>延迟退休配置</Text>
      <Text style={styles.sectionDescription}>
        根据出生年份的延迟退休政策
      </Text>
      
      {config?.delayConfigs.map((delayConfig) => (
        <View key={delayConfig.id} style={styles.ruleCard}>
          <View style={styles.ruleHeader}>
            <Text style={styles.ruleName}>
              {delayConfig.birthYearStart === 0 ? '1965年及以前' : 
               delayConfig.birthYearEnd === 9999 ? `${delayConfig.birthYearStart}年及以后` :
               `${delayConfig.birthYearStart}-${delayConfig.birthYearEnd}年`}出生
            </Text>
            <Switch value={delayConfig.enabled} disabled />
          </View>
          <Text style={styles.ruleDescription}>{delayConfig.description}</Text>
          <Text style={styles.ruleDetails}>
            每年延迟: {delayConfig.delayMonthsPerYear}个月，最多延迟: {Math.floor(delayConfig.maxDelayMonths / 12)}年{delayConfig.maxDelayMonths % 12}个月
          </Text>
        </View>
      ))}
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>加载配置中...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color="#007AFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>退休规则设置</Text>
        <TouchableOpacity onPress={resetToDefault}>
          <Text style={styles.resetButton}>重置</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        <View style={styles.infoCard}>
          <Ionicons name="information-circle" size={24} color="#007AFF" />
          <Text style={styles.infoText}>
            这里可以配置退休预警的规则。退居二线规则基于职级和年龄，退休预警规则基于年龄并结合延迟退休计算。
          </Text>
        </View>

        {renderSecondLineRules()}
        {renderRetirementRules()}
        {renderRetiredRules()}
        {renderDelayConfigs()}
        
        <View style={styles.footer}>
          <Text style={styles.footerText}>
            最后更新: {config?.lastUpdated ? new Date(config.lastUpdated).toLocaleString() : '未知'}
          </Text>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7'
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: '#FFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA'
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000'
  },
  resetButton: {
    fontSize: 16,
    color: '#FF3B30'
  },
  content: {
    flex: 1,
    padding: 20
  },
  infoCard: {
    flexDirection: 'row',
    backgroundColor: '#E3F2FD',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    alignItems: 'flex-start'
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    color: '#1976D2',
    marginLeft: 12,
    lineHeight: 20
  },
  section: {
    marginBottom: 30
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000',
    marginBottom: 8
  },
  sectionDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 15,
    lineHeight: 20
  },
  ruleCard: {
    backgroundColor: '#FFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3
  },
  ruleHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8
  },
  ruleName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000',
    flex: 1
  },
  ruleDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
    lineHeight: 20
  },
  ruleDetails: {
    fontSize: 12,
    color: '#999',
    marginBottom: 4
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F2F2F7'
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666'
  },
  footer: {
    paddingVertical: 20,
    alignItems: 'center'
  },
  footerText: {
    fontSize: 12,
    color: '#999'
  }
});
