/**
 * 测试应用实际使用的数据库
 */
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// 使用应用实际的数据库文件
const dbPath = path.join(__dirname, 'cadre_management.db');

console.log('🔍 检查应用实际数据库...');
console.log('数据库路径:', dbPath);

const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ 连接数据库失败:', err.message);
    return;
  }
  console.log('✅ 已连接到应用数据库');
});

// 检查表结构
db.all("SELECT name FROM sqlite_master WHERE type='table'", [], (err, tables) => {
  if (err) {
    console.error('❌ 查询表失败:', err.message);
    return;
  }
  
  console.log('\n📋 数据库中的表:');
  tables.forEach(table => {
    console.log(`- ${table.name}`);
  });
  
  // 检查cadres表的数据
  db.all("SELECT COUNT(*) as count FROM cadres", [], (err, result) => {
    if (err) {
      console.error('❌ 查询cadres表失败:', err.message);
      return;
    }
    
    const count = result[0].count;
    console.log(`\n📊 cadres表中有 ${count} 条记录`);
    
    if (count > 0) {
      // 显示前10条记录
      db.all("SELECT 姓名, 性别, 出生日期, 现职级, 单位 FROM cadres LIMIT 10", [], (err, rows) => {
        if (err) {
          console.error('❌ 查询数据失败:', err.message);
          return;
        }
        
        console.log('\n📋 前10条干部数据:');
        console.log('姓名     | 性别 | 出生日期   | 现职级           | 单位');
        console.log('-'.repeat(80));
        rows.forEach(row => {
          console.log(`${(row.姓名 || '').padEnd(8)} | ${(row.性别 || '').padEnd(2)} | ${(row.出生日期 || '').padEnd(10)} | ${(row.现职级 || '').padEnd(15)} | ${(row.单位 || '').substring(0, 20)}`);
        });
        
        // 测试退休预警计算
        console.log('\n🔍 测试退休预警计算...');
        testRetirementWarnings(rows);
      });
    } else {
      console.log('❌ cadres表中没有数据！');
    }
  });
});

function testRetirementWarnings(cadres) {
  console.log('\n⚠️ 退休预警分析:');
  console.log('='.repeat(60));
  
  const today = new Date();
  let warningCount = 0;
  
  cadres.forEach(cadre => {
    const warning = calculateWarning(cadre, today);
    if (warning) {
      warningCount++;
      console.log(`🚨 ${cadre.姓名}: ${warning}`);
    } else {
      console.log(`✅ ${cadre.姓名}: 无需预警`);
    }
  });
  
  console.log(`\n📊 总计: ${warningCount}/${cadres.length} 人需要预警`);
  
  if (warningCount === 0) {
    console.log('\n❌ 没有生成任何预警！可能的原因:');
    console.log('1. 出生日期格式问题');
    console.log('2. 退休计算逻辑问题');
    console.log('3. 所有人距离退休超过5年');
  }
  
  db.close();
}

function calculateWarning(cadre, today) {
  if (!cadre.出生日期) {
    return null;
  }
  
  // 尝试解析出生日期
  const birthDate = new Date(cadre.出生日期);
  if (isNaN(birthDate.getTime())) {
    console.log(`❌ ${cadre.姓名}: 出生日期格式错误 '${cadre.出生日期}'`);
    return null;
  }
  
  // 计算年龄
  const age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  let currentAge = age;
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    currentAge--;
  }
  
  // 确定退休年龄
  let retirementAge = 60; // 默认男性
  if (cadre.性别 === '女') {
    const position = cadre.现职级 || '';
    retirementAge = position.includes('工人') || position.includes('操作') ? 50 : 55;
  }
  
  // 计算退休日期
  const retirementDate = new Date(birthDate);
  retirementDate.setFullYear(retirementDate.getFullYear() + retirementAge);
  
  // 计算距离退休天数
  const daysUntilRetirement = Math.ceil((retirementDate - today) / (1000 * 60 * 60 * 24));
  
  // 生成预警
  if (daysUntilRetirement <= 0) {
    return `已退休 (${currentAge}岁)`;
  } else if (daysUntilRetirement <= 90) {
    return `紧急预警 - ${daysUntilRetirement}天后退休`;
  } else if (daysUntilRetirement <= 730) {
    return `近两年退休 - ${Math.ceil(daysUntilRetirement / 365)}年后退休`;
  } else if (daysUntilRetirement <= 1825) {
    return `退居二线预警 - ${Math.ceil(daysUntilRetirement / 365)}年后退休`;
  }
  
  return null;
}
