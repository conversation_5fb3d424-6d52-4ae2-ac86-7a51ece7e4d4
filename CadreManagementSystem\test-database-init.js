/**
 * 测试数据库初始化和数据导入
 */
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, 'cadre_management.db');

console.log('🔍 测试数据库初始化...');
console.log('数据库路径:', dbPath);

const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ 连接数据库失败:', err.message);
    return;
  }
  console.log('✅ 已连接到数据库');
});

// 创建表结构（模拟应用的初始化）
db.serialize(() => {
  console.log('🔧 创建表结构...');
  
  // 创建干部信息表
  db.run(`
    CREATE TABLE IF NOT EXISTS cadres (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      序号 TEXT,
      单位 TEXT,
      姓名 TEXT NOT NULL,
      职务 TEXT,
      性别 TEXT,
      民族 TEXT,
      籍贯 TEXT,
      出生日期 TEXT,
      参加工作时间 TEXT,
      政治面貌 TEXT,
      入党时间 TEXT,
      全日制教育 TEXT,
      毕业院校系及专业 TEXT,
      在职教育 TEXT,
      毕业院校系及专业_在职 TEXT,
      专业技术职务 TEXT,
      现职级 TEXT,
      任现职时间 TEXT,
      任现职级时间 TEXT,
      工作简历 TEXT,
      身份证号 TEXT UNIQUE,
      身份证号码校验正误 TEXT,
      联系方式 TEXT,
      获得奖励荣誉情况 TEXT,
      党纪政纪处分情况 TEXT,
      备注 TEXT,
      照片路径 TEXT,
      退休状态 TEXT DEFAULT 'active',
      创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
      更新时间 DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `, (err) => {
    if (err) {
      console.error('❌ 创建cadres表失败:', err.message);
      return;
    }
    console.log('✅ cadres表创建成功');
  });

  // 插入测试数据
  console.log('📊 插入测试数据...');
  
  const testData = [
    {
      姓名: '李财茂',
      性别: '男',
      出生日期: '1965-10-15',
      现职级: '处长',
      单位: '测试单位',
      职务: '处长'
    },
    {
      姓名: '武海珠',
      性别: '女',
      出生日期: '1966-05-20',
      现职级: '副处长',
      单位: '测试单位',
      职务: '副处长'
    },
    {
      姓名: '王四顿',
      性别: '男',
      出生日期: '1964-03-15',
      现职级: '处长',
      单位: '测试单位',
      职务: '处长'
    },
    {
      姓名: '张建国',
      性别: '男',
      出生日期: '1965-04-01',
      现职级: '主任',
      单位: '测试单位',
      职务: '主任'
    },
    {
      姓名: '刘秀英',
      性别: '女',
      出生日期: '1975-12-25',
      现职级: '操作员',
      单位: '测试单位',
      职务: '操作员'
    }
  ];

  const stmt = db.prepare(`
    INSERT OR REPLACE INTO cadres (姓名, 性别, 出生日期, 现职级, 单位, 职务)
    VALUES (?, ?, ?, ?, ?, ?)
  `);

  testData.forEach(data => {
    stmt.run([data.姓名, data.性别, data.出生日期, data.现职级, data.单位, data.职务], (err) => {
      if (err) {
        console.error(`❌ 插入${data.姓名}失败:`, err.message);
      } else {
        console.log(`✅ 插入${data.姓名}成功`);
      }
    });
  });

  stmt.finalize();

  // 验证数据
  setTimeout(() => {
    db.all("SELECT COUNT(*) as count FROM cadres", [], (err, result) => {
      if (err) {
        console.error('❌ 查询数据失败:', err.message);
        return;
      }
      
      const count = result[0].count;
      console.log(`\n📊 数据库中现在有 ${count} 条记录`);
      
      if (count > 0) {
        db.all("SELECT 姓名, 性别, 出生日期, 现职级 FROM cadres", [], (err, rows) => {
          if (err) {
            console.error('❌ 查询详细数据失败:', err.message);
            return;
          }
          
          console.log('\n📋 数据库中的干部信息:');
          console.log('姓名     | 性别 | 出生日期   | 现职级');
          console.log('-'.repeat(40));
          rows.forEach(row => {
            console.log(`${row.姓名.padEnd(8)} | ${row.性别.padEnd(2)} | ${row.出生日期.padEnd(10)} | ${row.现职级}`);
          });
          
          console.log('\n✅ 数据库初始化完成！');
          console.log('💡 现在重新启动应用，应该能看到退休预警数据了。');
          
          db.close();
        });
      } else {
        console.log('❌ 数据插入失败');
        db.close();
      }
    });
  }, 1000);
});
