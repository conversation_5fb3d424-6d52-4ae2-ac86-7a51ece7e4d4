/**
 * 测试新的可配置退休预警系统
 */

const XLSX = require('xlsx');
const path = require('path');

console.log('🧪 测试新的可配置退休预警系统\n');

// 模拟默认配置
const DEFAULT_CONFIG = {
  secondLineRules: [
    {
      id: 'default_second_line',
      name: '中层干部退居二线规则',
      positionLevels: ['中层正职', '中层副职', '正处', '副处', '处级', '科级'],
      minAge: 56,
      maxAge: 58,
      description: '中层正职、中层副职、正处、副处等职务人员56-58岁退居二线预警',
      enabled: true
    }
  ],
  retirementRules: [
    {
      id: 'default_retirement',
      name: '退休预警规则',
      minAge: 58,
      maxAge: 60,
      description: '58-60岁人员退休预警，结合延迟退休计算',
      enabled: true
    }
  ],
  retiredRules: [
    {
      id: 'default_retired',
      name: '已退休规则',
      minAge: 60,
      description: '大于60岁人员视为已退休',
      enabled: true
    }
  ],
  delayConfigs: [
    {
      id: 'delay_1965_before',
      birthYearStart: 0,
      birthYearEnd: 1965,
      delayMonthsPerYear: 0,
      maxDelayMonths: 0,
      description: '1965年及以前出生，不延迟退休',
      enabled: true
    },
    {
      id: 'delay_1966_1970',
      birthYearStart: 1966,
      birthYearEnd: 1970,
      delayMonthsPerYear: 2,
      maxDelayMonths: 12,
      description: '1966-1970年出生，每年延迟2个月，最多1年',
      enabled: true
    }
  ]
};

// 模拟计算函数
function parseBirthDate(birthDateStr) {
  if (!birthDateStr && birthDateStr !== 0) {
    return null;
  }
  
  // 处理Excel日期序列号
  if (typeof birthDateStr === 'number') {
    try {
      const excelDate = new Date((birthDateStr - 25569) * 86400 * 1000);
      if (!isNaN(excelDate.getTime()) && excelDate.getFullYear() > 1900 && excelDate.getFullYear() < 2100) {
        return excelDate;
      }
    } catch (e) {
      // 忽略错误
    }
  }
  
  return null;
}

function calculateAge(birthDate) {
  const today = new Date();
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  
  return age;
}

function calculateDelayRetirement(birthYear, delayConfigs) {
  for (const config of delayConfigs) {
    if (birthYear >= config.birthYearStart && birthYear <= config.birthYearEnd) {
      const baseYear = 1965;
      const delayYearsFromBase = Math.max(0, birthYear - baseYear);
      const totalDelayMonths = Math.min(
        delayYearsFromBase * config.delayMonthsPerYear,
        config.maxDelayMonths
      );
      
      return {
        delayYears: Math.floor(totalDelayMonths / 12),
        delayMonths: totalDelayMonths % 12,
        totalDelayMonths,
        originalRetirementAge: 60,
        actualRetirementAge: 60 + (totalDelayMonths / 12)
      };
    }
  }
  
  return {
    delayYears: 0,
    delayMonths: 0,
    totalDelayMonths: 0,
    originalRetirementAge: 60,
    actualRetirementAge: 60
  };
}

function matchSecondLineRule(cadre, age, rules) {
  for (const rule of rules) {
    if (age >= rule.minAge && age <= rule.maxAge) {
      const position = cadre.现职级 || cadre.职务 || '';
      const isPositionMatch = rule.positionLevels.some(level => 
        position.includes(level) || level.includes(position)
      );
      
      if (isPositionMatch) {
        return rule;
      }
    }
  }
  return null;
}

function matchRetirementRule(age, rules) {
  for (const rule of rules) {
    if (age >= rule.minAge && age <= rule.maxAge) {
      return rule;
    }
  }
  return null;
}

function matchRetiredRule(age, rules) {
  for (const rule of rules) {
    if (age >= rule.minAge) {
      return rule;
    }
  }
  return null;
}

function calculateDaysUntilRetirement(birthDate, actualRetirementAge) {
  const retirementDate = new Date(birthDate);
  retirementDate.setFullYear(birthDate.getFullYear() + actualRetirementAge);
  
  const today = new Date();
  const diffTime = retirementDate.getTime() - today.getTime();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

function generateWarning(cadre) {
  const birthDate = parseBirthDate(cadre.出生日期);
  if (!birthDate) {
    console.log(`❌ ${cadre.姓名}: 无法解析出生日期 '${cadre.出生日期}'`);
    return null;
  }

  const age = calculateAge(birthDate);
  const birthYear = birthDate.getFullYear();

  const delayInfo = calculateDelayRetirement(birthYear, DEFAULT_CONFIG.delayConfigs);
  const daysUntilRetirement = calculateDaysUntilRetirement(birthDate, delayInfo.actualRetirementAge);

  // 按优先级检查规则匹配
  
  // 1. 检查已退休规则
  const retiredRule = matchRetiredRule(age, DEFAULT_CONFIG.retiredRules);
  if (retiredRule) {
    return {
      cadre,
      warningType: 'retired',
      urgencyLevel: 'info',
      description: `已达到退休年龄（${age}岁）`,
      daysUntilRetirement: Math.min(0, daysUntilRetirement),
      delayInfo,
      matchedRule: {
        ruleId: retiredRule.id,
        ruleName: retiredRule.name,
        ruleType: 'retired'
      }
    };
  }

  // 2. 检查退休预警规则
  const retirementRule = matchRetirementRule(age, DEFAULT_CONFIG.retirementRules);
  if (retirementRule) {
    const delayText = delayInfo.delayYears > 0 || delayInfo.delayMonths > 0 
      ? `，预计延迟${delayInfo.delayYears}年${delayInfo.delayMonths}个月`
      : '';
    
    return {
      cadre,
      warningType: 'retirement_warning',
      urgencyLevel: 'urgent',
      description: `退休预警（${age}岁）${delayText}，还剩${Math.max(0, daysUntilRetirement)}天退休`,
      daysUntilRetirement: Math.max(0, daysUntilRetirement),
      delayInfo,
      matchedRule: {
        ruleId: retirementRule.id,
        ruleName: retirementRule.name,
        ruleType: 'retirement'
      }
    };
  }

  // 3. 检查退居二线规则
  const secondLineRule = matchSecondLineRule(cadre, age, DEFAULT_CONFIG.secondLineRules);
  if (secondLineRule) {
    return {
      cadre,
      warningType: 'second_line_warning',
      urgencyLevel: 'normal',
      description: `退居二线预警（${age}岁，${cadre.现职级 || cadre.职务}）`,
      daysUntilRetirement: Math.max(0, daysUntilRetirement),
      delayInfo,
      matchedRule: {
        ruleId: secondLineRule.id,
        ruleName: secondLineRule.name,
        ruleType: 'second_line'
      }
    };
  }

  return null;
}

// 测试关键人员
console.log('🔍 测试关键人员：\n');

const testCases = [
  {
    姓名: '王四顿',
    出生日期: 23853, // Excel序列号 -> 1965/4/21
    职务: '调研员',
    现职级: '调研员'
  },
  {
    姓名: '许国泰',
    出生日期: 24368, // Excel序列号 -> 1966/9/18
    职务: '退居二线',
    现职级: '退居二线'
  },
  {
    姓名: '测试中层正职',
    出生日期: 25567, // 1970年左右
    职务: '中层正职',
    现职级: '中层正职'
  }
];

testCases.forEach((cadre, index) => {
  console.log(`${index + 1}. 测试 ${cadre.姓名}:`);
  
  const birthDate = parseBirthDate(cadre.出生日期);
  if (birthDate) {
    const age = calculateAge(birthDate);
    console.log(`   出生日期: ${birthDate.toLocaleDateString()}`);
    console.log(`   当前年龄: ${age}岁`);
    console.log(`   职务: ${cadre.职务}`);
    
    const warning = generateWarning(cadre);
    if (warning) {
      console.log(`   ⚠️ 预警类型: ${warning.warningType}`);
      console.log(`   📋 预警描述: ${warning.description}`);
      console.log(`   🎯 匹配规则: ${warning.matchedRule.ruleName}`);
      if (warning.delayInfo.delayYears > 0 || warning.delayInfo.delayMonths > 0) {
        console.log(`   ⏰ 延迟退休: ${warning.delayInfo.delayYears}年${warning.delayInfo.delayMonths}个月`);
      }
    } else {
      console.log(`   ✅ 无预警`);
    }
  } else {
    console.log(`   ❌ 无法解析出生日期`);
  }
  
  console.log('');
});

console.log('✅ 测试完成！');
console.log('\n📋 预期结果：');
console.log('- 王四顿: 已退休（60岁以上）');
console.log('- 许国泰: 退休预警（58-60岁范围）');
console.log('- 测试中层正职: 退居二线预警（如果年龄在56-58岁且职务匹配）');
