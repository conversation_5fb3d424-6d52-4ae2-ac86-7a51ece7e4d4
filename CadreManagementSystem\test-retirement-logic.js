/**
 * 测试退休逻辑计算准确性
 */

console.log('🧮 测试退休逻辑计算准确性...');
console.log('');

// 修复后的延迟退休计算逻辑
function calculateDelayRetirement(birthYear) {
  if (birthYear <= 1964) {
    return { delayYears: 0, delayMonths: 0, totalDelayMonths: 0 };
  }

  let totalDelayMonths = 0;

  if (birthYear === 1965) {
    // 1965年：基础延迟3个月
    totalDelayMonths = 3;
  } else if (birthYear >= 1966 && birthYear <= 1970) {
    // 1966-1970年：基础3个月 + 每年2个月
    totalDelayMonths = 3;
    const extraYears = birthYear - 1965;
    const extraMonths = extraYears * 2; // 不设上限，累积计算
    totalDelayMonths += extraMonths;
  } else if (birthYear >= 1971 && birthYear <= 1980) {
    // 1971-1980年：基础3个月 + 1966-1970年累积(5年×2个月) + 1971年开始每年3个月
    totalDelayMonths = 3; // 基础
    totalDelayMonths += 5 * 2; // 1966-1970年累积：10个月
    const extraYears = birthYear - 1970; // 1971年开始的年数
    const extraMonths = extraYears * 3; // 每年3个月
    totalDelayMonths += extraMonths;
  } else if (birthYear >= 1981) {
    // 1981年及以后：基础3个月 + 1966-1970年累积(10个月) + 1971-1980年累积(10年×3个月) + 1981年开始每年4个月
    totalDelayMonths = 3; // 基础
    totalDelayMonths += 5 * 2; // 1966-1970年累积：10个月
    totalDelayMonths += 10 * 3; // 1971-1980年累积：30个月
    const extraYears = birthYear - 1980; // 1981年开始的年数
    const extraMonths = extraYears * 4; // 每年4个月
    totalDelayMonths += extraMonths;
  }

  // 最大延迟5年（60个月）
  totalDelayMonths = Math.min(totalDelayMonths, 60);

  return {
    delayYears: Math.floor(totalDelayMonths / 12),
    delayMonths: totalDelayMonths % 12,
    totalDelayMonths
  };
}

// 测试关键案例 - 更新预期值
const testCases = [
  { name: '李财茂', birthYear: 1965, gender: '男', expected: '60.25' },
  { name: '申丽丽', birthYear: 1971, gender: '女', expected: '56.33' }, // 55 + 16个月/12 = 56.33
  { name: '王张荣', birthYear: 1965, gender: '男', expected: '60.25' },
  { name: '测试1966', birthYear: 1966, gender: '男', expected: '60.42' }, // 60 + 5个月/12 = 60.42
  { name: '测试1970', birthYear: 1970, gender: '男', expected: '61.08' }, // 60 + 13个月/12 = 61.08
  { name: '测试1975', birthYear: 1975, gender: '男', expected: '62.33' }, // 60 + 28个月/12 = 62.33
  { name: '测试1980', birthYear: 1980, gender: '男', expected: '63.58' }  // 60 + 43个月/12 = 63.58
];

console.log('📊 测试结果:');
console.log('');

testCases.forEach(testCase => {
  const delayInfo = calculateDelayRetirement(testCase.birthYear);
  const baseAge = testCase.gender === '男' ? 60 : 55;
  const actualAge = baseAge + (delayInfo.totalDelayMonths / 12);
  
  console.log(`👤 ${testCase.name} (${testCase.birthYear}年, ${testCase.gender}):`);
  console.log(`   基础退休年龄: ${baseAge}岁`);
  console.log(`   延迟: ${delayInfo.delayYears}年${delayInfo.delayMonths}个月 (总计${delayInfo.totalDelayMonths}个月)`);
  console.log(`   实际退休年龄: ${actualAge.toFixed(2)}岁`);
  console.log(`   预期: ${testCase.expected}`);
  const isCorrect = Math.abs(actualAge - parseFloat(testCase.expected)) < 0.01;
  console.log(`   状态: ${isCorrect ? '✅ 正确' : '❌ 错误'}`);
  console.log('');
});

// 验证1965年的计算
console.log('🔍 详细验证1965年计算:');
const delay1965 = calculateDelayRetirement(1965);
console.log(`1965年延迟: ${delay1965.totalDelayMonths}个月`);
console.log(`男性实际退休年龄: ${60 + delay1965.totalDelayMonths/12}岁`);
console.log(`应该是: 60.25岁 (60岁 + 3个月)`);
console.log('');

// 验证1971年的计算
console.log('🔍 详细验证1971年计算:');
const delay1971 = calculateDelayRetirement(1971);
console.log(`1971年延迟: ${delay1971.totalDelayMonths}个月`);
console.log(`女性实际退休年龄: ${55 + delay1971.totalDelayMonths/12}岁`);
console.log(`计算过程: 基础3个月 + 1966-1970年(5年×2个月=10个月) + 1971年(1年×3个月=3个月) = 16个月`);
console.log(`应该是: 56.33岁 (55岁 + 16个月)`);
console.log('');

console.log('🎯 测试完成！');
console.log('');
console.log('📋 发现的问题:');
console.log('1. 1971年的计算可能有误，当前逻辑是 3+12+3=18个月，但应该是 3+10+3=16个月');
console.log('2. 需要修正1971-1980年的计算基础');
console.log('');
console.log('💡 建议修复:');
console.log('1971-1980年应该是: 基础3个月 + 1966-1970年的累积(5年×2个月) + 1971年开始的累积(年数×3个月)');
