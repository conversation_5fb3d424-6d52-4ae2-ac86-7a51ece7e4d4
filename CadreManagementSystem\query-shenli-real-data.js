console.log('🔍 查询数据库中申丽丽的真实信息...\n');

const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// 数据库路径
const dbPath = path.join(__dirname, 'cadre_management.db');

// 连接数据库
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ 连接数据库失败:', err.message);
    return;
  }
  console.log('✅ 成功连接到数据库');
});

// 查询申丽丽的信息
db.get(`
  SELECT * FROM cadres WHERE 姓名 = '申丽丽'
`, [], (err, row) => {
  if (err) {
    console.error('❌ 查询申丽丽信息失败:', err.message);
    return;
  }
  
  if (row) {
    console.log(`📋 申丽丽的真实数据库信息：`);
    console.log(`姓名: ${row.姓名}`);
    console.log(`性别: ${row.性别}`);
    console.log(`出生日期: ${row.出生日期}`);
    console.log(`现职级: ${row.现职级}`);
    console.log(`职务: ${row.职务}`);
    console.log(`单位: ${row.单位}`);
    console.log(`退休状态: ${row.退休状态}`);
    console.log(`身份证号: ${row.身份证号}`);
    
    // 解析出生日期
    const birthDateStr = row.出生日期;
    let birthDate;
    
    if (birthDateStr) {
      // 尝试多种日期格式
      const cleanStr = birthDateStr.replace(/[年月日\-\.]/g, '/');
      birthDate = new Date(cleanStr);
      
      if (isNaN(birthDate.getTime())) {
        console.log('❌ 无法解析出生日期格式');
        return;
      }
    } else {
      console.log('❌ 出生日期为空');
      return;
    }
    
    const birthYear = birthDate.getFullYear();
    console.log(`\n📅 出生日期分析：`);
    console.log(`原始格式: ${birthDateStr}`);
    console.log(`解析结果: ${birthDate.toLocaleDateString()}`);
    console.log(`出生年份: ${birthYear}年`);
    
    // 计算当前年龄
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    console.log(`\n👤 年龄计算：`);
    console.log(`当前年龄: ${age}岁`);
    console.log(`性别: ${row.性别}`);
    console.log(`法定退休年龄: ${row.性别 === '女' ? '55岁' : '60岁'}`);
    
    // 计算延迟退休
    function calculateDelayRetirement(birthYear) {
      let totalDelayMonths = 3; // 基础延迟3个月
      
      if (birthYear >= 1966 && birthYear <= 1970) {
        const extraYears = birthYear - 1965;
        const extraMonths = Math.min(extraYears * 2, 12); // 每年2个月，最多1年
        totalDelayMonths += extraMonths;
      } else if (birthYear >= 1971 && birthYear <= 1980) {
        totalDelayMonths += 12; // 先加满1年
        const extraYears = birthYear - 1970;
        const extraMonths = Math.min(extraYears * 3, 24); // 每年3个月，最多2年
        totalDelayMonths += extraMonths;
      } else if (birthYear >= 1981) {
        totalDelayMonths += 36; // 先加满3年
        const extraYears = birthYear - 1980;
        const extraMonths = Math.min(extraYears * 4, 24); // 每年4个月，最多2年
        totalDelayMonths += extraMonths;
      }
      
      const maxDelayMonths = 60; // 最多延迟5年
      totalDelayMonths = Math.min(totalDelayMonths, maxDelayMonths);
      
      return {
        totalDelayMonths,
        delayYears: Math.floor(totalDelayMonths / 12),
        delayMonths: totalDelayMonths % 12
      };
    }
    
    const delayInfo = calculateDelayRetirement(birthYear);
    console.log(`\n⏰ 延迟退休计算：`);
    console.log(`出生年份: ${birthYear}年`);
    
    if (birthYear <= 1965) {
      console.log(`适用规则: 1965年及以前（基础延迟3个月）`);
    } else if (birthYear >= 1966 && birthYear <= 1970) {
      console.log(`适用规则: 1966-1970年（基础3个月 + 每年2个月）`);
      console.log(`计算过程: 基础3个月 + (${birthYear} - 1965) × 2个月 = 3 + ${(birthYear - 1965) * 2} = ${delayInfo.totalDelayMonths}个月`);
    } else if (birthYear >= 1971 && birthYear <= 1980) {
      console.log(`适用规则: 1971-1980年（基础3个月 + 1年 + 每年3个月）`);
      console.log(`计算过程: 基础3个月 + 12个月 + (${birthYear} - 1970) × 3个月 = 15 + ${(birthYear - 1970) * 3} = ${delayInfo.totalDelayMonths}个月`);
    } else if (birthYear >= 1981) {
      console.log(`适用规则: 1981年及以后（基础3个月 + 3年 + 每年4个月）`);
      console.log(`计算过程: 基础3个月 + 36个月 + (${birthYear} - 1980) × 4个月 = 39 + ${(birthYear - 1980) * 4} = ${delayInfo.totalDelayMonths}个月`);
    }
    
    console.log(`延迟月数: ${delayInfo.totalDelayMonths}个月`);
    console.log(`延迟格式: ${delayInfo.delayYears > 0 ? `${delayInfo.delayYears}年` : ''}${delayInfo.delayMonths}个月`);
    
    // 计算实际退休年龄和日期
    const baseRetirementAge = row.性别 === '女' ? 55 : 60;
    const actualRetirementAge = baseRetirementAge + (delayInfo.totalDelayMonths / 12);
    
    console.log(`\n🎯 实际退休计算：`);
    console.log(`法定退休年龄: ${baseRetirementAge}岁`);
    console.log(`延迟月数: ${delayInfo.totalDelayMonths}个月`);
    console.log(`实际退休年龄: ${baseRetirementAge} + ${delayInfo.totalDelayMonths}/12 = ${actualRetirementAge.toFixed(2)}岁`);
    
    // 计算实际退休日期
    const legalRetirementDate = new Date(birthDate);
    legalRetirementDate.setFullYear(birthDate.getFullYear() + baseRetirementAge);
    
    const actualRetirementDate = new Date(legalRetirementDate);
    actualRetirementDate.setMonth(actualRetirementDate.getMonth() + delayInfo.totalDelayMonths);
    
    console.log(`\n📅 退休日期计算：`);
    console.log(`法定退休日期: ${legalRetirementDate.getFullYear()}年${legalRetirementDate.getMonth() + 1}月${legalRetirementDate.getDate()}日`);
    console.log(`实际退休日期: ${actualRetirementDate.getFullYear()}年${actualRetirementDate.getMonth() + 1}月${actualRetirementDate.getDate()}日`);
    
    // 计算距离退休的天数
    const daysToLegal = Math.ceil((legalRetirementDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    const daysToActual = Math.ceil((actualRetirementDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    
    console.log(`\n⏳ 倒计时计算：`);
    console.log(`距离法定退休: ${daysToLegal}天`);
    console.log(`距离实际退休: ${daysToActual}天`);
    
    // 判断退休状态
    console.log(`\n📊 退休状态判断：`);
    if (age < baseRetirementAge) {
      console.log(`✅ 未到法定退休年龄（${age}岁 < ${baseRetirementAge}岁）`);
      if (daysToActual > 0) {
        console.log(`✅ 未到实际退休日期（还剩${daysToActual}天）`);
        console.log(`📋 应归类为: 近两年退休预警`);
      } else {
        console.log(`❌ 已超过实际退休日期（超过${Math.abs(daysToActual)}天）`);
        console.log(`📋 应归类为: 已退休`);
      }
    } else {
      console.log(`⚠️ 已达到法定退休年龄（${age}岁 >= ${baseRetirementAge}岁）`);
      if (daysToActual > 0) {
        console.log(`✅ 但未到实际退休日期（还剩${daysToActual}天）`);
        console.log(`📋 应归类为: 已超过法定退休年龄，但未到实际退休`);
      } else {
        console.log(`❌ 已超过实际退休日期（超过${Math.abs(daysToActual)}天）`);
        console.log(`📋 应归类为: 已退休`);
      }
    }
    
    console.log(`\n🎯 正确的预期结果：`);
    console.log(`申丽丽（${row.性别}，${birthDateStr}）：`);
    console.log(`- 当前年龄: ${age}岁`);
    console.log(`- 延迟月数: ${delayInfo.totalDelayMonths}个月`);
    console.log(`- 距离法定退休: ${daysToLegal}天`);
    console.log(`- 距离实际退休: ${daysToActual}天`);
    console.log(`- 状态: ${age < baseRetirementAge ? '未到法定退休年龄' : '已超过法定退休年龄'}，${daysToActual > 0 ? '未到实际退休日期' : '已超过实际退休日期'}`);
    
  } else {
    console.log('❌ 数据库中未找到申丽丽的信息');
  }
  
  // 关闭数据库连接
  db.close((err) => {
    if (err) {
      console.error('❌ 关闭数据库失败:', err.message);
    } else {
      console.log('\n✅ 数据库查询完成');
    }
  });
});

console.log('🔍 申丽丽真实数据查询完成！');
