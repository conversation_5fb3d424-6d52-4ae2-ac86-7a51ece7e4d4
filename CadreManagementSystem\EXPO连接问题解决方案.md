# EXPO开发服务器连接问题解决方案

## 🔍 问题诊断

如果EXPO服务器启动后没有显示地址、端口和二维码，通常是以下原因：

### 常见原因：
1. **服务器启动失败** - 端口被占用或配置问题
2. **输出被重定向** - 控制台输出没有正确显示
3. **网络配置问题** - 防火墙或网络设置阻止
4. **EXPO版本问题** - 需要更新或重新安装

## 🛠️ 解决方案

### 方案1：强制指定端口和地址
```cmd
cd /d F:\APPapk\CadreManagementSystem
npx expo start --port 8081 --host tunnel
```

### 方案2：使用局域网模式
```cmd
npx expo start --lan
```

### 方案3：使用隧道模式
```cmd
npx expo start --tunnel
```

### 方案4：清理缓存重新启动
```cmd
npx expo start --clear
```

## 📱 默认地址信息

### EXPO开发服务器默认配置：
- **默认端口**: `8081`
- **本地地址**: `http://localhost:8081`
- **局域网地址**: `http://[您的IP]:8081`
- **Metro Bundler**: `exp://[您的IP]:8081`

### 手动连接方法：

#### 方法1：获取本机IP地址
```cmd
ipconfig
```
查找您的局域网IP（通常是192.168.x.x），然后在雷电模拟器中访问：
`exp://[您的IP]:8081`

#### 方法2：使用localhost
在雷电模拟器的Expo Go中输入：
`exp://********:8081`
（********是Android模拟器访问主机的特殊IP）

## 🔧 详细排查步骤

### 步骤1：检查端口占用
```cmd
netstat -ano | findstr :8081
```
如果端口被占用，使用其他端口：
```cmd
npx expo start --port 8082
```

### 步骤2：检查防火墙
- 临时关闭Windows防火墙
- 或添加端口8081到防火墙例外

### 步骤3：重新安装EXPO CLI
```cmd
npm uninstall -g @expo/cli
npm install -g @expo/cli@latest
```

### 步骤4：使用开发者工具
```cmd
npx expo start --dev-client
```

## 🌐 Web界面访问

EXPO通常会在浏览器中打开开发者界面：
- 访问：`http://localhost:8081`
- 在此界面可以看到二维码和连接选项

## 📱 雷电模拟器连接步骤

### 1. 安装Expo Go
- 在雷电模拟器中打开Google Play Store
- 搜索并安装"Expo Go"

### 2. 手动输入连接地址
如果扫码不行，在Expo Go中：
- 点击"Enter URL manually"
- 输入：`exp://********:8081`
- 或输入：`exp://[您的局域网IP]:8081`

### 3. 使用ADB连接
```cmd
# 如果安装了Android SDK
adb connect 127.0.0.1:5555
npx expo start --android
```

## 🚀 推荐的启动命令

基于您的环境，建议使用：

```cmd
cd /d F:\APPapk\CadreManagementSystem
npx expo start --tunnel --port 8081
```

这个命令会：
- 使用隧道模式（绕过网络限制）
- 指定端口8081
- 提供公网可访问的URL

## 📋 如果仍然无法连接

### 备用方案：直接构建到模拟器
```cmd
# 直接在雷电模拟器中运行
npx expo run:android
```

### 或使用Web版本测试
```cmd
npx expo start --web
```
然后在浏览器中测试基本功能。

## 🔍 调试信息收集

如果问题持续，请运行以下命令收集信息：

```cmd
# 检查EXPO版本
npx expo --version

# 检查Node.js版本
node --version

# 检查网络配置
ipconfig /all

# 详细启动日志
npx expo start --verbose
```

---

## 🎯 立即尝试

请按以下顺序尝试：

1. **首先尝试隧道模式**：
   ```cmd
   npx expo start --tunnel
   ```

2. **如果不行，尝试指定端口**：
   ```cmd
   npx expo start --port 8081 --lan
   ```

3. **最后尝试直接运行**：
   ```cmd
   npx expo run:android
   ```

请告诉我哪个命令有效，我会根据结果进一步指导您！
