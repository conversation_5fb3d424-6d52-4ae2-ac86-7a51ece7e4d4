import { IdCardValidation } from '../types';

/**
 * 身份证号码验证工具类
 */
export class IdCardValidator {
  
  // 省份代码映射
  private static readonly PROVINCE_CODES: Record<string, string> = {
    '11': '北京', '12': '天津', '13': '河北', '14': '山西', '15': '内蒙古',
    '21': '辽宁', '22': '吉林', '23': '黑龙江', '31': '上海', '32': '江苏',
    '33': '浙江', '34': '安徽', '35': '福建', '36': '江西', '37': '山东',
    '41': '河南', '42': '湖北', '43': '湖南', '44': '广东', '45': '广西',
    '46': '海南', '50': '重庆', '51': '四川', '52': '贵州', '53': '云南',
    '54': '西藏', '61': '陕西', '62': '甘肃', '63': '青海', '64': '宁夏',
    '65': '新疆', '71': '台湾', '81': '香港', '82': '澳门', '91': '国外'
  };

  // 校验码权重
  private static readonly WEIGHTS = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
  
  // 校验码对应表
  private static readonly CHECK_CODES = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];

  /**
   * 验证身份证号码
   * @param idCard 身份证号码
   * @returns 验证结果
   */
  static validate(idCard: string): IdCardValidation {
    const errors: string[] = [];
    
    if (!idCard) {
      errors.push('身份证号码不能为空');
      return { isValid: false, errors };
    }

    // 去除空格并转换为大写
    const cleanIdCard = idCard.trim().toUpperCase();
    
    // 长度验证
    if (cleanIdCard.length !== 18) {
      errors.push('身份证号码长度必须为18位');
      return { isValid: false, errors };
    }

    // 格式验证
    if (!/^[0-9]{17}[0-9X]$/.test(cleanIdCard)) {
      errors.push('身份证号码格式不正确');
      return { isValid: false, errors };
    }

    // 省份代码验证
    const provinceCode = cleanIdCard.substring(0, 2);
    if (!this.PROVINCE_CODES[provinceCode]) {
      errors.push('省份代码不正确');
    }

    // 出生日期验证
    const birthDateStr = cleanIdCard.substring(6, 14);
    const year = parseInt(birthDateStr.substring(0, 4));
    const month = parseInt(birthDateStr.substring(4, 6));
    const day = parseInt(birthDateStr.substring(6, 8));
    
    const birthDate = new Date(year, month - 1, day);
    const currentDate = new Date();
    
    if (
      birthDate.getFullYear() !== year ||
      birthDate.getMonth() !== month - 1 ||
      birthDate.getDate() !== day ||
      year < 1900 ||
      year > currentDate.getFullYear() ||
      month < 1 || month > 12 ||
      day < 1 || day > 31 ||
      birthDate > currentDate
    ) {
      errors.push('出生日期不正确');
    }

    // 校验码验证
    const checkCodeValid = this.validateCheckCode(cleanIdCard);
    if (!checkCodeValid) {
      errors.push('校验码不正确');
    }

    // 计算年龄
    const age = this.calculateAge(birthDate);
    
    // 获取性别
    const gender = this.getGender(cleanIdCard);

    const isValid = errors.length === 0;
    
    return {
      isValid,
      birthDate: isValid ? this.formatDate(birthDate) : undefined,
      gender: isValid ? gender : undefined,
      age: isValid ? age : undefined,
      errors
    };
  }

  /**
   * 验证校验码
   * @param idCard 身份证号码
   * @returns 是否有效
   */
  private static validateCheckCode(idCard: string): boolean {
    const codes = idCard.split('');
    let sum = 0;
    
    for (let i = 0; i < 17; i++) {
      sum += parseInt(codes[i]) * this.WEIGHTS[i];
    }
    
    const remainder = sum % 11;
    const expectedCheckCode = this.CHECK_CODES[remainder];
    
    return codes[17] === expectedCheckCode;
  }

  /**
   * 获取性别
   * @param idCard 身份证号码
   * @returns 性别
   */
  private static getGender(idCard: string): string {
    const genderCode = parseInt(idCard.charAt(16));
    return genderCode % 2 === 0 ? '女' : '男';
  }

  /**
   * 计算年龄
   * @param birthDate 出生日期
   * @returns 年龄
   */
  private static calculateAge(birthDate: Date): number {
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    return age;
  }

  /**
   * 格式化日期
   * @param date 日期对象
   * @returns 格式化后的日期字符串
   */
  private static formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  /**
   * 从身份证号码提取出生日期
   * @param idCard 身份证号码
   * @returns 出生日期字符串
   */
  static extractBirthDate(idCard: string): string | null {
    if (!idCard || idCard.length !== 18) {
      return null;
    }
    
    const birthDateStr = idCard.substring(6, 14);
    const year = birthDateStr.substring(0, 4);
    const month = birthDateStr.substring(4, 6);
    const day = birthDateStr.substring(6, 8);
    
    return `${year}-${month}-${day}`;
  }

  /**
   * 从身份证号码提取性别
   * @param idCard 身份证号码
   * @returns 性别
   */
  static extractGender(idCard: string): string | null {
    if (!idCard || idCard.length !== 18) {
      return null;
    }
    
    const genderCode = parseInt(idCard.charAt(16));
    return genderCode % 2 === 0 ? '女' : '男';
  }

  /**
   * 计算当前年龄
   * @param idCard 身份证号码
   * @returns 年龄
   */
  static calculateCurrentAge(idCard: string): number | null {
    const birthDateStr = this.extractBirthDate(idCard);
    if (!birthDateStr) {
      return null;
    }
    
    const birthDate = new Date(birthDateStr);
    return this.calculateAge(birthDate);
  }

  /**
   * 批量验证身份证号码
   * @param idCards 身份证号码数组
   * @returns 验证结果数组
   */
  static batchValidate(idCards: string[]): IdCardValidation[] {
    return idCards.map(idCard => this.validate(idCard));
  }

  /**
   * 检查身份证号码是否重复
   * @param idCards 身份证号码数组
   * @returns 重复的身份证号码
   */
  static findDuplicates(idCards: string[]): string[] {
    const seen = new Set<string>();
    const duplicates = new Set<string>();
    
    for (const idCard of idCards) {
      const cleanIdCard = idCard.trim().toUpperCase();
      if (seen.has(cleanIdCard)) {
        duplicates.add(cleanIdCard);
      } else {
        seen.add(cleanIdCard);
      }
    }
    
    return Array.from(duplicates);
  }

  /**
   * 生成身份证号码校验报告
   * @param idCards 身份证号码数组
   * @returns 校验报告
   */
  static generateValidationReport(idCards: string[]) {
    const results = this.batchValidate(idCards);
    const duplicates = this.findDuplicates(idCards);
    
    const validCount = results.filter(r => r.isValid).length;
    const invalidCount = results.length - validCount;
    
    const errorSummary: Record<string, number> = {};
    results.forEach(result => {
      result.errors.forEach(error => {
        errorSummary[error] = (errorSummary[error] || 0) + 1;
      });
    });
    
    return {
      total: idCards.length,
      valid: validCount,
      invalid: invalidCount,
      duplicates: duplicates.length,
      duplicateList: duplicates,
      errorSummary,
      details: results.map((result, index) => ({
        idCard: idCards[index],
        ...result
      }))
    };
  }
}
