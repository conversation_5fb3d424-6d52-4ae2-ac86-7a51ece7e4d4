/**
 * 最终测试：完整的个人信息卡片 + 精准时间计算
 */

console.log('🎯 最终测试：完整的个人信息卡片 + 精准时间计算\n');

// 模拟完整的卡片显示效果
function simulateCardDisplay(person) {
  console.log(`📱 ${person.name}的个人信息卡片：`);
  console.log(`┌─────────────────────────────────────────────────┐`);
  console.log(`│ ${person.name.padEnd(25)} [${person.urgencyLevel}] │`);
  console.log(`│ ${person.unit.padEnd(35)} │`);
  console.log(`│ ${person.position.padEnd(35)} │`);
  console.log(`│                                                 │`);
  
  person.timeLines.forEach(line => {
    if (line.highlight) {
      // 模拟红色高亮显示
      const parts = line.text.split('[RED]');
      if (parts.length > 1) {
        const beforeRed = parts[0];
        const redPart = parts[1].split('[/RED]')[0];
        const afterRed = parts[1].split('[/RED]')[1] || '';
        console.log(`│ ${beforeRed}🔴${redPart}🔴${afterRed.padEnd(Math.max(0, 35 - beforeRed.length - redPart.length - afterRed.length))} │`);
      } else {
        console.log(`│ ${line.text.padEnd(35)} │`);
      }
    } else {
      console.log(`│ ${line.text.padEnd(35)} │`);
    }
  });
  
  console.log(`└─────────────────────────────────────────────────┘`);
  console.log('');
}

// 测试数据
const testPersons = [
  {
    name: '吴小方',
    unit: '晋圣三沟鑫都煤业',
    position: '副矿长（通风）',
    urgencyLevel: '一般',
    warningType: 'second_line_warning',
    remainingDays: 6,
    timeLines: [
      {
        text: '距退居二线剩余[RED]6天[/RED]',
        highlight: true
      },
      {
        text: '预计延迟退休年龄: 60.5岁',
        highlight: false
      },
      {
        text: '延迟0年6个月（剩余[RED]1460天[/RED]）',
        highlight: true
      }
    ]
  },
  {
    name: '韦树林',
    unit: '晋圣公司',
    position: '纪委副书记兼信访案管室主任',
    urgencyLevel: '紧急',
    warningType: 'retirement_warning',
    remainingDays: 444,
    timeLines: [
      {
        text: '距法定退休年龄剩余1年2个月（剩余[RED]444天[/RED]）',
        highlight: true
      },
      {
        text: '预计延迟退休年龄: 60.2岁',
        highlight: false
      },
      {
        text: '延迟0年2个月（剩余[RED]229天[/RED]）',
        highlight: true
      }
    ]
  },
  {
    name: '测试短期',
    unit: '测试公司',
    position: '测试职务',
    urgencyLevel: '紧急',
    warningType: 'second_line_warning',
    remainingDays: 25,
    timeLines: [
      {
        text: '距退居二线剩余[RED]25天[/RED]',
        highlight: true
      },
      {
        text: '预计延迟退休年龄: 60.0岁',
        highlight: false
      },
      {
        text: '延迟0年0个月（剩余[RED]1825天[/RED]）',
        highlight: true
      }
    ]
  },
  {
    name: '王四顿',
    unit: '晋圣凤红煤业',
    position: '调研员',
    urgencyLevel: '信息',
    warningType: 'retired',
    remainingDays: -30,
    timeLines: [
      {
        text: '已超过法定退休年龄',
        highlight: false
      },
      {
        text: '实际退休年龄: 60.0岁',
        highlight: false
      },
      {
        text: '已超过退休年龄[RED]30天[/RED]',
        highlight: true
      }
    ]
  }
];

// 显示所有测试卡片
testPersons.forEach((person, index) => {
  console.log(`${index + 1}. ${person.warningType === 'second_line_warning' ? '退居二线预警' : person.warningType === 'retirement_warning' ? '退休预警' : '已退休'}示例：`);
  simulateCardDisplay(person);
});

console.log('✅ 最终测试完成！');
console.log('\n🎨 优化总结：');
console.log('1. 📊 精准计算：');
console.log('   - 基于实际日期差计算剩余天数');
console.log('   - 智能显示格式：大于1年显示年月，大于1个月显示月份，不足1个月仅显示天数');
console.log('   - 避免了"1年0个月"这种不准确的显示');

console.log('\n2. 🎯 突出显示：');
console.log('   - 所有剩余天数用红色背景高亮');
console.log('   - 行内布局，文字和天数在同一行');
console.log('   - 视觉焦点明确，一眼就能看到关键信息');

console.log('\n3. 📱 美观布局：');
console.log('   - 姓名：18px，深色，加粗 - 最突出');
console.log('   - 单位：14px，中灰色 - 次要信息');
console.log('   - 职务：14px，浅灰色，中等加粗 - 补充信息');
console.log('   - 时间信息层次分明，重要性递减');

console.log('\n4. 🔍 智能逻辑：');
console.log('   - 6天 → "距退居二线剩余6天"（红色突出）');
console.log('   - 45天 → "距退居二线剩余1个月（剩余45天）"（红色突出天数）');
console.log('   - 444天 → "距法定退休年龄剩余1年2个月（剩余444天）"（红色突出天数）');

console.log('\n🚀 现在应用中的显示效果将更加精准和美观！');
