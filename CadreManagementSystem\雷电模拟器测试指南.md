# 雷电模拟器测试指南

## 🎯 测试准备

### 1. 环境要求
- **雷电模拟器**: 版本 9.0 或更高
- **Android版本**: Android 7.0 (API 24) 或更高
- **内存配置**: 建议 4GB 或更高
- **CPU核心**: 建议 4 核心或更高

### 2. 雷电模拟器配置
1. **打开雷电模拟器**
2. **设置模拟器参数**:
   - 分辨率: 1080x1920 (手机竖屏)
   - DPI: 320
   - 内存: 4096MB
   - CPU: 4核心
   - 启用VT虚拟化

3. **开启开发者选项**:
   - 设置 → 关于手机 → 连续点击版本号7次
   - 返回设置 → 开发者选项
   - 开启"USB调试"
   - 开启"允许USB安装"

## 🚀 启动测试

### 方法一：Expo开发服务器 (推荐)

1. **启动开发服务器**:
```bash
cd CadreManagementSystem
npm start
```

2. **在雷电模拟器中安装Expo Go**:
   - 打开模拟器中的浏览器
   - 访问: https://expo.dev/client
   - 下载并安装 Expo Go APK

3. **连接到开发服务器**:
   - 在Expo Go中扫描终端显示的二维码
   - 或者输入开发服务器地址 (通常是 exp://192.168.x.x:8081)

### 方法二：ADB连接

1. **检查ADB连接**:
```bash
adb devices
```

2. **如果未检测到设备**:
   - 确保雷电模拟器已开启ADB调试
   - 手动连接: `adb connect 127.0.0.1:5555`

3. **运行Android应用**:
```bash
npm run android
```

## 🎨 主题测试重点

### 1. 主题切换测试
- **进入设置页面** → 点击"主题设置"
- **测试四种主题**:
  - ✅ **高端主题**: 深色奢华风格
  - ✅ **商务主题**: 专业蓝色风格 (默认)
  - ✅ **简约主题**: 清新简洁风格
  - ✅ **经典主题**: 传统稳重风格

### 2. 主题效果验证
- **颜色一致性**: 检查所有页面颜色是否统一
- **文字可读性**: 确保文字在各主题下清晰可读
- **图标适配**: 验证图标颜色与主题匹配
- **阴影效果**: 检查卡片阴影在不同主题下的效果

### 3. 主题持久化测试
- 切换主题后关闭应用
- 重新打开应用验证主题是否保持
- 测试应用重启后主题设置的持久性

## 📱 功能测试清单

### 1. 首页测试
- ✅ 统计数据显示
- ✅ 快捷操作按钮
- ✅ 功能模块导航
- ✅ 下拉刷新功能
- ✅ 主题适配效果

### 2. 干部管理测试
- ✅ 干部列表加载
- ✅ 搜索和筛选功能
- ✅ 添加新干部
- ✅ 编辑干部信息
- ✅ 删除干部确认
- ✅ 分页加载

### 3. 退休预警测试
- ✅ 饼图数据展示
- ✅ 预警分类筛选
- ✅ 预警详情查看
- ✅ 倒计时显示
- ✅ 紧急程度标识

### 4. 设置功能测试
- ✅ 主题选择器
- ✅ 职级管理入口
- ✅ Excel导入入口
- ✅ 关于信息显示

## 🔧 常见问题解决

### 1. 模拟器连接问题
**问题**: ADB无法检测到雷电模拟器
**解决**:
```bash
# 重启ADB服务
adb kill-server
adb start-server

# 手动连接雷电模拟器
adb connect 127.0.0.1:5555
```

### 2. 应用安装失败
**问题**: APK安装失败或闪退
**解决**:
- 检查模拟器Android版本 (需要7.0+)
- 清除模拟器缓存并重启
- 确保开启了"允许未知来源安装"

### 3. 网络连接问题
**问题**: 无法连接到开发服务器
**解决**:
- 检查防火墙设置
- 确保电脑和模拟器在同一网络
- 使用IP地址而非localhost

### 4. 性能问题
**问题**: 应用运行卡顿
**解决**:
- 增加模拟器内存分配
- 关闭其他占用资源的应用
- 启用硬件加速

## 📊 测试数据准备

### 1. 示例干部数据
为了更好地测试功能，建议准备以下测试数据：

```
姓名: 张三, 性别: 男, 出生日期: 1963-05-15, 现职级: 集团中层正职
姓名: 李四, 性别: 女, 出生日期: 1965-08-20, 现职级: 二级公司中层副职
姓名: 王五, 性别: 男, 出生日期: 1964-12-10, 现职级: 三级公司班子成员
```

### 2. Excel测试文件
可以创建包含以上数据的Excel文件来测试导入功能。

## 🎯 测试重点关注

### 1. 用户体验
- **响应速度**: 页面切换是否流畅
- **操作反馈**: 按钮点击是否有及时反馈
- **错误处理**: 错误信息是否友好明确
- **数据加载**: 加载状态是否清晰

### 2. 视觉效果
- **主题一致性**: 所有页面主题风格统一
- **布局适配**: 不同屏幕尺寸下布局正常
- **图标清晰**: 图标在各种主题下清晰可见
- **文字可读**: 文字大小和对比度合适

### 3. 功能完整性
- **数据持久化**: 数据保存和读取正常
- **搜索筛选**: 搜索结果准确
- **预警计算**: 退休预警算法正确
- **主题切换**: 主题切换即时生效

## 📝 测试报告模板

测试完成后，请记录以下信息：

### 基本信息
- 测试时间: ___________
- 雷电模拟器版本: ___________
- Android版本: ___________
- 应用版本: v1.0.0

### 功能测试结果
- [ ] 首页功能正常
- [ ] 干部管理功能正常
- [ ] 退休预警功能正常
- [ ] 主题切换功能正常
- [ ] 设置功能正常

### 主题测试结果
- [ ] 高端主题显示正常
- [ ] 商务主题显示正常
- [ ] 简约主题显示正常
- [ ] 经典主题显示正常
- [ ] 主题持久化正常

### 发现的问题
1. ___________
2. ___________
3. ___________

### 整体评价
- 性能: ⭐⭐⭐⭐⭐
- 界面: ⭐⭐⭐⭐⭐
- 功能: ⭐⭐⭐⭐⭐
- 稳定性: ⭐⭐⭐⭐⭐

---

**准备就绪！现在可以开始在雷电模拟器中测试智慧干部信息管理系统了！** 🚀
