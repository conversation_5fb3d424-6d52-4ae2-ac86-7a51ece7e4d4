import React, { useState } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  Alert,
  TextInput,
  ScrollView
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { CadreInfo } from '../types';
import { CadreStatusDao, CadreStatus } from '../database/cadreStatusDao';

interface CadreStatusModalProps {
  visible: boolean;
  cadre: CadreInfo | null;
  onClose: () => void;
  onStatusUpdated: () => void;
}

export const CadreStatusModal: React.FC<CadreStatusModalProps> = ({
  visible,
  cadre,
  onClose,
  onStatusUpdated
}) => {
  const [selectedStatus, setSelectedStatus] = useState<CadreStatus | null>(null);
  const [reason, setReason] = useState('');
  const [updating, setUpdating] = useState(false);

  if (!cadre) return null;

  // 完整的5个状态选项 - 确保所有选项都能显示
  const statusOptions = [
    {
      status: 'active' as CadreStatus,
      label: '在职',
      color: '#34C759',
      description: '正常在职状态'
    },
    {
      status: 'retired' as CadreStatus,
      label: '已退休',
      color: '#8E8E93',
      description: '已办理退休手续'
    },
    {
      status: 'delayed_retirement' as CadreStatus,
      label: '延迟退休',
      color: '#FF3B30',
      description: '达到退休年龄但延迟退休'
    },
    {
      status: 'second_line_retired' as CadreStatus,
      label: '已退居二线',
      color: '#5AC8FA',
      description: '已正式退居二线'
    },
    {
      status: 'transferred' as CadreStatus,
      label: '已调动',
      color: '#5856D6',
      description: '已调离本单位'
    }
  ];

  console.log('🎯 CadreStatusModal 渲染开始');
  console.log('🎯 状态选项数量:', statusOptions.length);
  console.log('🎯 状态选项详情:', statusOptions.map((o, i) => `${i+1}. ${o.label} (${o.status})`));

  const handleStatusUpdate = async () => {
    if (!selectedStatus || !cadre.id) {
      Alert.alert('提示', '请选择状态');
      return;
    }

    setUpdating(true);
    try {
      await CadreStatusDao.updateCadreStatus(cadre.id, selectedStatus, reason);
      
      Alert.alert(
        '成功',
        `已将 ${cadre.姓名} 的状态更新为 ${CadreStatusDao.getStatusDisplayName(selectedStatus)}`,
        [
          {
            text: '确定',
            onPress: () => {
              onStatusUpdated();
              onClose();
              setSelectedStatus(null);
              setReason('');
            }
          }
        ]
      );
    } catch (error) {
      console.error('更新状态失败:', error);
      Alert.alert('错误', '更新状态失败，请重试');
    } finally {
      setUpdating(false);
    }
  };

  const handleClose = () => {
    setSelectedStatus(null);
    setReason('');
    onClose();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={handleClose}
    >
      <View style={styles.overlay}>
        <View style={styles.container}>
          {/* 头部 */}
          <View style={styles.header}>
            <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
              <Ionicons name="close" size={24} color="#666" />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>更新干部状态</Text>
            <TouchableOpacity
              onPress={handleStatusUpdate}
              style={[styles.saveButton, (!selectedStatus || updating) && styles.saveButtonDisabled]}
              disabled={!selectedStatus || updating}
            >
              <Text style={[styles.saveButtonText, (!selectedStatus || updating) && styles.saveButtonTextDisabled]}>
                {updating ? '更新中...' : '确定'}
              </Text>
            </TouchableOpacity>
          </View>

          {/* 干部信息 */}
          <View style={styles.cadreInfo}>
            <Text style={styles.cadreName}>{cadre.姓名}</Text>
            <Text style={styles.cadreDetails}>
              {cadre.单位} · {cadre.职务}
            </Text>
            <Text style={styles.currentStatus}>
              当前状态: {CadreStatusDao.getStatusDisplayName(cadre.退休状态 as CadreStatus || 'active')}
            </Text>
          </View>

          {/* 滚动内容区域 */}
          <ScrollView style={styles.scrollContent} showsVerticalScrollIndicator={false}>
            {/* 状态选项 */}
            <View style={styles.statusContainer}>
              <Text style={styles.sectionTitle}>选择新状态 (共{statusOptions.length}个选项)</Text>

              {/* 强制渲染所有5个选项 */}
              <TouchableOpacity
                style={[styles.statusOption, selectedStatus === 'active' && styles.statusOptionSelected]}
                onPress={() => {
                  console.log('🎯 选择状态: 在职');
                  setSelectedStatus('active');
                }}
              >
                <View style={styles.statusOptionLeft}>
                  <View style={[styles.statusIndicator, { backgroundColor: '#34C759' }]} />
                  <View style={styles.statusTextContainer}>
                    <Text style={styles.statusLabel}>在职</Text>
                    <Text style={styles.statusDescription}>正常在职状态</Text>
                  </View>
                </View>
                {selectedStatus === 'active' && (
                  <Ionicons name="checkmark-circle" size={24} color="#007AFF" />
                )}
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.statusOption, selectedStatus === 'retired' && styles.statusOptionSelected]}
                onPress={() => {
                  console.log('🎯 选择状态: 已退休');
                  setSelectedStatus('retired');
                }}
              >
                <View style={styles.statusOptionLeft}>
                  <View style={[styles.statusIndicator, { backgroundColor: '#8E8E93' }]} />
                  <View style={styles.statusTextContainer}>
                    <Text style={styles.statusLabel}>已退休</Text>
                    <Text style={styles.statusDescription}>已办理退休手续</Text>
                  </View>
                </View>
                {selectedStatus === 'retired' && (
                  <Ionicons name="checkmark-circle" size={24} color="#007AFF" />
                )}
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.statusOption, selectedStatus === 'delayed_retirement' && styles.statusOptionSelected]}
                onPress={() => {
                  console.log('🎯 选择状态: 延迟退休');
                  setSelectedStatus('delayed_retirement');
                }}
              >
                <View style={styles.statusOptionLeft}>
                  <View style={[styles.statusIndicator, { backgroundColor: '#FF3B30' }]} />
                  <View style={styles.statusTextContainer}>
                    <Text style={styles.statusLabel}>延迟退休</Text>
                    <Text style={styles.statusDescription}>达到退休年龄但延迟退休</Text>
                  </View>
                </View>
                {selectedStatus === 'delayed_retirement' && (
                  <Ionicons name="checkmark-circle" size={24} color="#007AFF" />
                )}
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.statusOption, selectedStatus === 'second_line_retired' && styles.statusOptionSelected]}
                onPress={() => {
                  console.log('🎯 选择状态: 已退居二线');
                  setSelectedStatus('second_line_retired');
                }}
              >
                <View style={styles.statusOptionLeft}>
                  <View style={[styles.statusIndicator, { backgroundColor: '#5AC8FA' }]} />
                  <View style={styles.statusTextContainer}>
                    <Text style={styles.statusLabel}>已退居二线</Text>
                    <Text style={styles.statusDescription}>已正式退居二线</Text>
                  </View>
                </View>
                {selectedStatus === 'second_line_retired' && (
                  <Ionicons name="checkmark-circle" size={24} color="#007AFF" />
                )}
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.statusOption, selectedStatus === 'transferred' && styles.statusOptionSelected]}
                onPress={() => {
                  console.log('🎯 选择状态: 已调动');
                  setSelectedStatus('transferred');
                }}
              >
                <View style={styles.statusOptionLeft}>
                  <View style={[styles.statusIndicator, { backgroundColor: '#5856D6' }]} />
                  <View style={styles.statusTextContainer}>
                    <Text style={styles.statusLabel}>已调动</Text>
                    <Text style={styles.statusDescription}>已调离本单位</Text>
                  </View>
                </View>
                {selectedStatus === 'transferred' && (
                  <Ionicons name="checkmark-circle" size={24} color="#007AFF" />
                )}
              </TouchableOpacity>
            </View>

            {/* 备注 */}
            <View style={styles.reasonContainer}>
              <Text style={styles.sectionTitle}>备注说明（可选）</Text>
              <TextInput
                style={styles.reasonInput}
                placeholder="请输入状态变更原因..."
                value={reason}
                onChangeText={setReason}
                multiline
                numberOfLines={3}
                textAlignVertical="top"
              />
            </View>
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  container: {
    backgroundColor: '#FFF',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
    flex: 1,
  },
  scrollContent: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 0.5,
    borderBottomColor: '#E5E5EA',
  },
  closeButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000',
  },
  saveButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#007AFF',
    borderRadius: 8,
  },
  saveButtonDisabled: {
    backgroundColor: '#E5E5EA',
  },
  saveButtonText: {
    color: '#FFF',
    fontSize: 16,
    fontWeight: '600',
  },
  saveButtonTextDisabled: {
    color: '#8E8E93',
  },
  cadreInfo: {
    padding: 20,
    borderBottomWidth: 0.5,
    borderBottomColor: '#E5E5EA',
  },
  cadreName: {
    fontSize: 20,
    fontWeight: '700',
    color: '#000',
    marginBottom: 4,
  },
  cadreDetails: {
    fontSize: 16,
    color: '#666',
    marginBottom: 8,
  },
  currentStatus: {
    fontSize: 14,
    color: '#007AFF',
    fontWeight: '500',
  },
  statusContainer: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000',
    marginBottom: 16,
  },
  statusOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    marginBottom: 8,
    backgroundColor: '#F8F9FA',
  },
  statusOptionSelected: {
    backgroundColor: '#E3F2FD',
    borderWidth: 1,
    borderColor: '#007AFF',
  },
  statusOptionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  statusTextContainer: {
    flex: 1,
  },
  statusLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#000',
    marginBottom: 2,
  },
  statusDescription: {
    fontSize: 14,
    color: '#666',
  },
  reasonContainer: {
    padding: 20,
    paddingTop: 0,
  },
  reasonInput: {
    borderWidth: 1,
    borderColor: '#E5E5EA',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#000',
    minHeight: 80,
  },
  // 智能提醒弹窗样式
  reminderContent: {
    padding: 20,
  },
  reminderTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000',
    marginBottom: 8,
  },
  reminderSubtitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 20,
  },
  actionContainer: {
    marginBottom: 20,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#F8F9FA',
    borderRadius: 8,
    marginHorizontal: 4,
    alignItems: 'center',
  },
  actionButtonSelected: {
    backgroundColor: '#007AFF',
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
  },
  actionButtonTextSelected: {
    color: '#FFF',
  },
  cadreListContainer: {
    maxHeight: 300,
  },
  cadreItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 8,
    backgroundColor: '#F8F9FA',
  },
  cadreItemSelected: {
    backgroundColor: '#E3F2FD',
    borderWidth: 1,
    borderColor: '#007AFF',
  },
  cadreItemLeft: {
    flex: 1,
  },
  cadreItemName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#000',
    marginBottom: 2,
  },
  cadreItemDetails: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  cadreItemWarning: {
    fontSize: 12,
    color: '#FF3B30',
    fontWeight: '500',
  },
});

// 智能提醒弹窗组件
interface SmartReminderModalProps {
  visible: boolean;
  cadres: Array<{ cadre: CadreInfo; daysRemaining: number; warningType: string }>;
  onClose: () => void;
  onBatchUpdate: (cadreIds: number[], status: CadreStatus) => void;
}

export const SmartReminderModal: React.FC<SmartReminderModalProps> = ({
  visible,
  cadres,
  onClose,
  onBatchUpdate
}) => {
  const [selectedCadres, setSelectedCadres] = useState<Set<number>>(new Set());
  const [selectedAction, setSelectedAction] = useState<CadreStatus | null>(null);

  const handleCadreToggle = (cadreId: number) => {
    const newSelected = new Set(selectedCadres);
    if (newSelected.has(cadreId)) {
      newSelected.delete(cadreId);
    } else {
      newSelected.add(cadreId);
    }
    setSelectedCadres(newSelected);
  };

  const handleBatchUpdate = () => {
    if (selectedCadres.size === 0 || !selectedAction) {
      Alert.alert('提示', '请选择人员和操作');
      return;
    }

    Alert.alert(
      '确认操作',
      `确定要将选中的 ${selectedCadres.size} 名干部标记为 ${CadreStatusDao.getStatusDisplayName(selectedAction)} 吗？`,
      [
        { text: '取消', style: 'cancel' },
        {
          text: '确定',
          onPress: () => {
            onBatchUpdate(Array.from(selectedCadres), selectedAction);
            setSelectedCadres(new Set());
            setSelectedAction(null);
            onClose();
          }
        }
      ]
    );
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={[styles.container, { maxHeight: '90%' }]}>
          <View style={styles.header}>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={24} color="#666" />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>退休提醒</Text>
            <TouchableOpacity
              onPress={handleBatchUpdate}
              style={[styles.saveButton, (!selectedAction || selectedCadres.size === 0) && styles.saveButtonDisabled]}
              disabled={!selectedAction || selectedCadres.size === 0}
            >
              <Text style={[styles.saveButtonText, (!selectedAction || selectedCadres.size === 0) && styles.saveButtonTextDisabled]}>
                批量操作
              </Text>
            </TouchableOpacity>
          </View>

          <View style={styles.reminderContent}>
            <Text style={styles.reminderTitle}>
              发现 {cadres.length} 名干部即将到期（1-3天内）
            </Text>
            <Text style={styles.reminderSubtitle}>
              请确认这些人员的实际状态，以确保数据准确性
            </Text>

            {/* 操作选项 */}
            <View style={styles.actionContainer}>
              <Text style={styles.sectionTitle}>选择操作</Text>
              <View style={styles.actionButtons}>
                <TouchableOpacity
                  style={[styles.actionButton, selectedAction === 'delayed_retirement' && styles.actionButtonSelected]}
                  onPress={() => setSelectedAction('delayed_retirement')}
                >
                  <Text style={[styles.actionButtonText, selectedAction === 'delayed_retirement' && styles.actionButtonTextSelected]}>
                    延迟退休
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.actionButton, selectedAction === 'retired' && styles.actionButtonSelected]}
                  onPress={() => setSelectedAction('retired')}
                >
                  <Text style={[styles.actionButtonText, selectedAction === 'retired' && styles.actionButtonTextSelected]}>
                    已退休
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.actionButton, selectedAction === 'transferred' && styles.actionButtonSelected]}
                  onPress={() => setSelectedAction('transferred')}
                >
                  <Text style={[styles.actionButtonText, selectedAction === 'transferred' && styles.actionButtonTextSelected]}>
                    调动干部
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* 人员列表 */}
            <View style={styles.cadreListContainer}>
              <Text style={styles.sectionTitle}>
                选择人员 ({selectedCadres.size}/{cadres.length})
              </Text>
              {cadres.map((item, index) => (
                <TouchableOpacity
                  key={item.cadre.id || index}
                  style={[styles.cadreItem, selectedCadres.has(item.cadre.id!) && styles.cadreItemSelected]}
                  onPress={() => handleCadreToggle(item.cadre.id!)}
                >
                  <View style={styles.cadreItemLeft}>
                    <Text style={styles.cadreItemName}>{item.cadre.姓名}</Text>
                    <Text style={styles.cadreItemDetails}>
                      {item.cadre.单位} · {item.cadre.职务}
                    </Text>
                    <Text style={styles.cadreItemWarning}>
                      {item.warningType} · 剩余 {item.daysRemaining} 天
                    </Text>
                  </View>
                  {selectedCadres.has(item.cadre.id!) && (
                    <Ionicons name="checkmark-circle" size={24} color="#007AFF" />
                  )}
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );
};
