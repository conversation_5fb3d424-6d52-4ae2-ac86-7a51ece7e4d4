# 手动启动EXPO开发服务器指南

## 🚀 快速启动步骤

### 1. 打开新的命令提示符窗口
- 按 `Win + R`
- 输入 `cmd` 并按回车

### 2. 导航到项目目录
```cmd
cd /d F:\APPapk\CadreManagementSystem
```

### 3. 启动EXPO开发服务器
```cmd
npx expo start
```

## 📱 连接雷电模拟器

### 方法一：扫描二维码
1. EXPO服务器启动后会显示一个二维码
2. 在雷电模拟器中安装 Expo Go 应用
3. 使用 Expo Go 扫描二维码

### 方法二：使用局域网连接
1. 在EXPO服务器界面按 `a` 键选择Android
2. 或者在浏览器中打开显示的URL
3. 选择"在设备上打开"

### 方法三：直接运行Android
```cmd
npx expo run:android
```

## 🔧 如果遇到问题

### 问题1：Node.js命令不识别
**解决方案**：
1. 重新启动命令提示符
2. 或者使用完整路径：
```cmd
"C:\Program Files\nodejs\npx.exe" expo start
```

### 问题2：EXPO命令不存在
**解决方案**：
```cmd
npm install -g @expo/cli
```

### 问题3：端口被占用
**解决方案**：
```cmd
npx expo start --port 8081
```

### 问题4：网络连接问题
**解决方案**：
```cmd
npx expo start --tunnel
```

## 📋 预期结果

启动成功后，您应该看到类似以下的输出：

```
Starting Metro Bundler
› Metro waiting on exp://192.168.1.100:8081
› Scan the QR code above with Expo Go (Android) or the Camera app (iOS)

› Press a │ open Android
› Press w │ open web

› Press r │ reload app
› Press m │ toggle menu
› Press d │ show developer menu
› Press shift+d │ toggle development mode

› Press ? │ show all commands
```

## 🎯 下一步操作

1. **启动EXPO服务器** (按照上述步骤)
2. **启动雷电模拟器**：
   ```cmd
   D:\LDPlayer\dnplayer.exe
   ```
3. **在模拟器中安装Expo Go**
4. **连接到开发服务器**
5. **开始测试应用功能**

## 📞 如果仍有问题

如果按照上述步骤仍然无法启动，请：
1. 确认Node.js已正确安装
2. 重启计算机
3. 尝试使用管理员权限运行命令提示符
4. 检查防火墙设置

---

**准备就绪！** 现在您可以手动启动EXPO服务器并在雷电模拟器中测试应用了！
