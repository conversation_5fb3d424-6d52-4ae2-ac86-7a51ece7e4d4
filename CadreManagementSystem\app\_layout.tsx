import { DarkTheme, DefaultTheme, ThemeProvider as NavigationThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useEffect, useState } from 'react';
import { Alert } from 'react-native';
import 'react-native-reanimated';

import { useColorScheme } from '@/hooks/useColorScheme';
import { initDatabase } from '../src/database/database';
import { TestDataInitializer } from '../src/database/testDataInitializer';
import { ThemeProvider } from '../src/contexts/ThemeContext';

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });
  const [dbReady, setDbReady] = useState(false);

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      // 初始化数据库
      await initDatabase();
      console.log('数据库初始化成功');

      // 初始化测试数据（如果数据库为空）
      try {
        await TestDataInitializer.initializeTestData();
        console.log('测试数据初始化成功');
      } catch (testDataError) {
        console.warn('测试数据初始化失败:', testDataError);
        // 测试数据初始化失败不影响应用启动
      }

      setDbReady(true);
    } catch (error) {
      console.error('数据库初始化失败:', error);
      Alert.alert('初始化失败', '数据库启动时发生错误，请重新启动应用');
    }
  };

  if (!loaded || !dbReady) {
    return null;
  }

  return (
    <ThemeProvider>
      <NavigationThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
        <Stack>
          <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
          <Stack.Screen
            name="cadre-detail"
            options={{
              headerShown: true,
              headerTitle: '干部详情',
              presentation: 'modal'
            }}
          />
          <Stack.Screen
            name="position-management"
            options={{
              headerShown: true,
              headerTitle: '职级管理',
              presentation: 'modal'
            }}
          />
          <Stack.Screen
            name="import-excel"
            options={{
              headerShown: true,
              headerTitle: 'Excel导入',
              presentation: 'modal'
            }}
          />
          <Stack.Screen
            name="data-management"
            options={{
              headerShown: true,
              headerTitle: '数据管理',
              presentation: 'modal'
            }}
          />
          <Stack.Screen
            name="retirement-settings"
            options={{
              headerShown: true,
              headerTitle: '退休规则设置',
              presentation: 'modal'
            }}
          />
          <Stack.Screen name="+not-found" />
        </Stack>
        <StatusBar style="auto" />
      </NavigationThemeProvider>
    </ThemeProvider>
  );
}
