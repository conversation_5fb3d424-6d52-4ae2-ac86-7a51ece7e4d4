/**
 * 测试新的个人信息卡片格式
 */

console.log('🎯 测试新的个人信息卡片格式...\n');

// 模拟精确年龄计算
function calculatePreciseAge(birthDate) {
  const today = new Date();
  const ageInMilliseconds = today.getTime() - birthDate.getTime();
  const ageInYears = ageInMilliseconds / (365.25 * 24 * 60 * 60 * 1000);
  return Math.round(ageInYears * 10) / 10; // 保留一位小数
}

// 模拟计算距离指定年龄的剩余时间
function calculateTimeToAge(birthDate, targetAge) {
  const targetDate = new Date(birthDate);
  targetDate.setFullYear(birthDate.getFullYear() + targetAge);
  
  const today = new Date();
  const diffTime = targetDate.getTime() - today.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  // 计算年月
  const years = Math.floor(diffDays / 365);
  const months = Math.floor((diffDays % 365) / 30);
  
  return {
    years: Math.max(0, years),
    months: Math.max(0, months),
    days: Math.max(0, diffDays)
  };
}

// 解析Excel日期序列号
function parseBirthDate(birthDateStr) {
  if (typeof birthDateStr === 'number') {
    try {
      const excelDate = new Date((birthDateStr - 25569) * 86400 * 1000);
      if (!isNaN(excelDate.getTime()) && excelDate.getFullYear() > 1900 && excelDate.getFullYear() < 2100) {
        return excelDate;
      }
    } catch (e) {
      // 忽略错误
    }
  }
  return null;
}

// 测试数据
const testCases = [
  {
    name: '韦树林（退休预警）',
    cadre: {
      姓名: '韦树林',
      单位: '晋圣公司',
      职务: '纪委副书记兼信访案管室主任',
      出生日期: 24368 // 1966年，约58岁
    },
    warningType: 'retirement_warning',
    urgencyLevel: 'urgent',
    delayInfo: {
      delayYears: 0,
      delayMonths: 2,
      actualRetirementAge: 60.17
    },
    daysUntilRetirement: 229
  },
  {
    name: '测试中层正职（退居二线预警）',
    cadre: {
      姓名: '测试中层',
      单位: '测试公司',
      职务: '中层正职',
      出生日期: 25567 // 1970年，约55岁
    },
    warningType: 'second_line_warning',
    urgencyLevel: 'normal',
    delayInfo: {
      delayYears: 0,
      delayMonths: 6,
      actualRetirementAge: 60.5
    },
    daysUntilRetirement: 1825
  },
  {
    name: '王四顿（已退休）',
    cadre: {
      姓名: '王四顿',
      单位: '晋圣凤红煤业',
      职务: '调研员',
      出生日期: 23853 // 1965年，约60岁
    },
    warningType: 'retired',
    urgencyLevel: 'info',
    delayInfo: {
      delayYears: 0,
      delayMonths: 0,
      actualRetirementAge: 60
    },
    daysUntilRetirement: -30
  }
];

// 执行测试
testCases.forEach((testCase, index) => {
  console.log(`${index + 1}. ${testCase.name}:`);
  
  const birthDate = parseBirthDate(testCase.cadre.出生日期);
  if (birthDate) {
    const preciseAge = calculatePreciseAge(birthDate);
    const legalRetirementTime = calculateTimeToAge(birthDate, 60);
    
    console.log(`   📋 卡片头部显示:`);
    console.log(`      ${testCase.cadre.姓名} · ${testCase.cadre.单位} · ${testCase.cadre.职务} [${testCase.urgencyLevel === 'urgent' ? '紧急' : testCase.urgencyLevel === 'normal' ? '一般' : '信息'}]`);
    
    console.log(`   📋 卡片内容显示:`);
    
    if (testCase.warningType === 'retirement_warning') {
      console.log(`      退休预警（${preciseAge}岁）`);
      console.log(`      距法定退休年龄剩余${legalRetirementTime.years}年${legalRetirementTime.months}个月（剩余${legalRetirementTime.days}天）`);
      console.log(`      预计延迟退休${testCase.delayInfo.delayYears}年${testCase.delayInfo.delayMonths}个月（剩余${testCase.daysUntilRetirement}天）`);
    } else if (testCase.warningType === 'second_line_warning') {
      // 简化的退居二线计算
      const secondLineTime = calculateTimeToAge(birthDate, 58);
      console.log(`      退休预警（${preciseAge}岁）`);
      console.log(`      距退居二线剩余${secondLineTime.years}年${secondLineTime.months}个月（剩余${secondLineTime.days}天）`);
      console.log(`      预计退休年龄: ${testCase.delayInfo.actualRetirementAge.toFixed(1)}岁（剩余${testCase.daysUntilRetirement}天）`);
    } else {
      console.log(`      已退休（${preciseAge}岁）`);
      console.log(`      实际退休年龄: ${testCase.delayInfo.actualRetirementAge.toFixed(1)}岁`);
      console.log(`      已超过退休年龄${Math.abs(testCase.daysUntilRetirement)}天`);
    }
  } else {
    console.log(`   ❌ 无法解析出生日期`);
  }
  
  console.log('');
});

console.log('✅ 测试完成！');
console.log('\n📋 新格式特点：');
console.log('1. 头部：姓名 · 单位 · 职务 + 预警级别标签');
console.log('2. 第一行：退休预警（X.X岁）- 精确年龄，保留一位小数');
console.log('3. 第二行：距法定退休年龄剩余时间');
console.log('4. 第三行：预计延迟退休信息');
console.log('\n🎨 样式层次：');
console.log('- 主要预警文本：14px，深色，加粗');
console.log('- 时间信息：12px，灰色');
console.log('- 延迟信息：12px，蓝色，加粗');
