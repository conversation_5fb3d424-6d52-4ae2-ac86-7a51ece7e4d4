// 主题系统定义
export interface Theme {
  name: string;
  displayName: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    surface: string;
    card: string;
    text: string;
    textSecondary: string;
    border: string;
    success: string;
    warning: string;
    error: string;
    info: string;
  };
  gradients: {
    primary: string[];
    secondary: string[];
    header: string[];
  };
  shadows: {
    small: {
      shadowColor: string;
      shadowOffset: { width: number; height: number };
      shadowOpacity: number;
      shadowRadius: number;
      elevation: number;
    };
    medium: {
      shadowColor: string;
      shadowOffset: { width: number; height: number };
      shadowOpacity: number;
      shadowRadius: number;
      elevation: number;
    };
    large: {
      shadowColor: string;
      shadowOffset: { width: number; height: number };
      shadowOpacity: number;
      shadowRadius: number;
      elevation: number;
    };
  };
  typography: {
    fontFamily: string;
    sizes: {
      xs: number;
      sm: number;
      md: number;
      lg: number;
      xl: number;
      xxl: number;
    };
    weights: {
      light: string;
      normal: string;
      medium: string;
      semibold: string;
      bold: string;
    };
  };
  spacing: {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
    xxl: number;
  };
  borderRadius: {
    sm: number;
    md: number;
    lg: number;
    xl: number;
    full: number;
  };
}

// 高端主题 - 深色奢华风格
export const premiumTheme: Theme = {
  name: 'premium',
  displayName: '高端主题',
  colors: {
    primary: '#1A1A2E',
    secondary: '#16213E',
    accent: '#E94560',
    background: '#0F0F23',
    surface: '#1A1A2E',
    card: '#16213E',
    text: '#FFFFFF',
    textSecondary: '#B8B8D1',
    border: '#2A2A4A',
    success: '#00D4AA',
    warning: '#FFB800',
    error: '#FF6B6B',
    info: '#4ECDC4',
  },
  gradients: {
    primary: ['#1A1A2E', '#16213E'],
    secondary: ['#E94560', '#FF6B6B'],
    header: ['#1A1A2E', '#16213E', '#0F0F23'],
  },
  shadows: {
    small: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.3,
      shadowRadius: 4,
      elevation: 5,
    },
    medium: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.4,
      shadowRadius: 8,
      elevation: 8,
    },
    large: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.5,
      shadowRadius: 16,
      elevation: 12,
    },
  },
  typography: {
    fontFamily: 'System',
    sizes: { xs: 12, sm: 14, md: 16, lg: 18, xl: 24, xxl: 32 },
    weights: { light: '300', normal: '400', medium: '500', semibold: '600', bold: '700' },
  },
  spacing: { xs: 4, sm: 8, md: 16, lg: 24, xl: 32, xxl: 48 },
  borderRadius: { sm: 8, md: 12, lg: 16, xl: 24, full: 9999 },
};

// 商务主题 - 专业蓝色风格
export const businessTheme: Theme = {
  name: 'business',
  displayName: '商务主题',
  colors: {
    primary: '#1E3A8A',
    secondary: '#3B82F6',
    accent: '#F59E0B',
    background: '#F8FAFC',
    surface: '#FFFFFF',
    card: '#FFFFFF',
    text: '#1F2937',
    textSecondary: '#6B7280',
    border: '#E5E7EB',
    success: '#10B981',
    warning: '#F59E0B',
    error: '#EF4444',
    info: '#3B82F6',
  },
  gradients: {
    primary: ['#1E3A8A', '#3B82F6'],
    secondary: ['#F59E0B', '#FBBF24'],
    header: ['#1E3A8A', '#2563EB', '#3B82F6'],
  },
  shadows: {
    small: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 3,
      elevation: 2,
    },
    medium: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.15,
      shadowRadius: 6,
      elevation: 4,
    },
    large: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.2,
      shadowRadius: 12,
      elevation: 8,
    },
  },
  typography: {
    fontFamily: 'System',
    sizes: { xs: 12, sm: 14, md: 16, lg: 18, xl: 24, xxl: 32 },
    weights: { light: '300', normal: '400', medium: '500', semibold: '600', bold: '700' },
  },
  spacing: { xs: 4, sm: 8, md: 16, lg: 24, xl: 32, xxl: 48 },
  borderRadius: { sm: 6, md: 8, lg: 12, xl: 16, full: 9999 },
};

// 简约主题 - 清新简洁风格
export const minimalistTheme: Theme = {
  name: 'minimalist',
  displayName: '简约主题',
  colors: {
    primary: '#2DD4BF',
    secondary: '#06B6D4',
    accent: '#F472B6',
    background: '#FAFAFA',
    surface: '#FFFFFF',
    card: '#FFFFFF',
    text: '#374151',
    textSecondary: '#9CA3AF',
    border: '#F3F4F6',
    success: '#34D399',
    warning: '#FBBF24',
    error: '#F87171',
    info: '#60A5FA',
  },
  gradients: {
    primary: ['#2DD4BF', '#06B6D4'],
    secondary: ['#F472B6', '#EC4899'],
    header: ['#2DD4BF', '#14B8A6', '#0891B2'],
  },
  shadows: {
    small: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 1,
    },
    medium: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.08,
      shadowRadius: 4,
      elevation: 3,
    },
    large: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.12,
      shadowRadius: 8,
      elevation: 6,
    },
  },
  typography: {
    fontFamily: 'System',
    sizes: { xs: 12, sm: 14, md: 16, lg: 18, xl: 24, xxl: 32 },
    weights: { light: '300', normal: '400', medium: '500', semibold: '600', bold: '700' },
  },
  spacing: { xs: 4, sm: 8, md: 16, lg: 24, xl: 32, xxl: 48 },
  borderRadius: { sm: 4, md: 8, lg: 12, xl: 20, full: 9999 },
};

// 经典主题 - 传统稳重风格
export const classicTheme: Theme = {
  name: 'classic',
  displayName: '经典主题',
  colors: {
    primary: '#7C3AED',
    secondary: '#A855F7',
    accent: '#F97316',
    background: '#F9FAFB',
    surface: '#FFFFFF',
    card: '#FFFFFF',
    text: '#111827',
    textSecondary: '#4B5563',
    border: '#D1D5DB',
    success: '#059669',
    warning: '#D97706',
    error: '#DC2626',
    info: '#2563EB',
  },
  gradients: {
    primary: ['#7C3AED', '#A855F7'],
    secondary: ['#F97316', '#FB923C'],
    header: ['#7C3AED', '#8B5CF6', '#A855F7'],
  },
  shadows: {
    small: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 2,
    },
    medium: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.12,
      shadowRadius: 4,
      elevation: 4,
    },
    large: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.15,
      shadowRadius: 8,
      elevation: 8,
    },
  },
  typography: {
    fontFamily: 'System',
    sizes: { xs: 12, sm: 14, md: 16, lg: 18, xl: 24, xxl: 32 },
    weights: { light: '300', normal: '400', medium: '500', semibold: '600', bold: '700' },
  },
  spacing: { xs: 4, sm: 8, md: 16, lg: 24, xl: 32, xxl: 48 },
  borderRadius: { sm: 6, md: 10, lg: 14, xl: 18, full: 9999 },
};

// 主题集合
export const themes = {
  premium: premiumTheme,
  business: businessTheme,
  minimalist: minimalistTheme,
  classic: classicTheme,
};

// 默认主题
export const defaultTheme = businessTheme;
