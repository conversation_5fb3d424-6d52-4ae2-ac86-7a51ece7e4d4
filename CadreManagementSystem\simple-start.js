/**
 * 简化的EXPO启动脚本
 */

const { exec } = require('child_process');

console.log('🚀 启动EXPO开发服务器...');
console.log('');
console.log('📡 您的IP地址: *************');
console.log('🔗 连接地址: exp://*************:19000');
console.log('');
console.log('📱 请在雷电模拟器中：');
console.log('1. 打开 Expo Go 应用');
console.log('2. 选择 "Enter URL manually"');
console.log('3. 输入: exp://*************:19000');
console.log('4. 点击连接');
console.log('');

// 启动EXPO
const command = 'npx expo start --lan --host *************';
console.log('正在执行命令:', command);
console.log('');

const child = exec(command, (error, stdout, stderr) => {
  if (error) {
    console.log('❌ 启动错误:', error.message);
    return;
  }
  if (stderr) {
    console.log('⚠️ 警告:', stderr);
  }
  console.log('📤 输出:', stdout);
});

child.stdout.on('data', (data) => {
  console.log('📤 EXPO:', data.toString());
});

child.stderr.on('data', (data) => {
  console.log('🚨 错误:', data.toString());
});

console.log('⏳ 等待EXPO启动...');
console.log('💡 如果长时间无响应，请按 Ctrl+C 停止并重试');
