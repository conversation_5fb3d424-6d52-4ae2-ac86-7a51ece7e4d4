/**
 * 测试数据库中的实际数据
 * 用于验证Excel导入是否成功
 */

const XLSX = require('xlsx');
const path = require('path');

console.log('🔍 开始检查Excel文件和数据库数据...\n');

// 1. 首先检查Excel文件内容
const excelPath = path.join(__dirname, '0606.xlsx');
console.log('📄 Excel文件路径:', excelPath);

try {
  const workbook = XLSX.readFile(excelPath);
  const sheetName = workbook.SheetNames[0];
  const worksheet = workbook.Sheets[sheetName];
  const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
  
  console.log(`📊 Excel文件包含 ${data.length} 行数据`);
  console.log('📋 表头:', data[0]);
  
  // 查找王四顿和许国泰
  const dataRows = data.slice(1);
  let foundWang = false;
  let foundXu = false;
  
  console.log('\n🔍 查找关键人员：');
  dataRows.forEach((row, index) => {
    const name = row[2] || row[1] || row[0]; // 尝试不同列
    if (name && typeof name === 'string') {
      if (name.includes('王四顿')) {
        foundWang = true;
        console.log(`✅ 找到王四顿 - 行${index + 2}:`, row.slice(0, 8));
      }
      if (name.includes('许国泰')) {
        foundXu = true;
        console.log(`✅ 找到许国泰 - 行${index + 2}:`, row.slice(0, 8));
      }
    }
  });
  
  if (!foundWang) console.log('❌ 未找到王四顿');
  if (!foundXu) console.log('❌ 未找到许国泰');
  
  // 显示前5行数据
  console.log('\n📋 前5行数据：');
  dataRows.slice(0, 5).forEach((row, index) => {
    console.log(`${index + 1}:`, row.slice(0, 8));
  });
  
} catch (error) {
  console.error('❌ 读取Excel文件失败:', error.message);
}

// 2. 模拟退休预警计算
console.log('\n🔍 模拟退休预警计算：');

function calculateRetirementWarning(name, gender, birthDate) {
  if (!birthDate) {
    return `❌ ${name}: 出生日期缺失`;
  }

  let date;
  try {
    // 尝试解析日期
    if (typeof birthDate === 'number') {
      // Excel日期序列号
      date = new Date((birthDate - 25569) * 86400 * 1000);
    } else {
      date = new Date(birthDate);
    }
    
    if (isNaN(date.getTime())) {
      return `❌ ${name}: 出生日期格式错误 (${birthDate})`;
    }
  } catch (e) {
    return `❌ ${name}: 出生日期解析失败 (${birthDate})`;
  }

  const today = new Date();
  const age = today.getFullYear() - date.getFullYear();
  const monthDiff = today.getMonth() - date.getMonth();
  
  let currentAge = age;
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < date.getDate())) {
    currentAge--;
  }

  // 简化的退休年龄计算
  let retirementAge = 60; // 默认男性
  if (gender === '女') {
    retirementAge = 55; // 简化为女干部55岁
  }

  const yearsToRetirement = retirementAge - currentAge;

  if (yearsToRetirement <= 0) {
    return `🚨 ${name}: 已达到退休年龄 (当前${currentAge}岁，应${retirementAge}岁退休)`;
  } else if (yearsToRetirement <= 2) {
    return `⚠️ ${name}: 即将退休 (当前${currentAge}岁，${yearsToRetirement}年后退休)`;
  } else if (yearsToRetirement <= 5) {
    return `📋 ${name}: 退居二线预警 (当前${currentAge}岁，${yearsToRetirement}年后退休)`;
  } else {
    return `✅ ${name}: 正常 (当前${currentAge}岁，${yearsToRetirement}年后退休)`;
  }
}

// 测试关键人员
const testCases = [
  ['王四顿', '男', '1964-03-15'],
  ['王四顿', '男', '1964/3/15'],
  ['王四顿', '男', 23485], // Excel日期序列号示例
  ['许国泰', '男', '1969-08-20'],
  ['许国泰', '男', '1969/8/20']
];

testCases.forEach(([name, gender, birthDate]) => {
  const result = calculateRetirementWarning(name, gender, birthDate);
  console.log(result);
});

console.log('\n✅ 检查完成！');
console.log('\n💡 如果Excel中有数据但应用中没有显示预警，可能的原因：');
console.log('1. 出生日期格式不被识别（Excel日期序列号、特殊格式等）');
console.log('2. 姓名字段位置不正确');
console.log('3. 数据库导入过程中字段映射错误');
console.log('4. 退休预警计算逻辑有问题');
