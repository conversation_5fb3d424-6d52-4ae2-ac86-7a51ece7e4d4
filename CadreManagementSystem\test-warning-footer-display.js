/**
 * 测试退休预警页脚显示逻辑
 */

console.log('🧪 测试退休预警页脚显示逻辑...\n');

// 模拟解析出生日期函数
function parseBirthDate(birthDateStr) {
  if (!birthDateStr && birthDateStr !== 0) {
    return null;
  }
  
  // 处理Excel日期序列号
  if (typeof birthDateStr === 'number') {
    try {
      const excelDate = new Date((birthDateStr - 25569) * 86400 * 1000);
      if (!isNaN(excelDate.getTime()) && excelDate.getFullYear() > 1900 && excelDate.getFullYear() < 2100) {
        return excelDate;
      }
    } catch (e) {
      // 忽略错误
    }
  }
  
  return null;
}

// 计算当前年龄
function calculateAge(birthDate) {
  const today = new Date();
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  
  return age;
}

// 计算距离退居二线的时间
function calculateSecondLineTime(birthDate, currentAge) {
  const secondLineAge = 58;
  const yearsToSecondLine = secondLineAge - currentAge;
  const monthsToSecondLine = yearsToSecondLine * 12;
  
  const secondLineDate = new Date(birthDate);
  secondLineDate.setFullYear(birthDate.getFullYear() + secondLineAge);
  
  const today = new Date();
  const diffTime = secondLineDate.getTime() - today.getTime();
  const daysToSecondLine = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  return {
    years: Math.max(0, yearsToSecondLine),
    months: Math.max(0, monthsToSecondLine % 12),
    days: Math.max(0, daysToSecondLine)
  };
}

// 渲染预警页脚信息
function renderWarningFooter(warning) {
  const birthDate = parseBirthDate(warning.cadre.出生日期);
  
  if (warning.warningType === 'second_line_warning') {
    // 退居二线预警显示
    if (birthDate) {
      const currentAge = calculateAge(birthDate);
      const secondLineTime = calculateSecondLineTime(birthDate, currentAge);
      
      return {
        line1: `距退居二线剩余${secondLineTime.years}年${secondLineTime.months}个月（剩余${secondLineTime.days}天）`,
        line2: `预计退休年龄: ${warning.delayInfo?.actualRetirementAge.toFixed(1) || '60.0'}岁（剩余${warning.daysUntilRetirement}天）`
      };
    } else {
      return {
        line1: '距退居二线剩余时间计算中...',
        line2: `预计退休年龄: ${warning.delayInfo?.actualRetirementAge.toFixed(1) || '60.0'}岁`
      };
    }
  } else if (warning.warningType === 'retirement_warning') {
    // 退休预警显示
    const legalRetirementDays = warning.daysUntilRetirement + (warning.delayInfo?.delayYears || 0) * 365 + (warning.delayInfo?.delayMonths || 0) * 30;
    
    return {
      line1: `距法定退休年龄剩余${Math.max(0, legalRetirementDays)}天`,
      line2: `预计延迟${warning.delayInfo?.delayYears || 0}年${warning.delayInfo?.delayMonths || 0}个月（剩余${warning.daysUntilRetirement}天）`
    };
  } else {
    // 已退休或其他情况
    return {
      line1: warning.delayInfo ? 
        `预计退休年龄: ${warning.delayInfo.actualRetirementAge.toFixed(1)}岁` :
        '退休年龄: 60岁',
      line2: warning.daysUntilRetirement > 0
        ? `还有${warning.daysUntilRetirement}天`
        : '已退休'
    };
  }
}

// 测试数据
const testCases = [
  {
    name: '退居二线预警测试',
    warning: {
      cadre: {
        姓名: '测试中层正职',
        出生日期: 25567, // 1970年左右，约55岁
        职务: '中层正职'
      },
      warningType: 'second_line_warning',
      urgencyLevel: 'normal',
      description: '退居二线预警（55岁，中层正职）',
      daysUntilRetirement: 1825, // 5年后退休
      delayInfo: {
        delayYears: 0,
        delayMonths: 6,
        originalRetirementAge: 60,
        actualRetirementAge: 60.5
      }
    }
  },
  {
    name: '退休预警测试',
    warning: {
      cadre: {
        姓名: '许国泰',
        出生日期: 24368, // 1966年，约58岁
        职务: '退居二线'
      },
      warningType: 'retirement_warning',
      urgencyLevel: 'urgent',
      description: '退休预警（58岁），预计延迟0年2个月，还剩444天退休',
      daysUntilRetirement: 444,
      delayInfo: {
        delayYears: 0,
        delayMonths: 2,
        originalRetirementAge: 60,
        actualRetirementAge: 60.17
      }
    }
  },
  {
    name: '已退休测试',
    warning: {
      cadre: {
        姓名: '王四顿',
        出生日期: 23853, // 1965年，约60岁
        职务: '调研员'
      },
      warningType: 'retired',
      urgencyLevel: 'info',
      description: '已达到退休年龄（60岁）',
      daysUntilRetirement: -30,
      delayInfo: {
        delayYears: 0,
        delayMonths: 0,
        originalRetirementAge: 60,
        actualRetirementAge: 60
      }
    }
  }
];

// 执行测试
testCases.forEach((testCase, index) => {
  console.log(`${index + 1}. ${testCase.name}:`);
  console.log(`   姓名: ${testCase.warning.cadre.姓名}`);
  console.log(`   预警类型: ${testCase.warning.warningType}`);
  
  const birthDate = parseBirthDate(testCase.warning.cadre.出生日期);
  if (birthDate) {
    const age = calculateAge(birthDate);
    console.log(`   出生日期: ${birthDate.toLocaleDateString()}`);
    console.log(`   当前年龄: ${age}岁`);
  }
  
  const footerInfo = renderWarningFooter(testCase.warning);
  console.log(`   📋 显示内容:`);
  console.log(`      第一行: ${footerInfo.line1}`);
  console.log(`      第二行: ${footerInfo.line2}`);
  console.log('');
});

console.log('✅ 测试完成！');
console.log('\n📋 预期显示效果：');
console.log('1. 退居二线预警: 显示距退居二线剩余时间 + 预计退休年龄');
console.log('2. 退休预警: 显示距法定退休剩余时间 + 延迟退休信息');
console.log('3. 已退休: 显示退休年龄 + 已退休状态');
