import React, { useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { RetirementWarning } from '../../src/components/RetirementWarning';
import { CadreInfo } from '../../src/types';

type FilterType = 'all' | 'near_retirement' | 'near_second_line' | 'already_second_line' | 'retired';

export default function RetirementWarningScreen() {
  const { filter } = useLocalSearchParams<{ filter?: string }>();

  useEffect(() => {
    console.log('🚀 退休预警页面已加载，筛选类型:', filter);
  }, [filter]);

  const handleCadrePress = (cadre: CadreInfo) => {
    console.log('🔍 点击干部:', cadre.name);
    router.push(`/cadre-detail?id=${cadre.id}&mode=view`);
  };

  // 确保筛选类型有效
  const initialFilter: FilterType = (filter && ['near_retirement', 'near_second_line', 'already_second_line', 'retired'].includes(filter))
    ? filter as FilterType
    : 'all';

  return (
    <View style={styles.container}>
      <RetirementWarning
        onCadrePress={handleCadrePress}
        initialFilter={initialFilter}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7'
  }
});
