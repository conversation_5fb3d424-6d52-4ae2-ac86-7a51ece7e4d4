import React, { useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import { router } from 'expo-router';
import { RetirementWarning } from '../../src/components/RetirementWarning';
import { CadreInfo } from '../../src/types';

export default function RetirementWarningScreen() {
  useEffect(() => {
    console.log('🚀 退休预警页面已加载');
  }, []);

  const handleCadrePress = (cadre: CadreInfo) => {
    console.log('🔍 点击干部:', cadre.name);
    router.push(`/cadre-detail?id=${cadre.id}&mode=view`);
  };

  return (
    <View style={styles.container}>
      <RetirementWarning onCadrePress={handleCadrePress} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7'
  }
});
