# 智慧干部信息管理系统 - 开发说明

## 项目概述

本项目是专为国有大型煤炭企业组织部开发的智慧干部信息管理系统，基于React Native + Expo技术栈，支持Android平台。系统结合中组部和山西省委组织部对国有企业干部管理要求，提供完整的干部信息管理解决方案。

## 技术架构

### 前端技术栈
- **React Native**: 0.79.4
- **Expo**: ~53.0.13
- **TypeScript**: ~5.8.3
- **Expo Router**: 路由管理
- **React Navigation**: 导航组件

### 数据库
- **SQLite**: 本地数据存储
- **expo-sqlite**: SQLite数据库操作

### 图表组件
- **react-native-chart-kit**: 图表展示
- **react-native-svg**: SVG支持

### 文件处理
- **xlsx**: Excel文件处理
- **react-native-document-picker**: 文件选择
- **expo-file-system**: 文件系统操作

### UI组件
- **@expo/vector-icons**: 图标库
- **react-native-modal**: 模态框
- **expo-image-picker**: 图片选择

## 项目结构

```
CadreManagementSystem/
├── app/                          # Expo Router页面
│   ├── (tabs)/                   # 标签页
│   │   ├── index.tsx            # 首页
│   │   ├── cadre-list.tsx       # 干部列表
│   │   ├── retirement-warning.tsx # 退休预警
│   │   └── settings.tsx         # 设置
│   ├── cadre-detail.tsx         # 干部详情页
│   └── _layout.tsx              # 根布局
├── src/                         # 源代码
│   ├── components/              # 组件
│   │   ├── CadreList.tsx       # 干部列表组件
│   │   ├── CadreDetail.tsx     # 干部详情组件
│   │   └── RetirementWarning.tsx # 退休预警组件
│   ├── database/               # 数据库
│   │   ├── database.ts         # 数据库初始化
│   │   └── cadreDao.ts         # 数据访问层
│   ├── types/                  # 类型定义
│   │   └── index.ts            # 接口定义
│   └── utils/                  # 工具类
│       ├── idCardValidator.ts  # 身份证验证
│       ├── excelImporter.ts    # Excel导入
│       └── retirementCalculator.ts # 退休预警计算
├── assets/                     # 资源文件
└── package.json               # 项目配置
```

## 核心功能模块

### 1. 数据库设计

#### 主要数据表
- **cadres**: 干部信息表
- **position_levels**: 职级管理表
- **retirement_config**: 退休预警配置表
- **system_settings**: 系统设置表

#### 数据库特性
- 自动创建表结构
- 支持数据迁移
- 完整的CRUD操作
- 事务支持

### 2. 干部信息管理

#### 功能特性
- 完整的干部信息录入（26个字段）
- 身份证号自动验证和信息提取
- 照片上传和管理
- 高级搜索和筛选
- 分页加载
- 数据导出

#### 字段包括
- 基本信息：姓名、性别、民族、籍贯等
- 工作信息：单位、职务、现职级等
- 教育信息：全日制教育、在职教育等
- 党务信息：政治面貌、入党时间等
- 其他信息：联系方式、奖励处分等

### 3. 退休预警系统

#### 预警类型
1. **近两年退休预警**
   - 男性：58-60周岁
   - 女性：56-58周岁

2. **月度退休预警**
   - 男性：59-60周岁
   - 女性：57-58周岁

3. **退居二线预警**
   - 中层正职/副职：56-58周岁

4. **已退休人员**
   - 男性：>60周岁
   - 女性：>58周岁

#### 可视化展示
- 饼状图统计展示
- 预警列表详情
- 倒计时显示
- 紧急程度标识

### 4. Excel导入功能

#### 支持特性
- 自动识别表头字段
- 数据验证和清洗
- 身份证号自动校验
- 批量导入处理
- 错误报告生成
- 模板文件导出

### 5. 职级管理

#### 默认职级体系
- 集团级：中层正职、中层副职
- 二级公司：中层正职、中层副职、主管级、主办级、技术主管等
- 三级公司：班子成员、中层正职、中层副职、主管级、主办级、技术主管等

#### 管理功能
- 职级增删改查
- 分类管理
- 排序功能
- 与干部信息关联

## 开发环境搭建

### 1. 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0
- Android Studio (Android开发)
- Expo CLI

### 2. 安装依赖
```bash
cd CadreManagementSystem
npm install
```

### 3. 启动开发服务器
```bash
npm start
```

### 4. 运行Android应用
```bash
npm run android
```

## 部署说明

### 1. 构建APK
```bash
# 构建开发版本
expo build:android

# 构建生产版本
expo build:android --type app-bundle
```

### 2. 配置签名
在app.json中配置Android签名信息：
```json
{
  "expo": {
    "android": {
      "package": "com.yourcompany.cadremanagement",
      "versionCode": 1
    }
  }
}
```

## 使用说明

### 1. 首次使用
- 应用启动时自动初始化数据库
- 可通过Excel导入现有干部数据
- 配置职级体系

### 2. 日常操作
- 添加/编辑干部信息
- 查看退休预警
- 导出数据报告
- 管理职级分类

### 3. 数据管理
- 定期备份数据
- 导入新的干部信息
- 更新退休预警配置

## 注意事项

### 1. 数据安全
- 本地SQLite数据库存储
- 敏感信息加密处理
- 定期数据备份

### 2. 性能优化
- 分页加载大量数据
- 图片压缩和缓存
- 数据库索引优化

### 3. 兼容性
- 支持Android 6.0+
- 适配不同屏幕尺寸
- 支持横竖屏切换

## 后续开发计划

### 1. 功能扩展
- 数据云端同步
- 多用户权限管理
- 报表生成功能
- 消息推送

### 2. 性能优化
- 数据库查询优化
- 界面渲染优化
- 内存使用优化

### 3. 用户体验
- 界面美化
- 操作流程优化
- 帮助文档完善

## 技术支持

如有技术问题或功能需求，请联系开发团队。

---

**智慧干部信息管理系统 v1.0.0**  
专为国有企业干部管理设计的智能化解决方案
