console.log('🔍 测试毕海宇和申丽丽的精确计算...\n');

// 精确的延迟退休计算函数
function calculateDelayRetirement(birthYear, baseRetirementAge) {
  let delayMonths = 0;
  
  if (birthYear >= 1965) {
    // 基础延迟3个月
    delayMonths = 3;
    
    if (birthYear >= 1966) {
      // 1966年及以后的额外延迟
      const extraYears = birthYear - 1965;
      
      if (birthYear <= 1970) {
        // 1966-1970: 每年额外2个月，最多1年
        delayMonths += Math.min(extraYears * 2, 12);
      } else if (birthYear <= 1980) {
        // 1971-1980: 前5年每年2个月，后续每年3个月，最多3年
        const firstPhase = Math.min(5, extraYears) * 2;
        const secondPhase = Math.max(0, extraYears - 5) * 3;
        delayMonths += Math.min(firstPhase + secondPhase, 36);
      } else {
        // 1981+: 前5年每年2个月，6-10年每年3个月，后续每年4个月，最多5年
        const firstPhase = Math.min(5, extraYears) * 2;
        const secondPhase = Math.min(5, Math.max(0, extraYears - 5)) * 3;
        const thirdPhase = Math.max(0, extraYears - 10) * 4;
        delayMonths += Math.min(firstPhase + secondPhase + thirdPhase, 60);
      }
    }
  }
  
  return {
    delayYears: Math.floor(delayMonths / 12),
    delayMonths: delayMonths % 12,
    totalDelayMonths: delayMonths,
    originalRetirementAge: baseRetirementAge,
    actualRetirementAge: baseRetirementAge + (delayMonths / 12)
  };
}

// 计算距离退休的天数
function calculateDaysUntilRetirement(birthDate, actualRetirementAge) {
  const retirementDate = new Date(birthDate);
  retirementDate.setFullYear(birthDate.getFullYear() + Math.floor(actualRetirementAge));
  
  // 处理延迟的月数
  const delayMonths = Math.round((actualRetirementAge - Math.floor(actualRetirementAge)) * 12);
  retirementDate.setMonth(retirementDate.getMonth() + delayMonths);

  const today = new Date();
  const diffTime = retirementDate.getTime() - today.getTime();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

// 计算距离法定退休年龄的天数
function calculateDaysToLegalRetirement(birthDate, legalRetirementAge) {
  const legalRetirementDate = new Date(birthDate);
  legalRetirementDate.setFullYear(birthDate.getFullYear() + legalRetirementAge);

  const today = new Date();
  const diffTime = legalRetirementDate.getTime() - today.getTime();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

// 计算当前年龄
function calculateAge(birthDate) {
  const today = new Date();
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  
  return age;
}

// 测试数据
const testCases = [
  {
    name: '毕海宇',
    gender: '男',
    birthDate: new Date(1965, 7, 17), // 1965年8月17日
    currentDisplay: {
      delayMonths: 0,
      daysToLegal: 46,
      daysToActual: 46
    }
  },
  {
    name: '申丽丽',
    gender: '女', 
    birthDate: new Date(1969, 2, 15), // 1969年3月15日
    currentDisplay: {
      delayMonths: 21, // 1年9个月
      daysToLegal: 821,
      daysToActual: 821
    }
  }
];

console.log('═══════════════════════════════════════════════════════════════');
console.log('🧮 精确计算验证');
console.log('═══════════════════════════════════════════════════════════════\n');

testCases.forEach((testCase, index) => {
  console.log(`${index + 1}. ${testCase.name}（${testCase.gender}，${testCase.birthDate.toLocaleDateString()}）：`);
  
  const birthYear = testCase.birthDate.getFullYear();
  const currentAge = calculateAge(testCase.birthDate);
  const legalRetirementAge = testCase.gender === '女' ? 55 : 60;
  
  // 计算延迟退休信息
  const delayInfo = calculateDelayRetirement(birthYear, legalRetirementAge);
  
  // 计算距离法定退休年龄的天数
  const daysToLegal = calculateDaysToLegalRetirement(testCase.birthDate, legalRetirementAge);
  
  // 计算距离实际退休的天数
  const daysToActual = calculateDaysUntilRetirement(testCase.birthDate, delayInfo.actualRetirementAge);
  
  console.log(`   📊 基础信息：`);
  console.log(`      当前年龄：${currentAge}岁`);
  console.log(`      法定退休年龄：${legalRetirementAge}岁`);
  console.log(`      出生年份：${birthYear}年`);
  
  console.log(`   🔢 延迟退休计算：`);
  console.log(`      基础延迟：3个月（1965年政策）`);
  if (birthYear > 1965) {
    const extraYears = birthYear - 1965;
    console.log(`      额外延迟：${extraYears}年 × 2个月/年 = ${extraYears * 2}个月`);
  }
  console.log(`      总延迟：${delayInfo.totalDelayMonths}个月 = ${delayInfo.delayYears}年${delayInfo.delayMonths}个月`);
  console.log(`      实际退休年龄：${delayInfo.actualRetirementAge.toFixed(1)}岁`);
  
  console.log(`   ⏰ 倒计时计算：`);
  console.log(`      距离法定退休年龄：${daysToLegal}天`);
  console.log(`      距离实际退休：${daysToActual}天`);
  
  console.log(`   📱 当前显示：`);
  console.log(`      延迟月数：${testCase.currentDisplay.delayMonths}个月`);
  console.log(`      距法定退休：${testCase.currentDisplay.daysToLegal}天`);
  console.log(`      距实际退休：${testCase.currentDisplay.daysToActual}天`);
  
  console.log(`   ✅ 正确计算：`);
  console.log(`      延迟月数：${delayInfo.totalDelayMonths}个月`);
  console.log(`      距法定退休：${daysToLegal}天`);
  console.log(`      距实际退休：${daysToActual}天`);
  
  // 验证准确性
  const delayCorrect = delayInfo.totalDelayMonths === testCase.currentDisplay.delayMonths;
  const legalCorrect = Math.abs(daysToLegal - testCase.currentDisplay.daysToLegal) <= 1;
  const actualCorrect = Math.abs(daysToActual - testCase.currentDisplay.daysToActual) <= 1;
  
  console.log(`   🎯 验证结果：`);
  console.log(`      延迟月数：${delayCorrect ? '✅ 正确' : '❌ 错误'}`);
  console.log(`      法定退休倒计时：${legalCorrect ? '✅ 正确' : '❌ 错误'}`);
  console.log(`      实际退休倒计时：${actualCorrect ? '✅ 正确' : '❌ 错误'}`);
  
  if (!delayCorrect || !legalCorrect || !actualCorrect) {
    console.log(`   🔧 需要修复的问题：`);
    if (!delayCorrect) {
      console.log(`      - 延迟月数计算错误：显示${testCase.currentDisplay.delayMonths}个月，应为${delayInfo.totalDelayMonths}个月`);
    }
    if (!legalCorrect) {
      console.log(`      - 法定退休倒计时错误：显示${testCase.currentDisplay.daysToLegal}天，应为${daysToLegal}天`);
    }
    if (!actualCorrect) {
      console.log(`      - 实际退休倒计时错误：显示${testCase.currentDisplay.daysToActual}天，应为${daysToActual}天`);
    }
  }
  
  console.log('');
});

console.log('═══════════════════════════════════════════════════════════════');
console.log('🔧 修复建议');
console.log('═══════════════════════════════════════════════════════════════\n');

console.log('1. 毕海宇（男，1965年8月17日）问题：');
console.log('   ❌ 当前显示：延迟0个月');
console.log('   ✅ 应该显示：延迟3个月（1965年基础延迟）');
console.log('   🔧 修复：确保1965年出生的人员也有3个月基础延迟\n');

console.log('2. 申丽丽（女，1969年3月15日）问题：');
console.log('   ❌ 当前显示：可能有计算偏差');
console.log('   ✅ 应该显示：延迟11个月（基础3个月 + 4年×2个月）');
console.log('   🔧 修复：确保女性使用55岁法定退休年龄计算\n');

console.log('3. 主要修复点：');
console.log('   - configurableRetirementCalculator.ts中的calculateDelayRetirement方法');
console.log('   - RetirementWarning.tsx中的renderWarningFooter方法');
console.log('   - 确保延迟退休计算逻辑一致');
console.log('   - 确保倒计时计算使用正确的退休日期');

console.log('\n🎉 精确计算验证完成！');
