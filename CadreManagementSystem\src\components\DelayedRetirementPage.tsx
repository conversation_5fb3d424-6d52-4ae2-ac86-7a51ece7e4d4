import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  RefreshControl,
  ActivityIndicator,
  Alert
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { CadreInfo } from '../types';
import { CadreDao } from '../database/cadreDao';
import { CadreDetailModal } from './CadreDetailModal';
import { CadreStatusModal } from './CadreStatusModal';

const { width } = Dimensions.get('window');

interface DelayedRetirementPageProps {
  onBack: () => void;
}

export const DelayedRetirementPage: React.FC<DelayedRetirementPageProps> = ({ onBack }) => {
  const [delayedCadres, setDelayedCadres] = useState<CadreInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  
  // 弹窗状态
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [statusModalVisible, setStatusModalVisible] = useState(false);
  const [selectedCadre, setSelectedCadre] = useState<CadreInfo | null>(null);

  // 加载延迟退休人员数据
  const loadDelayedRetirementCadres = async () => {
    try {
      console.log('🔍 延迟退休页面：开始加载延迟退休人员数据...');
      
      // 获取所有干部数据
      const allCadres = await CadreDao.getAllCadres();
      console.log(`📊 延迟退休页面：获取到${allCadres.length}条干部数据`);
      
      // 筛选出手动标记为延迟退休的人员
      const delayed = allCadres.filter(cadre => cadre.退休状态 === 'delayed_retirement');
      console.log(`⚠️ 延迟退休页面：找到${delayed.length}名延迟退休人员`);
      
      if (delayed.length > 0) {
        console.log('📋 延迟退休人员列表：');
        delayed.forEach((cadre, index) => {
          console.log(`${index + 1}. ${cadre.姓名} - ${cadre.职务} - ${cadre.单位}`);
        });
      }
      
      setDelayedCadres(delayed);
    } catch (error) {
      console.error('❌ 加载延迟退休人员数据失败:', error);
      Alert.alert('错误', '加载延迟退休人员数据失败');
      setDelayedCadres([]);
    }
  };

  // 初始加载
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await loadDelayedRetirementCadres();
      setLoading(false);
    };
    
    loadData();
  }, []);

  // 下拉刷新
  const onRefresh = async () => {
    setRefreshing(true);
    await loadDelayedRetirementCadres();
    setRefreshing(false);
  };

  // 计算年龄
  const calculateAge = (birthDateStr: string | number | undefined): number => {
    if (!birthDateStr) return 0;
    
    try {
      let birthDate: Date;
      
      if (typeof birthDateStr === 'number') {
        // Excel日期序列号
        birthDate = new Date((birthDateStr - 25569) * 86400 * 1000);
      } else {
        birthDate = new Date(birthDateStr);
      }
      
      if (isNaN(birthDate.getTime())) return 0;
      
      const today = new Date();
      let age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }
      
      return age;
    } catch (error) {
      return 0;
    }
  };

  // 处理干部卡片点击
  const handleCadrePress = (cadre: CadreInfo) => {
    setSelectedCadre(cadre);
    setDetailModalVisible(true);
  };

  // 处理干部卡片长按
  const handleCadreLongPress = (cadre: CadreInfo) => {
    setSelectedCadre(cadre);
    setStatusModalVisible(true);
  };

  // 处理状态更新
  const handleStatusUpdate = async () => {
    await loadDelayedRetirementCadres();
  };

  // 格式化出生日期显示
  const formatBirthDate = (birthDate: string | number | undefined): string => {
    if (!birthDate) return '';

    try {
      let date: Date;
      if (typeof birthDate === 'number') {
        date = new Date((birthDate - 25569) * 86400 * 1000);
      } else {
        date = new Date(birthDate);
      }

      if (isNaN(date.getTime())) return '';

      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    } catch (error) {
      return '';
    }
  };

  // 计算延迟退休信息（统一逻辑）
  const calculateDelayRetirement = (birthYear: number) => {
    // 使用统一的延迟退休计算逻辑
    let delayMonths = 0;

    if (birthYear >= 1965) {
      // 基础延迟3个月
      delayMonths = 3;

      if (birthYear >= 1966) {
        // 1966年及以后的额外延迟
        const extraYears = birthYear - 1965;

        if (birthYear <= 1970) {
          // 1966-1970: 每年额外2个月，最多1年
          delayMonths += Math.min(extraYears * 2, 12);
        } else if (birthYear <= 1980) {
          // 1971-1980: 前5年每年2个月，后续每年3个月，最多3年
          const firstPhase = Math.min(5, extraYears) * 2;
          const secondPhase = Math.max(0, extraYears - 5) * 3;
          delayMonths += Math.min(firstPhase + secondPhase, 36);
        } else {
          // 1981+: 前5年每年2个月，6-10年每年3个月，后续每年4个月，最多5年
          const firstPhase = Math.min(5, extraYears) * 2;
          const secondPhase = Math.min(5, Math.max(0, extraYears - 5)) * 3;
          const thirdPhase = Math.max(0, extraYears - 10) * 4;
          delayMonths += Math.min(firstPhase + secondPhase + thirdPhase, 60);
        }
      }
    }

    return {
      delayYears: Math.floor(delayMonths / 12),
      delayMonths: delayMonths % 12,
      totalDelayMonths: delayMonths
    };
  };

  // 计算距离退休天数（统一逻辑）
  const calculateDaysUntilRetirement = (birthDate: Date, actualRetirementAge: number): number => {
    const retirementDate = new Date(birthDate);
    retirementDate.setFullYear(birthDate.getFullYear() + Math.floor(actualRetirementAge));

    // 处理延迟的月数
    const delayMonths = Math.round((actualRetirementAge - Math.floor(actualRetirementAge)) * 12);
    retirementDate.setMonth(retirementDate.getMonth() + delayMonths);

    const today = new Date();
    const diffTime = retirementDate.getTime() - today.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  // 格式化剩余时间显示
  const formatTimeRemaining = (days: number): { text: string; daysText: string } => {
    if (days <= 0) {
      return { text: '', daysText: `${Math.abs(days)}天` };
    }

    const years = Math.floor(days / 365);
    const months = Math.floor((days % 365) / 30);
    const remainingDays = days % 30;

    if (years > 0) {
      return {
        text: `${years}年${months}个月`,
        daysText: `${remainingDays}天`
      };
    } else if (months > 0) {
      return {
        text: `${months}个月`,
        daysText: `${remainingDays}天`
      };
    } else {
      return {
        text: '',
        daysText: `${days}天`
      };
    }
  };

  // 检查是否符合退居二线条件
  const checkSecondLineCondition = (cadre: CadreInfo, age: number): boolean => {
    const position = cadre.现职级 || cadre.职务 || '';
    const secondLinePositions = ['中层正职', '中层副职', '正处', '副处'];

    // 检查职级匹配和年龄条件（56-58岁）
    const isPositionMatch = secondLinePositions.some(pos => position.includes(pos));
    const isAgeMatch = age >= 56 && age <= 58;

    return isPositionMatch && isAgeMatch;
  };

  // 渲染干部卡片
  const renderCadreCard = (cadre: CadreInfo) => {
    const age = calculateAge(cadre.出生日期);
    const birthDate = new Date(cadre.出生日期 || '');
    const birthYear = birthDate.getFullYear();
    const gender = cadre.性别 || '男';

    // 计算延迟退休信息
    const delayInfo = calculateDelayRetirement(birthYear);
    const baseRetirementAge = gender === '女' ? 55 : 60;
    const actualRetirementAge = baseRetirementAge + (delayInfo.totalDelayMonths / 12);

    // 计算距离退休的天数
    const legalRetirementDays = calculateDaysUntilRetirement(birthDate, baseRetirementAge);
    const actualRetirementDays = calculateDaysUntilRetirement(birthDate, actualRetirementAge);

    // 检查是否符合退居二线条件
    const meetsSecondLineCondition = checkSecondLineCondition(cadre, age);

    // 格式化时间显示
    const formatTimeDisplay = (days: number): { years: number; months: number; remainingDays: number } => {
      if (days <= 0) return { years: 0, months: 0, remainingDays: Math.abs(days) };

      const years = Math.floor(days / 365);
      const months = Math.floor((days % 365) / 30);
      const remainingDays = days % 30;

      return { years, months, remainingDays };
    };

    const legalTimeDisplay = formatTimeDisplay(legalRetirementDays);

    return (
      <TouchableOpacity
        key={cadre.id}
        style={styles.cadreCard}
        onPress={() => handleCadrePress(cadre)}
        onLongPress={() => handleCadreLongPress(cadre)}
        activeOpacity={0.7}
      >
        <View style={styles.cardHeader}>
          <View style={styles.nameSection}>
            <View style={styles.nameRow}>
              <Text style={styles.cadreName}>{cadre.姓名}</Text>
              <Text style={styles.birthDate}>
                {formatBirthDate(cadre.出生日期)}
              </Text>
            </View>
            <Text style={styles.unitPositionText} numberOfLines={1} ellipsizeMode="tail">
              {`${cadre.单位 || '未知单位'}|${cadre.职务 || '未知职务'}`}
            </Text>
          </View>
          <View style={styles.statusBadge}>
            <Text style={styles.statusText}>延迟退休</Text>
          </View>
        </View>

        <View style={styles.cardContent}>
          {/* 状态描述 */}
          <Text style={styles.statusInfoText}>
            {meetsSecondLineCondition ? '已达退居二线条件，延迟退休' : '延迟退休状态'}
          </Text>

          {/* 实际退休年龄 */}
          <Text style={styles.retirementAgeText}>
            实际退休年龄: {actualRetirementAge.toFixed(1)}岁
          </Text>

          {/* 预计延迟退休信息 */}
          {delayInfo.delayYears > 0 || delayInfo.delayMonths > 0 ? (
            <Text style={styles.delayInfoText}>
              预计延迟{delayInfo.delayYears > 0 ? `${delayInfo.delayYears}年${delayInfo.delayMonths}个月` : `${delayInfo.delayMonths}个月`}退休
            </Text>
          ) : (
            <Text style={styles.delayInfoText}>无延迟退休</Text>
          )}

          {/* 距离法定退休时间 */}
          {legalRetirementDays > 0 ? (
            <View style={styles.timeInfoRow}>
              <Text style={styles.timeInfoText}>
                距离法定退休时间剩余{legalTimeDisplay.years > 0 ? `${legalTimeDisplay.years}年` : ''}
                {legalTimeDisplay.months > 0 ? `${legalTimeDisplay.months}个月` : ''}
                （剩余
              </Text>
              <Text style={styles.daysHighlight}>
                {legalRetirementDays}天
              </Text>
              <Text style={styles.timeInfoText}>）</Text>
            </View>
          ) : (
            <Text style={styles.timeInfoText}>已达到法定退休年龄</Text>
          )}
        </View>

        <View style={styles.cardFooter}>
          <Text style={styles.footerText}>长按可修改状态</Text>
        </View>
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={onBack}>
            <Ionicons name="arrow-back" size={24} color="#007AFF" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>延迟退休人员</Text>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>加载中...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* 头部 */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={onBack}>
          <Ionicons name="arrow-back" size={24} color="#007AFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>延迟退休人员</Text>
      </View>

      {/* 统计信息 */}
      <View style={styles.statsContainer}>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{delayedCadres.length}</Text>
          <Text style={styles.statLabel}>延迟退休人员</Text>
        </View>
      </View>

      {/* 人员列表 */}
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {delayedCadres.length > 0 ? (
          <View style={styles.cardContainer}>
            {delayedCadres.map(renderCadreCard)}
          </View>
        ) : (
          <View style={styles.emptyContainer}>
            <Ionicons name="people-outline" size={64} color="#C7C7CC" />
            <Text style={styles.emptyText}>暂无延迟退休人员</Text>
            <Text style={styles.emptySubText}>可通过长按干部卡片设置延迟退休状态</Text>
          </View>
        )}
      </ScrollView>

      {/* 弹窗 */}
      {selectedCadre && (
        <>
          <CadreDetailModal
            visible={detailModalVisible}
            cadre={selectedCadre}
            onClose={() => {
              setDetailModalVisible(false);
              setSelectedCadre(null);
            }}
          />
          
          <CadreStatusModal
            visible={statusModalVisible}
            cadre={selectedCadre}
            onClose={() => {
              setStatusModalVisible(false);
              setSelectedCadre(null);
            }}
            onStatusUpdated={handleStatusUpdate}
          />
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  backButton: {
    marginRight: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#8E8E93',
  },
  statsContainer: {
    padding: 16,
    backgroundColor: '#FFFFFF',
    marginBottom: 8,
  },
  statCard: {
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#FF9500',
    borderRadius: 12,
  },
  statNumber: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  statLabel: {
    fontSize: 14,
    color: '#FFFFFF',
    marginTop: 4,
  },
  scrollView: {
    flex: 1,
  },
  cardContainer: {
    padding: 16,
  },
  cadreCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 14,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 10,
  },
  nameSection: {
    flex: 1,
  },
  nameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  cadreName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    marginRight: 8,
  },
  birthDate: {
    fontSize: 14,
    color: '#FF9500',
    fontWeight: '500',
  },
  unitPositionText: {
    fontSize: 13,
    color: '#8E8E93',
    fontWeight: '500',
  },
  statusBadge: {
    backgroundColor: '#FF9500',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  statusText: {
    fontSize: 12,
    color: '#FFFFFF',
    fontWeight: '500',
  },
  cardContent: {
    marginBottom: 8,
  },
  statusInfoText: {
    fontSize: 13,
    color: '#FF9500',
    fontWeight: '600',
    marginBottom: 3,
    lineHeight: 18,
  },
  retirementAgeText: {
    fontSize: 13,
    color: '#007AFF',
    fontWeight: '500',
    marginBottom: 3,
    lineHeight: 18,
  },
  timeInfoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  timeInfoText: {
    fontSize: 12,
    color: '#8E8E93',
    lineHeight: 16,
  },
  daysHighlight: {
    fontSize: 12,
    color: '#FF3B30',
    fontWeight: 'bold',
  },
  delayInfoText: {
    fontSize: 13,
    color: '#FF9500',
    fontWeight: '500',
    marginBottom: 3,
    lineHeight: 18,
  },
  cardFooter: {
    borderTopWidth: 1,
    borderTopColor: '#E5E5EA',
    paddingTop: 6,
    marginTop: 4,
  },
  footerText: {
    fontSize: 11,
    color: '#C7C7CC',
    textAlign: 'center',
    lineHeight: 14,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 18,
    color: '#8E8E93',
    marginTop: 16,
  },
  emptySubText: {
    fontSize: 14,
    color: '#C7C7CC',
    marginTop: 8,
    textAlign: 'center',
  },
});
