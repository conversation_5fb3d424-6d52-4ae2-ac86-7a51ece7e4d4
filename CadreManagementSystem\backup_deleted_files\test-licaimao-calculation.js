console.log('🔍 测试李财茂退休计算是否正确...\n');

// 统一的延迟退休计算函数
function calculateDelayRetirement(birthYear) {
  let delayMonths = 0;
  
  if (birthYear >= 1965) {
    // 基础延迟3个月
    delayMonths = 3;
    
    if (birthYear >= 1966) {
      // 1966年及以后的额外延迟
      const extraYears = birthYear - 1965;
      
      if (birthYear <= 1970) {
        // 1966-1970: 每年额外2个月，最多1年
        delayMonths += Math.min(extraYears * 2, 12);
      } else if (birthYear <= 1980) {
        // 1971-1980: 前5年每年2个月，后续每年3个月，最多3年
        const firstPhase = Math.min(5, extraYears) * 2;
        const secondPhase = Math.max(0, extraYears - 5) * 3;
        delayMonths += Math.min(firstPhase + secondPhase, 36);
      } else {
        // 1981+: 前5年每年2个月，6-10年每年3个月，后续每年4个月，最多5年
        const firstPhase = Math.min(5, extraYears) * 2;
        const secondPhase = Math.min(5, Math.max(0, extraYears - 5)) * 3;
        const thirdPhase = Math.max(0, extraYears - 10) * 4;
        delayMonths += Math.min(firstPhase + secondPhase + thirdPhase, 60);
      }
    }
  }
  
  return {
    delayYears: Math.floor(delayMonths / 12),
    delayMonths: delayMonths % 12,
    totalDelayMonths: delayMonths
  };
}

// 计算距离退休的天数
function calculateDaysUntilRetirement(birthDate, actualRetirementAge) {
  const retirementDate = new Date(birthDate);
  retirementDate.setFullYear(birthDate.getFullYear() + Math.floor(actualRetirementAge));
  
  // 处理延迟的月数
  const delayMonths = Math.round((actualRetirementAge - Math.floor(actualRetirementAge)) * 12);
  retirementDate.setMonth(retirementDate.getMonth() + delayMonths);

  const today = new Date();
  const diffTime = retirementDate.getTime() - today.getTime();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

// 计算当前年龄
function calculateAge(birthDate) {
  const today = new Date();
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  
  return age;
}

// 李财茂的信息
const licaimao = {
  name: '李财茂',
  gender: '男',
  birthDate: new Date(1965, 5, 3), // 1965年6月3日（注意：月份从0开始）
  birthYear: 1965
};

console.log('═══════════════════════════════════════════════════════════════');
console.log('🧮 李财茂退休计算验证');
console.log('═══════════════════════════════════════════════════════════════\n');

console.log(`📊 基础信息：`);
console.log(`   姓名：${licaimao.name}`);
console.log(`   性别：${licaimao.gender}`);
console.log(`   出生日期：${licaimao.birthDate.toLocaleDateString()}`);
console.log(`   出生年份：${licaimao.birthYear}年`);
console.log(`   当前日期：${new Date().toLocaleDateString()}`);

const currentAge = calculateAge(licaimao.birthDate);
const baseRetirementAge = licaimao.gender === '女' ? 55 : 60;
const delayInfo = calculateDelayRetirement(licaimao.birthYear);
const actualRetirementAge = baseRetirementAge + (delayInfo.totalDelayMonths / 12);

console.log(`\n🔢 延迟退休计算：`);
console.log(`   当前年龄：${currentAge}岁`);
console.log(`   法定退休年龄：${baseRetirementAge}岁`);
console.log(`   延迟月数：${delayInfo.totalDelayMonths}个月 = ${delayInfo.delayYears}年${delayInfo.delayMonths}个月`);
console.log(`   实际退休年龄：${actualRetirementAge.toFixed(2)}岁`);

// 计算退休日期
const retirementDate = new Date(licaimao.birthDate);
retirementDate.setFullYear(licaimao.birthDate.getFullYear() + Math.floor(actualRetirementAge));
const delayMonths = Math.round((actualRetirementAge - Math.floor(actualRetirementAge)) * 12);
retirementDate.setMonth(retirementDate.getMonth() + delayMonths);

console.log(`   退休日期：${retirementDate.toLocaleDateString()}`);

const daysUntilRetirement = calculateDaysUntilRetirement(licaimao.birthDate, actualRetirementAge);

console.log(`\n⏰ 倒计时计算：`);
console.log(`   距离实际退休：${daysUntilRetirement}天`);

console.log(`\n🎯 退休状态判断：`);
if (daysUntilRetirement <= 0) {
  console.log(`   ❌ 已退休（超过实际退休日期${Math.abs(daysUntilRetirement)}天）`);
  console.log(`   ✅ 应该出现在"已退休"列表中`);
} else {
  console.log(`   ✅ 未退休（还有${daysUntilRetirement}天到达实际退休日期）`);
  console.log(`   ❌ 不应该出现在"已退休"列表中`);
  
  if (daysUntilRetirement <= 365 * 2) {
    console.log(`   ✅ 应该出现在"近两年退休"列表中`);
  } else if (daysUntilRetirement <= 365 * 5) {
    console.log(`   ✅ 应该出现在"退居二线"列表中`);
  }
}

console.log(`\n🔧 问题分析：`);
console.log(`   问题：李财茂出现在"已退休"列表中，但实际上还未到退休时间`);
console.log(`   原因：matchRetiredRule方法只检查年龄是否达到法定退休年龄（60岁）`);
console.log(`   解决：修改matchRetiredRule方法，检查是否达到实际退休日期（考虑延迟退休）`);

console.log(`\n✅ 修复方案：`);
console.log(`   1. 修改matchRetiredRule方法，传入daysUntilRetirement参数`);
console.log(`   2. 只有当daysUntilRetirement <= 0时才判断为已退休`);
console.log(`   3. 确保延迟退休计算正确应用到退休状态判断中`);

console.log(`\n📋 预期修复后的结果：`);
if (daysUntilRetirement > 0) {
  console.log(`   ✅ 李财茂应该从"已退休"列表中移除`);
  if (daysUntilRetirement <= 365 * 2) {
    console.log(`   ✅ 李财茂应该出现在"近两年退休"列表中`);
  }
} else {
  console.log(`   ✅ 李财茂应该保留在"已退休"列表中`);
}

console.log('\n🎉 李财茂退休计算验证完成！');
