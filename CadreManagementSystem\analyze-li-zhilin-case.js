console.log('🔍 分析李治林退休预警情况...\n');

// 李治林基本信息
const name = '李治林';
const birthDate = new Date('1967-07-01');
const gender = '男'; // 假设为男性，如果是女性需要调整

console.log(`👤 李治林基本信息：`);
console.log(`姓名: ${name}`);
console.log(`出生日期: ${birthDate.toLocaleDateString()}`);
console.log(`性别: ${gender}`);

// 计算当前年龄
const today = new Date();
const age = today.getFullYear() - birthDate.getFullYear();
const monthDiff = today.getMonth() - birthDate.getMonth();
const dayDiff = today.getDate() - birthDate.getDate();

let exactAge = age;
if (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)) {
  exactAge--;
}

console.log(`\n📅 年龄计算：`);
console.log(`当前年龄: ${exactAge}岁`);
console.log(`详细年龄: ${exactAge}岁${monthDiff >= 0 ? monthDiff : 12 + monthDiff}个月${dayDiff >= 0 ? dayDiff : ''}天`);

// 计算基础退休年龄
const baseRetirementAge = gender === '女' ? 55 : 60;
console.log(`基础退休年龄: ${baseRetirementAge}岁`);

// 计算延迟退休信息（根据修复后的逻辑）
const birthYear = birthDate.getFullYear();
console.log(`\n🕐 延迟退休计算：`);
console.log(`出生年份: ${birthYear}`);

let totalDelayMonths = 0;
if (birthYear <= 1965) {
  // 1965年及以前：基础延迟3个月
  totalDelayMonths = 3;
  console.log(`延迟规则: 1965年及以前，基础延迟3个月`);
} else if (birthYear >= 1966 && birthYear <= 1970) {
  // 1966-1970年：基础3个月 + 每年2个月，最多1年
  const baseDelayMonths = 3;
  const yearsAfter1965 = birthYear - 1965;
  const additionalDelayMonths = Math.min(yearsAfter1965 * 2, 12 - baseDelayMonths);
  totalDelayMonths = baseDelayMonths + additionalDelayMonths;
  console.log(`延迟规则: 1966-1970年，基础3个月 + 每年2个月`);
  console.log(`超出1965年: ${yearsAfter1965}年`);
  console.log(`额外延迟: ${additionalDelayMonths}个月`);
} else if (birthYear >= 1971 && birthYear <= 1980) {
  // 1971-1980年：基础3个月 + 每年3个月，最多3年
  const baseDelayMonths = 3;
  const yearsAfter1965 = birthYear - 1965;
  const additionalDelayMonths = Math.min(yearsAfter1965 * 3, 36 - baseDelayMonths);
  totalDelayMonths = baseDelayMonths + additionalDelayMonths;
  console.log(`延迟规则: 1971-1980年，基础3个月 + 每年3个月`);
  console.log(`超出1965年: ${yearsAfter1965}年`);
  console.log(`额外延迟: ${additionalDelayMonths}个月`);
} else {
  // 1981年及以后：基础3个月 + 每年4个月，最多5年
  const baseDelayMonths = 3;
  const yearsAfter1965 = birthYear - 1965;
  const additionalDelayMonths = Math.min(yearsAfter1965 * 4, 60 - baseDelayMonths);
  totalDelayMonths = baseDelayMonths + additionalDelayMonths;
  console.log(`延迟规则: 1981年及以后，基础3个月 + 每年4个月`);
  console.log(`超出1965年: ${yearsAfter1965}年`);
  console.log(`额外延迟: ${additionalDelayMonths}个月`);
}

const delayYears = Math.floor(totalDelayMonths / 12);
const delayMonths = totalDelayMonths % 12;
const actualRetirementAge = baseRetirementAge + (totalDelayMonths / 12);

console.log(`总延迟月数: ${totalDelayMonths}个月`);
console.log(`延迟时间: ${delayYears}年${delayMonths}个月`);
console.log(`实际退休年龄: ${actualRetirementAge}岁`);

// 计算退休日期
const retirementDate = new Date(birthDate);
retirementDate.setFullYear(birthDate.getFullYear() + Math.floor(actualRetirementAge));
const delayMonthsForDate = Math.round((actualRetirementAge - Math.floor(actualRetirementAge)) * 12);
retirementDate.setMonth(retirementDate.getMonth() + delayMonthsForDate);

console.log(`\n📆 退休时间计算：`);
console.log(`退休日期: ${retirementDate.toLocaleDateString()}`);

// 计算剩余天数
const diffTime = retirementDate.getTime() - today.getTime();
const daysUntilRetirement = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

console.log(`距离退休剩余天数: ${daysUntilRetirement}天`);

// 转换为年月日
const remainingYears = Math.floor(daysUntilRetirement / 365);
const remainingMonths = Math.floor((daysUntilRetirement % 365) / 30);
const remainingDays = daysUntilRetirement % 30;

console.log(`剩余时间: ${remainingYears}年${remainingMonths}个月${remainingDays}天`);

// 判断是否符合近两年退休预警
const twoYearsFromNow = new Date();
twoYearsFromNow.setFullYear(twoYearsFromNow.getFullYear() + 2);

const isWithinTwoYears = retirementDate <= twoYearsFromNow;

console.log(`\n🎯 近两年退休预警分析：`);
console.log(`两年后日期: ${twoYearsFromNow.toLocaleDateString()}`);
console.log(`是否在近两年内退休: ${isWithinTwoYears ? '是' : '否'}`);

if (isWithinTwoYears) {
  console.log(`✅ 李治林符合近两年退休预警条件`);
  
  // 分析可能的原因
  console.log(`\n🔍 如果在预警列表中没有看到李治林，可能的原因：`);
  console.log(`1. 数据库中李治林的出生日期可能不是1967年7月1日`);
  console.log(`2. 李治林的性别信息可能不正确（女性退休年龄55岁）`);
  console.log(`3. 李治林可能已被手动标记为其他状态（如调离、延迟退休等）`);
  console.log(`4. 退休预警计算逻辑可能还有其他问题`);
  console.log(`5. 数据库查询条件可能过滤掉了李治林`);
  console.log(`6. 李治林的现职级可能不符合退休预警的职级条件`);
  
  console.log(`\n📋 建议检查步骤：`);
  console.log(`1. 在干部列表中搜索"李治林"，确认其基本信息`);
  console.log(`2. 检查李治林的出生日期是否为1967年7月1日`);
  console.log(`3. 确认李治林的性别信息`);
  console.log(`4. 查看李治林是否被手动标记为特殊状态`);
  console.log(`5. 检查李治林的现职级是否符合退休预警条件`);
  console.log(`6. 查看退休预警的筛选条件是否正确`);
  
} else {
  console.log(`❌ 李治林不符合近两年退休预警条件`);
  console.log(`原因: 距离退休还有${Math.floor(daysUntilRetirement/365)}年多，超过了两年的预警范围`);
}

// 计算紧急程度
let urgencyLevel = 'info';
if (daysUntilRetirement <= 0) {
  urgencyLevel = 'info'; // 已退休
} else if (daysUntilRetirement <= 90) { // 小于3个月
  urgencyLevel = 'critical'; // 非常紧急
} else if (daysUntilRetirement <= 180) { // 3-6个月
  urgencyLevel = 'urgent'; // 紧急
} else if (daysUntilRetirement <= 730) { // 6个月-2年
  urgencyLevel = 'normal'; // 一般
} else {
  urgencyLevel = 'info'; // 信息
}

console.log(`\n⚠️ 预警级别分析：`);
console.log(`预警级别: ${urgencyLevel}`);
switch(urgencyLevel) {
  case 'critical':
    console.log(`级别说明: 非常紧急（3个月内退休）`);
    break;
  case 'urgent':
    console.log(`级别说明: 紧急（3-6个月内退休）`);
    break;
  case 'normal':
    console.log(`级别说明: 一般（6个月-2年内退休）`);
    break;
  case 'info':
    console.log(`级别说明: 信息（2年以上或已退休）`);
    break;
}

console.log(`\n📊 总结：`);
if (isWithinTwoYears) {
  console.log(`李治林（1967年7月1日出生）应该出现在近两年退休预警列表中。`);
  console.log(`如果没有看到，请按照上述建议检查步骤进行排查。`);
} else {
  console.log(`李治林（1967年7月1日出生）不应该出现在近两年退休预警列表中，`);
  console.log(`因为距离退休还有${Math.floor(daysUntilRetirement/365)}年多。`);
}

console.log(`\n✅ 李治林退休预警分析完成！`);
