@echo off
chcp 65001 >nul
echo 🔧 EXPO开发服务器修复工具
echo ================================
echo.

echo 📋 步骤1: 检查Node.js环境
node --version
if %errorlevel% neq 0 (
    echo ❌ Node.js未正确安装
    echo 💡 请先安装Node.js: https://nodejs.org/
    pause
    exit /b 1
)

npm --version
if %errorlevel% neq 0 (
    echo ❌ npm未正确安装
    pause
    exit /b 1
)

echo ✅ Node.js环境正常
echo.

echo 📋 步骤2: 清理所有缓存
echo 正在清理npm缓存...
npm cache clean --force

echo 正在清理EXPO缓存...
if exist ".expo" rmdir /s /q ".expo"
if exist "node_modules\.cache" rmdir /s /q "node_modules\.cache"

echo 正在清理Metro缓存...
if exist "%TEMP%\metro-*" del /q "%TEMP%\metro-*"
if exist "%TEMP%\haste-map-*" del /q "%TEMP%\haste-map-*"

echo ✅ 缓存清理完成
echo.

echo 📋 步骤3: 重新安装依赖
echo 正在删除node_modules...
if exist "node_modules" rmdir /s /q "node_modules"

echo 正在重新安装依赖...
npm install
if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败，尝试使用yarn...
    yarn install
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
)

echo ✅ 依赖安装完成
echo.

echo 📋 步骤4: 检查EXPO CLI
npx expo --version
if %errorlevel% neq 0 (
    echo ❌ EXPO CLI有问题，正在重新安装...
    npm uninstall -g expo-cli @expo/cli
    npm install -g @expo/cli@latest
    if %errorlevel% neq 0 (
        echo ❌ EXPO CLI安装失败
        pause
        exit /b 1
    )
)

echo ✅ EXPO CLI正常
echo.

echo 📋 步骤5: 释放端口
echo 正在检查端口占用...
netstat -ano | findstr :19000
if %errorlevel% equ 0 (
    echo 端口19000被占用，正在释放...
    taskkill /f /im node.exe
    timeout /t 2 /nobreak >nul
)

echo ✅ 端口检查完成
echo.

echo 📋 步骤6: 启动EXPO开发服务器
echo 🚀 正在启动EXPO服务器...
echo.
echo 📱 启动后请在雷电模拟器中：
echo 1. 打开 Expo Go 应用
echo 2. 选择 "Enter URL manually"
echo 3. 输入显示的连接地址
echo 4. 点击 "Connect"
echo.

npx expo start --clear --host tunnel
