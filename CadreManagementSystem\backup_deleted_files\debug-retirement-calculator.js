/**
 * 完整模拟ConfigurableRetirementCalculator逻辑
 */
console.log('🔍 深度调试ConfigurableRetirementCalculator逻辑...\n');

// 模拟真实数据（基于EXPO日志中的前5条数据）
const testCadres = [
  {
    姓名: '张培峰',
    性别: '男',
    出生日期: '1981-02-26',
    职务: '生活服务中心一级主管业务员',
    现职级: '',
    单位: '生活服务中心'
  },
  {
    姓名: '张松霞',
    性别: '女',
    出生日期: '1974-04-28',
    职务: '生活服务中心一级主管业务员',
    现职级: '',
    单位: '生活服务中心'
  },
  {
    姓名: '卫鹏',
    性别: '男',
    出生日期: '1983-03-02',
    职务: '生活服务中心一级主管业务员',
    现职级: '',
    单位: '生活服务中心'
  },
  {
    姓名: '武家明',
    性别: '男',
    出生日期: '1969-09-28',
    职务: '机运队副队长',
    现职级: '',
    单位: '机运队'
  },
  {
    姓名: '王永雷',
    性别: '男',
    出生日期: '1981-08-13',
    职务: '机运队副队长',
    现职级: '',
    单位: '机运队'
  }
];

// 完整模拟ConfigurableRetirementCalculator的方法
class DebugRetirementCalculator {
  static parseBirthDate(birthDateStr) {
    console.log(`    🔍 解析出生日期: '${birthDateStr}'`);
    
    if (!birthDateStr) {
      console.log(`    ❌ 出生日期为空`);
      return null;
    }
    
    try {
      const cleanStr = birthDateStr.replace(/[年月日\-\.\/]/g, "-");
      const parts = cleanStr.split("-").filter(p => p.length > 0);
      
      console.log(`    📋 清理后: '${cleanStr}', 分割: [${parts.join(', ')}]`);
      
      if (parts.length >= 2) {
        const year = parseInt(parts[0]);
        const month = parseInt(parts[1]) - 1;
        const day = parts.length >= 3 ? parseInt(parts[2]) : 1;
        
        console.log(`    📅 解析: 年=${year}, 月=${month+1}, 日=${day}`);
        
        if (year > 1900 && year < 2100 && month >= 0 && month < 12) {
          const date = new Date(year, month, day);
          console.log(`    ✅ 解析成功: ${date.toLocaleDateString()}`);
          return date;
        } else {
          console.log(`    ❌ 日期范围无效`);
        }
      } else {
        console.log(`    ❌ 分割部分不足`);
      }
    } catch (error) {
      console.log(`    ❌ 解析异常: ${error.message}`);
    }
    
    return null;
  }

  static calculateAge(birthDate, currentDate) {
    const age = currentDate.getFullYear() - birthDate.getFullYear();
    const monthDiff = currentDate.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && currentDate.getDate() < birthDate.getDate())) {
      return age - 1;
    }
    
    return age;
  }

  static getBaseRetirementAge(cadre) {
    const gender = cadre.性别;
    const position = cadre.现职级 || cadre.职务 || "";
    
    console.log(`    👤 性别: ${gender}, 职务: '${position}'`);
    
    if (gender === "男") {
      console.log(`    📋 男性基础退休年龄: 60岁`);
      return 60;
    } else if (gender === "女") {
      const isCadre = position.includes("职") || position.includes("长") || 
                      position.includes("主任") || position.includes("副主任") ||
                      position.includes("正职") || position.includes("副职");
      const age = isCadre ? 55 : 50;
      console.log(`    📋 女性${isCadre ? '干部' : '工人'}基础退休年龄: ${age}岁`);
      return age;
    }
    
    console.log(`    📋 默认退休年龄: 60岁`);
    return 60;
  }

  static calculateDelayRetirement(birthYear) {
    console.log(`    🗓️ 出生年份: ${birthYear}`);
    
    let totalDelayMonths = 0;
    
    if (birthYear <= 1964) {
      totalDelayMonths = 0;
      console.log(`    📋 1964年及以前: 无延迟`);
    } else if (birthYear === 1965) {
      totalDelayMonths = 3;
      console.log(`    📋 1965年: 延迟3个月`);
    } else if (birthYear >= 1966 && birthYear <= 1970) {
      totalDelayMonths = 3;
      const extraYears = birthYear - 1965;
      const extraMonths = Math.min(extraYears * 2, 12);
      totalDelayMonths += extraMonths;
      console.log(`    📋 1966-1970年: 基础3个月 + ${extraYears}年×2个月 = ${totalDelayMonths}个月`);
    } else if (birthYear >= 1971 && birthYear <= 1980) {
      totalDelayMonths = 3 + 12;
      const extraYears = birthYear - 1970;
      const extraMonths = Math.min(extraYears * 3, 24);
      totalDelayMonths += extraMonths;
      console.log(`    📋 1971-1980年: 基础15个月 + ${extraYears}年×3个月 = ${totalDelayMonths}个月`);
    } else if (birthYear >= 1981) {
      totalDelayMonths = 3 + 12 + 24;
      const extraYears = birthYear - 1980;
      const extraMonths = Math.min(extraYears * 4, 24);
      totalDelayMonths += extraMonths;
      console.log(`    📋 1981年及以后: 基础39个月 + ${extraYears}年×4个月 = ${totalDelayMonths}个月`);
    }

    return {
      delayYears: Math.floor(totalDelayMonths / 12),
      delayMonths: totalDelayMonths % 12,
      totalDelayMonths
    };
  }

  static matchSecondLineRule(cadre, age) {
    if (age >= 56 && age <= 58) {
      const position = cadre.现职级 || cadre.职务 || "";
      const isMiddleManagement = position.includes("中层正职") || 
                                position.includes("中层副职") || 
                                position.includes("正职") || 
                                position.includes("副职");
      console.log(`    🏢 ${age}岁，职务'${position}'，是否中层: ${isMiddleManagement}`);
      return isMiddleManagement;
    }
    return false;
  }

  static analyzeSingleCadre(cadre, today) {
    console.log(`\n🔍 分析干部: ${cadre.姓名}`);
    
    const birthDate = this.parseBirthDate(cadre.出生日期);
    if (!birthDate) {
      console.log(`  ❌ 出生日期解析失败，跳过`);
      return null;
    }

    const age = this.calculateAge(birthDate, today);
    const birthYear = birthDate.getFullYear();
    const baseRetirementAge = this.getBaseRetirementAge(cadre);
    const delayInfo = this.calculateDelayRetirement(birthYear);
    const actualRetirementAge = baseRetirementAge + (delayInfo.totalDelayMonths / 12);

    console.log(`  📊 当前年龄: ${age}岁`);
    console.log(`  📊 基础退休年龄: ${baseRetirementAge}岁`);
    console.log(`  📊 延迟: ${delayInfo.totalDelayMonths}个月 (${delayInfo.delayYears}年${delayInfo.delayMonths}个月)`);
    console.log(`  📊 实际退休年龄: ${actualRetirementAge.toFixed(1)}岁`);

    const retirementDate = new Date(birthDate);
    retirementDate.setFullYear(retirementDate.getFullYear() + Math.floor(actualRetirementAge));
    retirementDate.setMonth(retirementDate.getMonth() + Math.round((actualRetirementAge % 1) * 12));

    const daysRemaining = Math.ceil((retirementDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    
    console.log(`  📊 实际退休日期: ${retirementDate.toLocaleDateString()}`);
    console.log(`  📊 距离退休天数: ${daysRemaining}天`);

    // 检查预警条件
    if (daysRemaining <= 0) {
      console.log(`  ⚠️ 已退休预警`);
      return { warningType: "retired", daysRemaining: Math.abs(daysRemaining) };
    }

    const secondLineRule = this.matchSecondLineRule(cadre, age);
    if (secondLineRule) {
      console.log(`  ⚠️ 退居二线预警`);
      return { warningType: "second_line", daysRemaining };
    }

    if (daysRemaining <= 730) {
      console.log(`  ⚠️ 近两年退休预警 (${daysRemaining}天 <= 730天)`);
      return { warningType: "near_retirement", daysRemaining };
    }

    console.log(`  ✅ 无预警 (${daysRemaining}天 > 730天)`);
    return null;
  }

  static generateWarnings(cadres) {
    console.log(`🚀 开始生成退休预警，共${cadres.length}个人员...\n`);
    
    const warnings = [];
    const today = new Date();
    
    for (const cadre of cadres) {
      try {
        const warning = this.analyzeSingleCadre(cadre, today);
        if (warning) {
          warnings.push(warning);
        }
      } catch (error) {
        console.error(`❌ 处理人员 ${cadre.姓名} 时出错:`, error);
      }
    }
    
    console.log(`\n📊 完成分析${cadres.length}个人员，生成${warnings.length}个预警`);
    return warnings;
  }
}

// 执行测试
const warnings = DebugRetirementCalculator.generateWarnings(testCadres);

console.log(`\n🎯 预警结果:`);
warnings.forEach((warning, index) => {
  console.log(`${index + 1}. ${warning.warningType} - ${warning.daysRemaining}天`);
});

if (warnings.length === 0) {
  console.log(`❌ 没有生成任何预警！`);
  console.log(`\n🔍 可能的原因:`);
  console.log(`1. 所有人员距离退休超过730天（2年）`);
  console.log(`2. 延迟退休计算导致退休时间推迟太多`);
  console.log(`3. 出生日期解析有问题`);
  console.log(`4. 退休年龄计算有问题`);
}
