# 延迟退休计算验证脚本 (PowerShell版本)
# 验证修复后的延迟退休计算逻辑

Write-Host "=== 延迟退休计算修复验证测试 ===" -ForegroundColor Green
Write-Host ""

# 定义测试用例
$testCases = @(
    @{
        Name = "李财茂"
        BirthYear = 1965
        Gender = "男"
        Position = "中层正职"
        ExpectedDelay = 3
        ExpectedAge = 60.25
    },
    @{
        Name = "武海珠"
        BirthYear = 1966
        Gender = "女"
        Position = "中层副职"
        ExpectedDelay = 5
        ExpectedAge = 55.42
    },
    @{
        Name = "王四顿"
        BirthYear = 1964
        Gender = "男"
        Position = "主管级"
        ExpectedDelay = 0
        ExpectedAge = 60.0
    }
)

# 延迟退休计算函数
function Calculate-DelayRetirement {
    param([int]$BirthYear)
    
    if ($BirthYear -le 1964) {
        return @{
            DelayYears = 0
            DelayMonths = 0
            TotalDelayMonths = 0
        }
    }

    $totalDelayMonths = 0
    
    if ($BirthYear -eq 1965) {
        $totalDelayMonths = 3
    } elseif ($BirthYear -ge 1966 -and $BirthYear -le 1970) {
        $totalDelayMonths = 3
        $extraYears = $BirthYear - 1965
        $extraMonths = [Math]::Min($extraYears * 2, 12)
        $totalDelayMonths += $extraMonths
    } elseif ($BirthYear -ge 1971 -and $BirthYear -le 1980) {
        $totalDelayMonths = 3 + 12
        $extraYears = $BirthYear - 1970
        $extraMonths = [Math]::Min($extraYears * 3, 24)
        $totalDelayMonths += $extraMonths
    } elseif ($BirthYear -ge 1981) {
        $totalDelayMonths = 3 + 12 + 24
        $extraYears = $BirthYear - 1980
        $extraMonths = [Math]::Min($extraYears * 4, 24)
        $totalDelayMonths += $extraMonths
    }
    
    $totalDelayMonths = [Math]::Min($totalDelayMonths, 60)

    return @{
        DelayYears = [Math]::Floor($totalDelayMonths / 12)
        DelayMonths = $totalDelayMonths % 12
        TotalDelayMonths = $totalDelayMonths
    }
}

# 基础退休年龄计算函数
function Get-BaseRetirementAge {
    param([string]$Gender, [string]$Position)
    
    if ($Gender -eq "男") {
        return 60
    } elseif ($Gender -eq "女") {
        $isCadre = $Position -match "职|长|主任|副主任|正职|副职"
        return if ($isCadre) { 55 } else { 50 }
    }
    
    return 60
}

# 执行测试
foreach ($testCase in $testCases) {
    Write-Host "测试人员: $($testCase.Name)" -ForegroundColor Cyan
    Write-Host "出生年份: $($testCase.BirthYear)"
    Write-Host "性别: $($testCase.Gender)"
    Write-Host "职级: $($testCase.Position)"
    
    $delayInfo = Calculate-DelayRetirement -BirthYear $testCase.BirthYear
    $baseAge = Get-BaseRetirementAge -Gender $testCase.Gender -Position $testCase.Position
    $actualAge = $baseAge + ($delayInfo.TotalDelayMonths / 12)
    
    Write-Host "基础退休年龄: $baseAge 岁"
    Write-Host "延迟信息:"
    Write-Host "  - 延迟年数: $($delayInfo.DelayYears) 年"
    Write-Host "  - 延迟月数: $($delayInfo.DelayMonths) 个月"
    Write-Host "  - 总延迟月数: $($delayInfo.TotalDelayMonths) 个月"
    Write-Host "实际退休年龄: $($actualAge.ToString('F1')) 岁"
    
    # 验证结果
    $tolerance = 0.01
    if ([Math]::Abs($actualAge - $testCase.ExpectedAge) -lt $tolerance) {
        Write-Host "✅ 计算正确！预期: $($testCase.ExpectedAge) 岁" -ForegroundColor Green
    } else {
        Write-Host "❌ 计算错误！预期: $($testCase.ExpectedAge) 岁，实际: $($actualAge.ToString('F1')) 岁" -ForegroundColor Red
    }
    
    if ($delayInfo.TotalDelayMonths -eq $testCase.ExpectedDelay) {
        Write-Host "✅ 延迟月数正确！预期: $($testCase.ExpectedDelay) 个月" -ForegroundColor Green
    } else {
        Write-Host "❌ 延迟月数错误！预期: $($testCase.ExpectedDelay) 个月，实际: $($delayInfo.TotalDelayMonths) 个月" -ForegroundColor Red
    }
    
    Write-Host "---" -ForegroundColor Gray
    Write-Host ""
}

Write-Host "=== 测试完成 ===" -ForegroundColor Green
Write-Host ""
Write-Host "如果所有测试都通过，说明延迟退休计算逻辑已修复正确！" -ForegroundColor Yellow
Write-Host "现在可以继续进行APK构建。" -ForegroundColor Yellow
