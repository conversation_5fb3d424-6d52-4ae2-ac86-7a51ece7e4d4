/**
 * 全面诊断和修复EXPO启动问题
 */

const fs = require('fs');
const path = require('path');
const { execSync, spawn } = require('child_process');

console.log('🔍 开始全面诊断EXPO启动问题...');
console.log('================================');
console.log('');

// 诊断步骤
async function diagnose() {
  console.log('📋 步骤1: 检查Node.js和npm版本');
  try {
    const nodeVersion = execSync('node --version', { encoding: 'utf8' }).trim();
    const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
    console.log(`✅ Node.js: ${nodeVersion}`);
    console.log(`✅ npm: ${npmVersion}`);
  } catch (error) {
    console.log('❌ Node.js或npm未正确安装');
    return false;
  }
  console.log('');

  console.log('📋 步骤2: 检查EXPO CLI');
  try {
    const expoVersion = execSync('npx expo --version', { encoding: 'utf8' }).trim();
    console.log(`✅ Expo CLI: ${expoVersion}`);
  } catch (error) {
    console.log('❌ Expo CLI有问题，正在重新安装...');
    try {
      execSync('npm install -g @expo/cli@latest', { stdio: 'inherit' });
      console.log('✅ Expo CLI重新安装完成');
    } catch (installError) {
      console.log('❌ Expo CLI安装失败');
      return false;
    }
  }
  console.log('');

  console.log('📋 步骤3: 检查项目依赖');
  if (!fs.existsSync('node_modules')) {
    console.log('❌ node_modules不存在，正在安装依赖...');
    try {
      execSync('npm install', { stdio: 'inherit' });
      console.log('✅ 依赖安装完成');
    } catch (error) {
      console.log('❌ 依赖安装失败');
      return false;
    }
  } else {
    console.log('✅ node_modules存在');
  }
  console.log('');

  console.log('📋 步骤4: 清理缓存');
  try {
    if (fs.existsSync('.expo')) {
      fs.rmSync('.expo', { recursive: true, force: true });
      console.log('✅ 已清理.expo缓存');
    }
    
    if (fs.existsSync('node_modules/.cache')) {
      fs.rmSync('node_modules/.cache', { recursive: true, force: true });
      console.log('✅ 已清理node_modules缓存');
    }
    
    execSync('npm cache clean --force', { stdio: 'inherit' });
    console.log('✅ 已清理npm缓存');
  } catch (error) {
    console.log('⚠️ 缓存清理部分失败，但可以继续');
  }
  console.log('');

  console.log('📋 步骤5: 检查端口占用');
  try {
    const netstat = execSync('netstat -ano | findstr :19000', { encoding: 'utf8' });
    if (netstat.trim()) {
      console.log('⚠️ 端口19000被占用:');
      console.log(netstat);
      console.log('正在尝试释放端口...');
      try {
        execSync('taskkill /f /im node.exe', { stdio: 'inherit' });
        console.log('✅ 已释放端口');
      } catch (killError) {
        console.log('⚠️ 端口释放失败，将使用其他端口');
      }
    } else {
      console.log('✅ 端口19000可用');
    }
  } catch (error) {
    console.log('✅ 端口检查完成');
  }
  console.log('');

  return true;
}

// 启动EXPO服务器
function startExpoServer() {
  console.log('🚀 启动EXPO开发服务器...');
  console.log('');
  
  const expo = spawn('npx', ['expo', 'start', '--clear'], {
    stdio: ['pipe', 'pipe', 'pipe'],
    shell: true
  });

  let hasOutput = false;
  let serverStarted = false;

  expo.stdout.on('data', (data) => {
    hasOutput = true;
    const output = data.toString();
    console.log('📤 EXPO输出:', output);
    
    // 检查启动成功的标志
    if (output.includes('Metro') || output.includes('QR code') || output.includes('exp://')) {
      if (!serverStarted) {
        serverStarted = true;
        console.log('');
        console.log('🎉 EXPO服务器启动成功！');
        console.log('');
        
        // 提取连接信息
        const lines = output.split('\n');
        for (const line of lines) {
          if (line.includes('exp://')) {
            console.log('🔗 连接地址:', line.trim());
          }
        }
        
        console.log('');
        console.log('📱 请在雷电模拟器中：');
        console.log('1. 打开 Expo Go 应用');
        console.log('2. 选择 "Enter URL manually"');
        console.log('3. 输入上面显示的连接地址');
        console.log('4. 点击 "Connect"');
        console.log('');
      }
    }
  });

  expo.stderr.on('data', (data) => {
    hasOutput = true;
    const error = data.toString();
    console.log('🚨 EXPO错误:', error);
    
    // 分析错误并提供解决方案
    if (error.includes('EADDRINUSE')) {
      console.log('💡 解决方案: 端口被占用，正在尝试其他端口...');
    } else if (error.includes('ENOENT')) {
      console.log('💡 解决方案: 命令未找到，请检查EXPO CLI安装');
    } else if (error.includes('Cannot resolve')) {
      console.log('💡 解决方案: 依赖解析失败，请检查package.json');
    }
  });

  expo.on('close', (code) => {
    console.log(`📋 EXPO进程结束，退出码: ${code}`);
    if (code !== 0) {
      console.log('❌ EXPO启动失败');
      console.log('🔄 正在尝试备用启动方式...');
      setTimeout(() => {
        startBackupServer();
      }, 2000);
    }
  });

  expo.on('error', (err) => {
    console.log('🚨 EXPO启动错误:', err.message);
  });

  // 超时检查
  setTimeout(() => {
    if (!hasOutput) {
      console.log('⚠️ 长时间无输出，可能存在问题');
      console.log('🔄 正在尝试备用方案...');
      expo.kill();
      startBackupServer();
    }
  }, 30000);

  return expo;
}

// 备用启动方式
function startBackupServer() {
  console.log('🔄 尝试备用启动方式...');
  
  const backupCommands = [
    'npx expo start --tunnel',
    'npx expo start --lan',
    'npx expo start --localhost',
    'npm start'
  ];

  let commandIndex = 0;

  function tryNextCommand() {
    if (commandIndex >= backupCommands.length) {
      console.log('❌ 所有启动方式都失败了');
      console.log('');
      console.log('💡 手动解决方案:');
      console.log('1. 重启电脑');
      console.log('2. 重新安装Node.js');
      console.log('3. 重新安装项目依赖: npm install');
      console.log('4. 使用Web版本: npx expo start --web');
      return;
    }

    const command = backupCommands[commandIndex];
    console.log(`🔄 尝试命令: ${command}`);
    
    const child = spawn('cmd', ['/c', command], {
      stdio: ['pipe', 'pipe', 'pipe'],
      cwd: process.cwd()
    });

    let hasOutput = false;

    child.stdout.on('data', (data) => {
      hasOutput = true;
      console.log('📤 输出:', data.toString());
    });

    child.stderr.on('data', (data) => {
      console.log('🚨 错误:', data.toString());
    });

    child.on('close', (code) => {
      if (code !== 0 || !hasOutput) {
        commandIndex++;
        setTimeout(tryNextCommand, 2000);
      }
    });

    setTimeout(() => {
      if (!hasOutput) {
        child.kill();
        commandIndex++;
        tryNextCommand();
      }
    }, 15000);
  }

  tryNextCommand();
}

// 主函数
async function main() {
  const diagnosisResult = await diagnose();
  
  if (!diagnosisResult) {
    console.log('❌ 诊断失败，请手动修复环境问题');
    return;
  }
  
  console.log('✅ 诊断完成，开始启动EXPO服务器...');
  console.log('');
  
  startExpoServer();
}

// 启动诊断
main().catch(console.error);
