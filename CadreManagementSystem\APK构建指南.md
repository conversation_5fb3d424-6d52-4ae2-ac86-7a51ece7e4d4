# 智慧干部管理系统 - APK构建指南

## 🎉 修复完成状态

✅ **延迟退休计算逻辑已修复**
- 李财茂(1965年): 延迟3个月 ✅
- 武海珠(1966年): 延迟5个月 ✅  
- 王四顿(1964年): 无延迟 ✅

✅ **Android构建环境已配置**
- Gradle国内镜像源已配置
- 构建优化参数已设置

## 📋 构建前准备

### 1. 安装Node.js (必需)
```bash
# 访问官网下载
https://nodejs.org/zh-cn/download

# 下载Windows Installer (.msi)
# 安装时选择默认设置
# 重启PowerShell
```

### 2. 运行环境配置脚本
```powershell
# 在项目目录运行
powershell -ExecutionPolicy Bypass -File setup-environment.ps1
```

### 3. 验证环境
```bash
node --version    # 应显示版本号
npm --version     # 应显示版本号
```

## 🚀 APK构建步骤

### 方法一：使用Expo构建 (推荐)

1. **安装依赖**
```bash
npm install
```

2. **安装Expo CLI**
```bash
npm install -g @expo/cli
```

3. **构建APK**
```bash
# 开发版本APK
npx expo build:android

# 生产版本APK
npx expo build:android --type apk

# 应用包(AAB)格式
npx expo build:android --type app-bundle
```

### 方法二：使用EAS构建

1. **安装EAS CLI**
```bash
npm install -g eas-cli
```

2. **登录Expo账户**
```bash
eas login
```

3. **配置构建**
```bash
eas build:configure
```

4. **执行构建**
```bash
# 构建APK
eas build --platform android --profile preview

# 构建生产版本
eas build --platform android --profile production
```

### 方法三：本地构建 (需要Android Studio)

1. **确保Android Studio已安装**
2. **配置环境变量**
```bash
# 设置ANDROID_HOME
# 例如: C:\Users\<USER>\AppData\Local\Android\Sdk
```

3. **生成Android项目**
```bash
npx expo run:android
```

4. **使用Gradle构建**
```bash
cd android
./gradlew assembleRelease
```

## 📱 构建配置说明

### app.json配置
```json
{
  "expo": {
    "name": "CadreManagementSystem",
    "android": {
      "package": "com.sky082.CadreManagementSystem",
      "versionCode": 1
    }
  }
}
```

### 构建优化
- ✅ 已配置阿里云镜像源加速下载
- ✅ 已启用Gradle并行构建
- ✅ 已优化内存设置
- ✅ 已启用Hermes引擎

## 🔧 常见问题解决

### 1. Node.js未安装
```bash
# 错误: 'node' 不是内部或外部命令
# 解决: 安装Node.js并重启终端
```

### 2. 网络连接问题
```bash
# 如果国内镜像源仍然慢，可以使用VPN
# 或者尝试其他镜像源
npm config set registry https://registry.npm.taobao.org
```

### 3. Android SDK问题
```bash
# 确保安装Android Studio
# 配置ANDROID_HOME环境变量
# 安装必要的SDK版本
```

### 4. 内存不足
```bash
# 增加Node.js内存限制
export NODE_OPTIONS="--max-old-space-size=4096"
```

## 📦 构建产物

构建完成后，APK文件位置：
- **Expo构建**: 下载链接会在构建完成后提供
- **本地构建**: `android/app/build/outputs/apk/release/`
- **EAS构建**: 在Expo网站下载

## 🎯 下一步操作

1. **立即执行**:
```bash
# 1. 安装Node.js (如果未安装)
# 2. 运行环境配置脚本
powershell -ExecutionPolicy Bypass -File setup-environment.ps1

# 3. 构建APK
npm install
npx expo build:android
```

2. **测试APK**:
- 在Android设备上安装APK
- 测试所有核心功能
- 验证延迟退休计算是否正确

3. **部署使用**:
- 分发APK给最终用户
- 提供使用说明文档

## 📞 技术支持

如果在构建过程中遇到问题：
1. 检查Node.js和npm版本
2. 确认网络连接正常
3. 查看构建日志中的错误信息
4. 尝试清理缓存: `npm cache clean --force`

---

**构建状态**: ✅ 准备就绪  
**预计构建时间**: 10-30分钟  
**推荐构建方式**: Expo构建 (最简单)  

祝您构建成功！🎉
