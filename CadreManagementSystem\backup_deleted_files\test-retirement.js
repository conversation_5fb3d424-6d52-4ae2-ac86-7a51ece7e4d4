/**
 * 测试退休计算逻辑
 */

// 模拟王四顿和许国泰的数据
const testCadres = [
  {
    id: 1,
    姓名: '王四顿',
    性别: '男',
    出生日期: '1963-05-15', // 假设已经达到退休年龄
    现职级: '处级',
    职务: '处长',
    单位: '测试单位'
  },
  {
    id: 2,
    姓名: '许国泰',
    性别: '男',
    出生日期: '1968-03-20', // 假设接近退居二线年龄
    现职级: '科级',
    职务: '科长',
    单位: '测试单位'
  },
  {
    id: 3,
    姓名: '测试女干部',
    性别: '女',
    出生日期: '1970-08-10',
    现职级: '处级',
    职务: '副处长',
    单位: '测试单位'
  },
  {
    id: 4,
    姓名: '测试女工人',
    性别: '女',
    出生日期: '1975-12-25',
    现职级: '工人',
    职务: '操作员',
    单位: '测试单位'
  }
];

// 延迟退休配置表
const DELAY_CONFIGS = [
  {
    birthYearStart: 0,
    birthYearEnd: 1965,
    delayMonthsPerYear: 0,  // 不延迟
    maxDelayYears: 0
  },
  {
    birthYearStart: 1966,
    birthYearEnd: 1970,
    delayMonthsPerYear: 2,  // 每年延迟2个月
    maxDelayYears: 1
  },
  {
    birthYearStart: 1971,
    birthYearEnd: 1980,
    delayMonthsPerYear: 3,  // 每年延迟3个月
    maxDelayYears: 3
  },
  {
    birthYearStart: 1981,
    birthYearEnd: 9999,
    delayMonthsPerYear: 4,  // 每年延迟4个月
    maxDelayYears: 5
  }
];

// 原法定退休年龄
const ORIGINAL_RETIREMENT_AGES = {
  male: 60,           // 男性干部/职工
  female_cadre: 55,   // 女干部/技术岗
  female_worker: 50   // 女工人/基层岗
};

// 解析出生日期
function parseBirthDate(birthDateStr) {
  if (!birthDateStr) return null;
  
  // 支持多种日期格式
  const formats = [
    /^(\d{4})-(\d{1,2})-(\d{1,2})$/,     // 1975-08-15
    /^(\d{4})\/(\d{1,2})\/(\d{1,2})$/,   // 1975/08/15
    /^(\d{4})\.(\d{1,2})\.(\d{1,2})$/,   // 1975.08.15
    /^(\d{4})年(\d{1,2})月(\d{1,2})日$/   // 1975年08月15日
  ];
  
  for (const format of formats) {
    const match = birthDateStr.match(format);
    if (match) {
      const year = parseInt(match[1]);
      const month = parseInt(match[2]) - 1; // JavaScript月份从0开始
      const day = parseInt(match[3]);
      
      const date = new Date(year, month, day);
      if (!isNaN(date.getTime())) {
        return date;
      }
    }
  }
  
  return null;
}

// 确定女性干部类型
function getFemaleType(cadre) {
  const position = cadre.现职级 || cadre.职务 || '';
  
  // 根据职级判断是否为干部
  const cadreKeywords = ['处级', '科级', '主任', '副主任', '书记', '副书记', '长', '副', '主管', '经理', '总监'];
  const workerKeywords = ['工人', '操作员', '技工', '普工', '班组'];
  
  // 优先检查工人关键词
  if (workerKeywords.some(keyword => position.includes(keyword))) {
    return 'worker';
  }
  
  // 检查干部关键词
  if (cadreKeywords.some(keyword => position.includes(keyword))) {
    return 'cadre';
  }
  
  // 默认按干部处理
  return 'cadre';
}

// 获取延迟退休配置
function getDelayConfig(birthYear) {
  for (const config of DELAY_CONFIGS) {
    if (birthYear >= config.birthYearStart && birthYear <= config.birthYearEnd) {
      return config;
    }
  }
  return DELAY_CONFIGS[0]; // 默认不延迟
}

// 计算退休信息
function calculateRetirement(cadre) {
  const birthDate = parseBirthDate(cadre.出生日期 || '');
  if (!birthDate) {
    return null;
  }

  const birthYear = birthDate.getFullYear();
  const gender = cadre.性别?.toLowerCase();
  
  // 确定原法定退休年龄
  let originalRetirementAge;
  if (gender === '男' || gender === 'male') {
    originalRetirementAge = ORIGINAL_RETIREMENT_AGES.male;
  } else if (gender === '女' || gender === 'female') {
    const femaleType = getFemaleType(cadre);
    originalRetirementAge = femaleType === 'cadre' 
      ? ORIGINAL_RETIREMENT_AGES.female_cadre 
      : ORIGINAL_RETIREMENT_AGES.female_worker;
  } else {
    // 默认按男性处理
    originalRetirementAge = ORIGINAL_RETIREMENT_AGES.male;
  }

  // 计算原退休日期
  const originalRetirementDate = new Date(birthDate);
  originalRetirementDate.setFullYear(birthDate.getFullYear() + originalRetirementAge);

  // 获取延迟配置
  const delayConfig = getDelayConfig(birthYear);
  
  // 计算延迟月数
  const baseYear = 1965; // 基准年份
  const delayYearsFromBase = Math.max(0, birthYear - baseYear);
  const totalDelayMonths = Math.min(
    delayYearsFromBase * delayConfig.delayMonthsPerYear,
    delayConfig.maxDelayYears * 12
  );

  // 计算实际退休日期
  const actualRetirementDate = new Date(originalRetirementDate);
  actualRetirementDate.setMonth(actualRetirementDate.getMonth() + totalDelayMonths);

  // 计算实际退休年龄
  const actualRetirementAge = originalRetirementAge + (totalDelayMonths / 12);

  // 计算距离退休天数
  const today = new Date();
  const daysUntilRetirement = Math.ceil(
    (actualRetirementDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)
  );

  // 生成说明
  let description = `原退休年龄${originalRetirementAge}岁`;
  if (totalDelayMonths > 0) {
    const delayYears = Math.floor(totalDelayMonths / 12);
    const delayMonths = totalDelayMonths % 12;
    let delayStr = '';
    if (delayYears > 0) delayStr += `${delayYears}年`;
    if (delayMonths > 0) delayStr += `${delayMonths}个月`;
    description += `，延迟${delayStr}，实际退休年龄${actualRetirementAge.toFixed(1)}岁`;
  }

  return {
    originalRetirementAge,
    originalRetirementDate,
    delayMonths: totalDelayMonths,
    delayYears: totalDelayMonths / 12,
    actualRetirementAge,
    actualRetirementDate,
    daysUntilRetirement,
    description
  };
}

// 生成退休预警信息
function generateWarning(cadre) {
  const calculation = calculateRetirement(cadre);
  if (!calculation) {
    return null;
  }

  const { daysUntilRetirement, actualRetirementDate } = calculation;
  
  // 已退休
  if (daysUntilRetirement <= 0) {
    return {
      cadre,
      warningType: 'retired',
      urgencyLevel: 'info',
      description: `已于${actualRetirementDate.toLocaleDateString()}退休`,
      daysUntilRetirement: 0,
      calculation
    };
  }

  // 退居二线预警（距离退休2-5年）
  if (daysUntilRetirement <= 365 * 5 && daysUntilRetirement > 365 * 2) {
    return {
      cadre,
      warningType: 'second_line_warning',
      urgencyLevel: 'normal',
      description: `距离退休${Math.ceil(daysUntilRetirement / 365)}年，建议考虑退居二线安排`,
      daysUntilRetirement,
      calculation
    };
  }

  // 近两年退休预警
  if (daysUntilRetirement <= 365 * 2 && daysUntilRetirement > 90) {
    return {
      cadre,
      warningType: 'two_year_warning',
      urgencyLevel: 'normal',
      description: `距离退休${Math.ceil(daysUntilRetirement / 365)}年，请提前做好退休准备`,
      daysUntilRetirement,
      calculation
    };
  }

  // 月度预警（3个月内退休）
  if (daysUntilRetirement <= 90) {
    return {
      cadre,
      warningType: 'monthly_warning',
      urgencyLevel: 'urgent',
      description: `距离退休仅剩${daysUntilRetirement}天，请立即办理退休手续`,
      daysUntilRetirement,
      calculation
    };
  }

  return null;
}

// 运行测试
console.log('=== 退休计算逻辑测试 ===');
console.log(`当前日期: ${new Date().toLocaleDateString()}`);

testCadres.forEach((cadre, index) => {
  console.log(`\n--- 测试 ${index + 1}: ${cadre.姓名} ---`);
  console.log(`性别: ${cadre.性别}`);
  console.log(`出生日期: ${cadre.出生日期}`);
  console.log(`现职级: ${cadre.现职级}`);
  
  const calculation = calculateRetirement(cadre);
  if (calculation) {
    console.log(`✅ 计算成功:`);
    console.log(`  原退休年龄: ${calculation.originalRetirementAge}岁`);
    console.log(`  延迟月数: ${calculation.delayMonths}个月`);
    console.log(`  实际退休年龄: ${calculation.actualRetirementAge.toFixed(1)}岁`);
    console.log(`  实际退休日期: ${calculation.actualRetirementDate.toLocaleDateString()}`);
    console.log(`  距离退休: ${calculation.daysUntilRetirement}天`);
    console.log(`  说明: ${calculation.description}`);
    
    const warning = generateWarning(cadre);
    if (warning) {
      console.log(`⚠️ 预警信息:`);
      console.log(`  类型: ${warning.warningType}`);
      console.log(`  紧急程度: ${warning.urgencyLevel}`);
      console.log(`  描述: ${warning.description}`);
    } else {
      console.log(`✅ 无需预警（距离退休超过5年）`);
    }
  } else {
    console.log(`❌ 计算失败`);
  }
});

console.log('\n=== 测试完成 ===');
