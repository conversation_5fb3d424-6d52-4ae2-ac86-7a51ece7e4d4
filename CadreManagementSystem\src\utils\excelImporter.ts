import * as XLSX from 'xlsx';
import { Platform } from 'react-native';

// Web环境下的文件处理模拟
const WebDocumentPicker = {
  getDocumentAsync: async (options: any) => {
    console.log('Web环境：模拟文档选择');
    return {
      canceled: false,
      assets: [{ uri: 'mock://excel-file.xlsx', name: 'sample.xlsx' }]
    };
  }
};

const WebFileSystem = {
  EncodingType: { Base64: 'base64' },
  documentDirectory: '/mock/',
  readAsStringAsync: async (uri: string, options: any) => {
    console.log('Web环境：模拟文件读取');
    return 'mock-base64-data';
  },
  writeAsStringAsync: async (uri: string, content: string, options: any) => {
    console.log('Web环境：模拟文件写入');
  }
};

// 跨平台适配器
let DocumentPicker: any;
let FileSystem: any;

if (Platform.OS === 'web') {
  DocumentPicker = WebDocumentPicker;
  FileSystem = WebFileSystem;
} else {
  DocumentPicker = require('expo-document-picker');
  FileSystem = require('expo-file-system');
}
import { CadreInfo, ImportResult } from '../types';
import { IdCardValidator } from './idCardValidator';
import { CadreDao } from '../database/cadreDao';

/**
 * Excel导入工具类
 */
export class ExcelImporter {
  
  // Excel字段映射
  private static readonly FIELD_MAPPING: Record<string, keyof CadreInfo> = {
    '序号': '序号',
    '单位': '单位',
    '姓名': '姓名',
    '职务': '职务',
    '性别': '性别',
    '民族': '民族',
    '籍贯': '籍贯',
    '出生日期': '出生日期',
    '参加工作时间': '参加工作时间',
    '政治面貌': '政治面貌',
    '入党时间': '入党时间',
    '全日制教育': '全日制教育',
    '毕业院校系及专业': '毕业院校系及专业',
    '在职教育': '在职教育',
    '毕业院校系及专业（在职）': '毕业院校系及专业_在职',
    '专业技术职务': '专业技术职务',
    '现职级': '现职级',
    '任现职时间': '任现职时间',
    '任现职级时间': '任现职级时间',
    '工作简历': '工作简历',
    '身份证号': '身份证号',
    '身份证号码校验正误': '身份证号码校验正误',
    '联系方式': '联系方式',
    '获得奖励荣誉情况': '获得奖励荣誉情况',
    '党纪政纪处分情况': '党纪政纪处分情况',
    '备注': '备注'
  };

  /**
   * 选择并导入Excel文件
   * @returns 导入结果
   */
  static async selectAndImportExcel(): Promise<ImportResult> {
    try {
      // 选择文件
      const result = await DocumentPicker.getDocumentAsync({
        type: [
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'application/vnd.ms-excel'
        ],
        copyToCacheDirectory: true
      });

      if (result.canceled) {
        return {
          success: false,
          totalRows: 0,
          successRows: 0,
          failedRows: 0,
          errors: [{ row: 0, field: 'file', message: '用户取消了文件选择' }]
        };
      }

      const fileUri = result.assets[0].uri;
      return await this.importFromFile(fileUri);
    } catch (error) {
      console.error('选择文件失败:', error);
      return {
        success: false,
        totalRows: 0,
        successRows: 0,
        failedRows: 0,
        errors: [{ row: 0, field: 'file', message: '文件选择失败: ' + error }]
      };
    }
  }

  /**
   * 从文件导入数据
   * @param fileUri 文件URI
   * @returns 导入结果
   */
  static async importFromFile(fileUri: string): Promise<ImportResult> {
    try {
      console.log('开始导入文件:', fileUri);
      let fileContent: string;

      if (Platform.OS === 'web') {
        // Web环境下创建示例数据
        console.log('Web环境：创建示例Excel数据进行导入测试');
        fileContent = this.createSampleExcelData();
      } else {
        try {
          // 移动端：读取真实Excel文件内容
          console.log('📱 移动端：开始读取Excel文件...');
          fileContent = await FileSystem.readAsStringAsync(fileUri, {
            encoding: FileSystem.EncodingType.Base64
          });
          console.log('✅ Excel文件读取成功，内容长度:', fileContent.length);
        } catch (readError) {
          console.error('❌ Excel文件读取失败:', readError);
          throw new Error(`文件读取失败: ${readError}`);
        }
      }

      // 解析Excel文件
      console.log('开始解析Excel文件...');
      const workbook = XLSX.read(fileContent, { type: 'base64' });
      console.log('Excel解析成功，工作表数量:', workbook.SheetNames.length);

      if (workbook.SheetNames.length === 0) {
        throw new Error('Excel文件中没有找到工作表');
      }

      const sheetName = workbook.SheetNames[0];
      console.log('使用工作表:', sheetName);
      const worksheet = workbook.Sheets[sheetName];

      // 转换为JSON数据
      console.log('转换为JSON数据...');
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
      console.log('数据转换成功，行数:', jsonData.length);

      return await this.processExcelData(jsonData as any[][]);
    } catch (error) {
      console.error('导入文件失败:', error);
      return {
        success: false,
        totalRows: 0,
        successRows: 0,
        failedRows: 0,
        errors: [{ row: 0, field: 'file', message: `文件解析失败: ${error}` }]
      };
    }
  }

  /**
   * 创建示例Excel数据（Web环境使用）
   */
  private static createSampleExcelData(): string {
    const sampleData = [
      ['姓名', '性别', '出生日期', '身份证号', '民族', '政治面貌', '学历', '职务', '现职级', '入职日期', '联系电话', '家庭住址', '备注'],
      // 已退休人员（61岁，已达到退休年龄）
      ['王四顿', '男', '1964-03-15', '110101196403151234', '汉族', '中共党员', '本科', '处长', '处级', '1985-08-01', '13800138001', '北京市朝阳区xxx街道', '已达到退休年龄'],
      // 退居二线预警人员（55岁，接近退居二线）
      ['许国泰', '男', '1969-08-20', '110101196908201234', '汉族', '中共党员', '硕士', '科长', '科级', '1990-06-15', '13800138002', '北京市海淀区xxx路', '接近退居二线年龄'],
      // 近两年退休预警人员
      ['李明华', '女', '1970-08-10', '110101197008101234', '汉族', '中共党员', '本科', '副处长', '处级', '1992-03-10', '13800138003', '北京市西城区xxx胡同', '女干部接近退休'],
      // 月度预警人员（3个月内退休）
      ['张建国', '男', '1965-04-01', '110101196504011234', '汉族', '中共党员', '大专', '主任', '处级', '1988-07-01', '13800138004', '北京市东城区xxx大街', '即将退休'],
      // 女工人退休预警
      ['刘秀英', '女', '1975-12-25', '110101197512251234', '汉族', '群众', '高中', '操作员', '工人', '1995-09-01', '13800138005', '北京市丰台区xxx小区', '女工人接近退休'],
      // 正常年龄人员
      ['赵六', '女', '1990-07-25', '110101199007251234', '满族', '共青团员', '本科', '科员', '科员', '2015-09-01', '13800138006', '北京市东城区xxx大街', '新入职'],
      ['钱七', '男', '1978-11-30', '110101197811301234', '汉族', '中共党员', '硕士', '副主任', '副处级', '2003-07-20', '13800138007', '北京市丰台区xxx小区', '业务骨干'],
    ];

    const worksheet = XLSX.utils.aoa_to_sheet(sampleData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, '干部信息');

    return XLSX.write(workbook, { type: 'base64', bookType: 'xlsx' });
  }

  /**
   * 处理Excel数据
   * @param data Excel数据
   * @returns 导入结果
   */
  private static async processExcelData(data: any[][]): Promise<ImportResult> {
    console.log('开始处理Excel数据，总行数:', data.length);

    if (data.length < 1) {
      return {
        success: false,
        totalRows: 0,
        successRows: 0,
        failedRows: 0,
        errors: [{ row: 0, field: 'data', message: 'Excel文件为空' }]
      };
    }

    if (data.length < 2) {
      return {
        success: false,
        totalRows: 0,
        successRows: 0,
        failedRows: 0,
        errors: [{ row: 0, field: 'data', message: 'Excel文件数据不足，至少需要表头和一行数据' }]
      };
    }

    const headers = data[0] as string[];
    const rows = data.slice(1);

    console.log('表头:', headers);
    console.log('数据行数:', rows.length);

    // 简化表头验证，只要有姓名字段就可以
    if (!headers.some(h => h && (h.includes('姓名') || h.includes('name')))) {
      console.log('未找到姓名字段，尝试使用第一列作为姓名');
      // 如果没有找到姓名字段，假设第一列是姓名
      if (headers.length > 0) {
        headers[0] = '姓名';
      }
    }

    const result: ImportResult = {
      success: true,
      totalRows: rows.length,
      successRows: 0,
      failedRows: 0,
      errors: []
    };

    const validCadres: CadreInfo[] = [];

    // 处理每一行数据
    for (let i = 0; i < rows.length; i++) {
      const rowIndex = i + 2; // Excel行号（从1开始，加上表头行）
      const row = rows[i];
      
      try {
        const cadre = this.parseRowToCadre(headers, row, rowIndex);
        const validation = this.validateCadreData(cadre, rowIndex);
        
        if (validation.isValid) {
          validCadres.push(cadre);
          result.successRows++;
        } else {
          result.errors.push(...validation.errors);
          result.failedRows++;
        }
      } catch (error) {
        result.errors.push({
          row: rowIndex,
          field: 'general',
          message: '行数据解析失败: ' + error
        });
        result.failedRows++;
      }
    }

    // 批量插入有效数据
    if (validCadres.length > 0) {
      try {
        console.log(`📊 Excel导入：解析完成，有效数据${validCadres.length}条`);

        // 显示前几条数据的关键信息
        validCadres.slice(0, 5).forEach((cadre, index) => {
          console.log(`📋 数据${index + 1}：${cadre.姓名} (性别:${cadre.性别}, 出生日期:${cadre.出生日期})`);
        });

        console.log(`💾 开始批量插入 ${validCadres.length} 条有效数据到数据库...`);
        const dbResult = await CadreDao.batchInsertCadres(validCadres);

        console.log('✅ 数据库插入结果:', dbResult);

        // 更新结果统计
        result.successRows = dbResult.success;
        result.failedRows += dbResult.failed;

        // 添加数据库错误到结果中
        if (dbResult.errors.length > 0) {
          dbResult.errors.forEach(error => {
            result.errors.push({
              row: 0,
              field: 'database',
              message: error
            });
          });
        }

      } catch (error) {
        console.error('数据库插入失败:', error);
        result.success = false;
        result.errors.push({
          row: 0,
          field: 'database',
          message: '数据库插入失败: ' + error
        });
      }
    }

    // 只要有成功插入的数据就认为导入成功
    result.success = result.successRows > 0;

    console.log('最终导入结果:', {
      totalRows: result.totalRows,
      successRows: result.successRows,
      failedRows: result.failedRows,
      success: result.success,
      errorCount: result.errors.length
    });

    return result;
  }

  /**
   * 验证表头
   * @param headers 表头数组
   * @returns 验证结果
   */
  private static validateHeaders(headers: string[]) {
    const errors: Array<{ row: number; field: string; message: string }> = [];
    const requiredFields = ['姓名'];
    
    // 检查必需字段
    for (const field of requiredFields) {
      if (!headers.includes(field)) {
        errors.push({
          row: 1,
          field: 'header',
          message: `缺少必需字段: ${field}`
        });
      }
    }

    // 检查未知字段
    const unknownFields = headers.filter(header => 
      header && !this.FIELD_MAPPING[header]
    );
    
    if (unknownFields.length > 0) {
      errors.push({
        row: 1,
        field: 'header',
        message: `包含未知字段: ${unknownFields.join(', ')}`
      });
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 将行数据转换为干部信息对象
   * @param headers 表头
   * @param row 行数据
   * @param rowIndex 行索引
   * @returns 干部信息对象
   */
  private static parseRowToCadre(headers: string[], row: any[], rowIndex: number): CadreInfo {
    const cadre: CadreInfo = { 姓名: '' };

    console.log(`解析第${rowIndex}行数据:`, row.slice(0, 5)); // 只显示前5列避免日志过长

    for (let i = 0; i < headers.length && i < row.length; i++) {
      try {
        const header = headers[i]?.toString().trim();
        const value = row[i];

        if (!header || value === undefined || value === null || value === '') continue;

        // 查找对应的字段名
        let fieldName = this.FIELD_MAPPING[header];

        // 如果找不到精确匹配，尝试模糊匹配
        if (!fieldName) {
          fieldName = this.findBestMatchField(header);
        }

        if (fieldName) {
          // 特殊处理日期字段
          if (header.includes('时间') || header.includes('日期')) {
            cadre[fieldName] = this.formatDate(value);
          } else {
            cadre[fieldName] = String(value).trim();
          }
        }
      } catch (error) {
        console.warn(`解析第${rowIndex}行第${i}列失败:`, error);
        // 继续处理其他字段
      }
    }

    // 自动验证身份证号码
    if (cadre.身份证号) {
      const validation = IdCardValidator.validate(cadre.身份证号);
      cadre.身份证号码校验正误 = validation.isValid ? '正确' : '错误';
      
      // 自动填充性别和出生日期（如果为空）
      if (validation.isValid) {
        if (!cadre.性别 && validation.gender) {
          cadre.性别 = validation.gender;
        }
        if (!cadre.出生日期 && validation.birthDate) {
          cadre.出生日期 = validation.birthDate;
        }
      }
    }

    return cadre;
  }

  /**
   * 模糊匹配字段名
   * @param header 表头
   * @returns 匹配的字段名
   */
  private static findBestMatchField(header: string): keyof CadreInfo | null {
    const headerLower = header.toLowerCase();

    // 常见的字段别名映射
    const aliasMapping: Record<string, keyof CadreInfo> = {
      'name': '姓名',
      '名字': '姓名',
      'gender': '性别',
      '性': '性别',
      'birth': '出生日期',
      '生日': '出生日期',
      'id': '身份证号',
      '身份证': '身份证号',
      'phone': '联系方式',
      '电话': '联系方式',
      '手机': '联系方式',
      'position': '职务',
      '职位': '职务',
      'department': '单位',
      '部门': '单位',
      'education': '全日制教育',
      '学历': '全日制教育',
      'party': '政治面貌',
      '政治': '政治面貌'
    };

    // 精确匹配
    if (aliasMapping[headerLower]) {
      return aliasMapping[headerLower];
    }

    // 包含匹配
    for (const [alias, field] of Object.entries(aliasMapping)) {
      if (headerLower.includes(alias) || alias.includes(headerLower)) {
        return field;
      }
    }

    return null;
  }

  /**
   * 验证干部数据
   * @param cadre 干部信息
   * @param rowIndex 行索引
   * @returns 验证结果
   */
  private static validateCadreData(cadre: CadreInfo, rowIndex: number) {
    const errors: Array<{ row: number; field: string; message: string }> = [];

    // 验证必需字段 - 只验证姓名
    if (!cadre.姓名 || cadre.姓名.trim() === '') {
      errors.push({
        row: rowIndex,
        field: '姓名',
        message: '姓名不能为空'
      });
    }

    // 身份证号码验证（如果有的话）- 不强制要求
    if (cadre.身份证号 && cadre.身份证号.trim() !== '') {
      try {
        const validation = IdCardValidator.validate(cadre.身份证号);
        if (!validation.isValid) {
          // 只是警告，不阻止导入
          console.warn(`第${rowIndex}行身份证号码格式不正确:`, validation.errors);
        }
      } catch (error) {
        console.warn(`第${rowIndex}行身份证号码验证失败:`, error);
      }
    }

    // 性别验证（如果有的话）- 不强制要求
    if (cadre.性别 && cadre.性别.trim() !== '' && !['男', '女', 'M', 'F', 'male', 'female'].includes(cadre.性别.toLowerCase())) {
      // 尝试转换
      if (cadre.性别.toLowerCase().includes('男') || cadre.性别.toLowerCase().includes('m')) {
        cadre.性别 = '男';
      } else if (cadre.性别.toLowerCase().includes('女') || cadre.性别.toLowerCase().includes('f')) {
        cadre.性别 = '女';
      } else {
        console.warn(`第${rowIndex}行性别格式不正确: ${cadre.性别}`);
      }
    }

    // 只要有姓名就认为是有效的
    return {
      isValid: cadre.姓名 && cadre.姓名.trim() !== '',
      errors
    };
  }

  /**
   * 格式化日期
   * @param value 日期值
   * @returns 格式化后的日期字符串
   */
  private static formatDate(value: any): string {
    if (!value) return '';

    // 如果是Excel日期序列号
    if (typeof value === 'number') {
      const date = XLSX.SSF.parse_date_code(value);
      return `${date.y}-${String(date.m).padStart(2, '0')}-${String(date.d).padStart(2, '0')}`;
    }

    // 如果是字符串，尝试解析
    if (typeof value === 'string') {
      const dateStr = value.trim();
      
      // 尝试不同的日期格式
      const formats = [
        /^(\d{4})-(\d{1,2})-(\d{1,2})$/,
        /^(\d{4})\/(\d{1,2})\/(\d{1,2})$/,
        /^(\d{4})\.(\d{1,2})\.(\d{1,2})$/,
        /^(\d{4})年(\d{1,2})月(\d{1,2})日$/
      ];

      for (const format of formats) {
        const match = dateStr.match(format);
        if (match) {
          const year = match[1];
          const month = String(match[2]).padStart(2, '0');
          const day = String(match[3]).padStart(2, '0');
          return `${year}-${month}-${day}`;
        }
      }
    }

    return String(value);
  }

  /**
   * 验证日期格式
   * @param dateStr 日期字符串
   * @returns 是否有效
   */
  private static isValidDate(dateStr: string): boolean {
    if (!dateStr) return true; // 空值认为有效
    
    const regex = /^\d{4}-\d{2}-\d{2}$/;
    if (!regex.test(dateStr)) return false;

    const date = new Date(dateStr);
    return date instanceof Date && !isNaN(date.getTime());
  }

  /**
   * 导出模板Excel文件
   * @returns 模板文件URI
   */
  static async exportTemplate(): Promise<string> {
    const headers = Object.keys(this.FIELD_MAPPING);
    const templateData = [headers];

    // 添加示例数据
    const exampleRow = [
      '1', '示例单位', '张三', '部门经理', '男', '汉族', '山西太原',
      '1980-01-01', '2000-07-01', '中共党员', '2005-01-01',
      '本科', '某大学计算机系', '硕士', '某大学MBA',
      '工程师', '中层正职', '2020-01-01', '2020-01-01',
      '2000年参加工作，历任...', '140000198001010001', '正确',
      '13800000000', '优秀员工', '无', '备注信息'
    ];
    templateData.push(exampleRow);

    const worksheet = XLSX.utils.aoa_to_sheet(templateData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, '干部信息模板');

    const wbout = XLSX.write(workbook, { type: 'base64', bookType: 'xlsx' });
    
    const fileUri = FileSystem.documentDirectory + 'cadre_template.xlsx';
    await FileSystem.writeAsStringAsync(fileUri, wbout, {
      encoding: FileSystem.EncodingType.Base64
    });

    return fileUri;
  }
}
