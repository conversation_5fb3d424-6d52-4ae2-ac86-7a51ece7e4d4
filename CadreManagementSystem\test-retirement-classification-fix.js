console.log('🔧 退休分类修复验证...\n');

// 测试数据
const testCadres = [
  {
    姓名: '李财茂',
    出生日期: '1965/06/03',
    性别: '男',
    现职级: '正处',
    单位: '晋圣公司提副总经理综合办公室副主任'
  },
  {
    姓名: '毕海宇',
    出生日期: '1965/08/17',
    性别: '男',
    现职级: '副处',
    单位: '晋圣公司'
  },
  {
    姓名: '申丽丽',
    出生日期: '1969/03/15',
    性别: '女',
    现职级: '科员',
    单位: '晋圣公司'
  }
];

// 延迟退休计算函数
function calculateDelayRetirement(birthYear) {
  let totalDelayMonths = 3; // 基础延迟3个月
  
  if (birthYear >= 1966 && birthYear <= 1970) {
    const extraYears = birthYear - 1965;
    const extraMonths = Math.min(extraYears * 2, 12); // 每年2个月，最多1年
    totalDelayMonths += extraMonths;
  } else if (birthYear >= 1971 && birthYear <= 1980) {
    totalDelayMonths += 12; // 先加满1年
    const extraYears = birthYear - 1970;
    const extraMonths = Math.min(extraYears * 3, 24); // 每年3个月，最多2年
    totalDelayMonths += extraMonths;
  } else if (birthYear >= 1981) {
    totalDelayMonths += 36; // 先加满3年
    const extraYears = birthYear - 1980;
    const extraMonths = Math.min(extraYears * 4, 24); // 每年4个月，最多2年
    totalDelayMonths += extraMonths;
  }
  
  const maxDelayMonths = 60; // 最多延迟5年
  totalDelayMonths = Math.min(totalDelayMonths, maxDelayMonths);
  
  return {
    totalDelayMonths,
    delayYears: Math.floor(totalDelayMonths / 12),
    delayMonths: totalDelayMonths % 12
  };
}

// 计算距离实际退休的天数
function calculateDaysUntilRetirement(birthDate, gender) {
  const baseRetirementAge = gender === '女' ? 55 : 60;
  const birthYear = birthDate.getFullYear();
  const delayInfo = calculateDelayRetirement(birthYear);
  
  // 计算实际退休日期
  const actualRetirementDate = new Date(birthDate);
  actualRetirementDate.setFullYear(birthDate.getFullYear() + baseRetirementAge);
  actualRetirementDate.setMonth(actualRetirementDate.getMonth() + delayInfo.totalDelayMonths);
  
  const today = new Date();
  const diffTime = actualRetirementDate.getTime() - today.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  return {
    daysUntilRetirement: diffDays,
    actualRetirementDate,
    delayInfo
  };
}

// 计算年龄
function calculateAge(birthDate) {
  const today = new Date();
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  
  return age;
}

// 解析出生日期
function parseBirthDate(dateStr) {
  if (!dateStr) return null;
  
  // 处理各种日期格式
  const cleanStr = dateStr.replace(/[年月日\-\.]/g, '/');
  const date = new Date(cleanStr);
  
  if (isNaN(date.getTime())) {
    return null;
  }
  
  return date;
}

// 判断退休状态
function determineRetirementStatus(cadre) {
  const birthDate = parseBirthDate(cadre.出生日期);
  if (!birthDate) return null;
  
  const age = calculateAge(birthDate);
  const retirementInfo = calculateDaysUntilRetirement(birthDate, cadre.性别);
  const { daysUntilRetirement, delayInfo } = retirementInfo;
  
  console.log(`\n📊 ${cadre.姓名} 分析：`);
  console.log(`   出生日期: ${cadre.出生日期}`);
  console.log(`   当前年龄: ${age}岁`);
  console.log(`   延迟月数: ${delayInfo.totalDelayMonths}个月`);
  console.log(`   距离实际退休: ${daysUntilRetirement}天`);
  
  // 判断退休状态
  if (daysUntilRetirement <= 0) {
    console.log(`   ✅ 状态: 已退休（实际退休日期已过）`);
    return 'retired';
  } else if (daysUntilRetirement <= 730) { // 2年内
    console.log(`   ⚠️ 状态: 近两年退休预警`);
    return 'retirement_warning';
  } else {
    console.log(`   ℹ️ 状态: 距离退休超过2年`);
    return 'normal';
  }
}

console.log('🎯 修复前的问题：');
console.log('- 李财茂等1965年出生人员错误出现在"已退休"列表');
console.log('- 显示"已超过退休年龄X天"但X是正数，逻辑矛盾');
console.log('- 延迟退休计算不准确');

console.log('\n🔧 修复内容：');
console.log('1. matchRetiredRule() 方法：只有daysUntilRetirement <= 0才算已退休');
console.log('2. 显示逻辑：根据actualRetirementDays正负值显示不同文本');
console.log('3. 统一延迟退休计算：1965年基础延迟3个月');

console.log('\n📋 测试结果：');

testCadres.forEach(cadre => {
  const status = determineRetirementStatus(cadre);
});

console.log('\n✅ 预期修复效果：');
console.log('1. 李财茂：从"已退休"移至"近两年退休"列表');
console.log('2. 毕海宇：延迟3个月，距离实际退休138天');
console.log('3. 申丽丽：延迟11个月，已超过法定但未到实际退休');
console.log('4. 显示文本：根据实际情况显示"距离实际退休还剩X天"或"已超过实际退休年龄X天"');

console.log('\n🎉 退休分类修复验证完成！');
