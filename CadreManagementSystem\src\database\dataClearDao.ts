import { getDatabase } from './database';
import { DataClearPassword } from '../types';

export class DataClearDao {
  // 检查是否已设置密码
  static async hasPassword(): Promise<boolean> {
    const db = getDatabase();
    
    try {
      const result = await db.getFirstAsync(`
        SELECT COUNT(*) as count FROM data_clear_passwords
      `);
      
      return (result as any)?.count > 0;
    } catch (error) {
      console.error('检查密码设置失败:', error);
      return false;
    }
  }

  // 简单的哈希函数
  private static simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return hash.toString(16);
  }

  // 生成简单的盐值
  private static generateSalt(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  }

  // 设置密码
  static async setPassword(password: string): Promise<void> {
    const db = getDatabase();

    try {
      // 生成盐值
      const salt = this.generateSalt();

      // 生成密码哈希
      const passwordHash = this.simpleHash(password + salt);

      // 清除旧密码（如果存在）
      await db.runAsync(`DELETE FROM data_clear_passwords`);

      // 插入新密码
      await db.runAsync(`
        INSERT INTO data_clear_passwords (password_hash, salt)
        VALUES (?, ?)
      `, [passwordHash, salt]);

      console.log('数据清空密码设置成功');
    } catch (error) {
      console.error('设置密码失败:', error);
      throw new Error(`设置密码失败: ${error}`);
    }
  }

  // 验证密码
  static async verifyPassword(password: string): Promise<boolean> {
    const db = getDatabase();

    try {
      const result = await db.getFirstAsync(`
        SELECT password_hash, salt FROM data_clear_passwords LIMIT 1
      `) as DataClearPassword;

      if (!result) {
        return false;
      }

      // 计算输入密码的哈希
      const inputHash = this.simpleHash(password + result.salt);

      return inputHash === result.password_hash;
    } catch (error) {
      console.error('验证密码失败:', error);
      return false;
    }
  }

  // 清空所有数据
  static async clearAllData(): Promise<void> {
    const db = getDatabase();
    
    try {
      await db.withTransactionAsync(async () => {
        // 清空干部数据
        await db.runAsync(`DELETE FROM cadres`);
        
        // 重置自增ID
        await db.runAsync(`DELETE FROM sqlite_sequence WHERE name='cadres'`);
        
        console.log('所有干部数据已清空');
      });
    } catch (error) {
      console.error('清空数据失败:', error);
      throw new Error(`清空数据失败: ${error}`);
    }
  }

  // 清空特定表数据
  static async clearTableData(tableName: string): Promise<void> {
    const db = getDatabase();
    
    // 安全检查，只允许清空特定表
    const allowedTables = ['cadres'];
    if (!allowedTables.includes(tableName)) {
      throw new Error('不允许清空该表');
    }
    
    try {
      await db.withTransactionAsync(async () => {
        await db.runAsync(`DELETE FROM ${tableName}`);
        await db.runAsync(`DELETE FROM sqlite_sequence WHERE name='${tableName}'`);
      });
      
      console.log(`表 ${tableName} 数据已清空`);
    } catch (error) {
      console.error(`清空表 ${tableName} 失败:`, error);
      throw new Error(`清空表失败: ${error}`);
    }
  }

  // 获取数据统计（清空前确认）
  static async getDataStatistics(): Promise<{
    cadreCount: number;
    positionLevelCount: number;
    retirementRuleCount: number;
  }> {
    const db = getDatabase();
    
    try {
      const cadreResult = await db.getFirstAsync(`SELECT COUNT(*) as count FROM cadres`);
      const positionResult = await db.getFirstAsync(`SELECT COUNT(*) as count FROM position_levels WHERE is_active = 1`);
      const ruleResult = await db.getFirstAsync(`SELECT COUNT(*) as count FROM retirement_rules WHERE is_active = 1`);
      
      return {
        cadreCount: (cadreResult as any)?.count || 0,
        positionLevelCount: (positionResult as any)?.count || 0,
        retirementRuleCount: (ruleResult as any)?.count || 0,
      };
    } catch (error) {
      console.error('获取数据统计失败:', error);
      return {
        cadreCount: 0,
        positionLevelCount: 0,
        retirementRuleCount: 0,
      };
    }
  }

  // 备份数据到JSON（可选功能）
  static async exportDataToJson(): Promise<string> {
    const db = getDatabase();
    
    try {
      const cadres = await db.getAllAsync(`SELECT * FROM cadres`);
      const positionLevels = await db.getAllAsync(`SELECT * FROM position_levels`);
      const retirementRules = await db.getAllAsync(`SELECT * FROM retirement_rules`);
      const systemSettings = await db.getAllAsync(`SELECT * FROM system_settings`);
      
      const exportData = {
        exportTime: new Date().toISOString(),
        data: {
          cadres,
          positionLevels,
          retirementRules,
          systemSettings
        }
      };
      
      return JSON.stringify(exportData, null, 2);
    } catch (error) {
      console.error('导出数据失败:', error);
      throw new Error(`导出数据失败: ${error}`);
    }
  }

  // 从JSON恢复数据（可选功能）
  static async importDataFromJson(jsonData: string): Promise<void> {
    const db = getDatabase();
    
    try {
      const importData = JSON.parse(jsonData);
      
      await db.withTransactionAsync(async () => {
        // 清空现有数据
        await this.clearAllData();
        
        // 恢复干部数据
        if (importData.data.cadres) {
          for (const cadre of importData.data.cadres) {
            const fields = Object.keys(cadre).filter(key => key !== 'id');
            const placeholders = fields.map(() => '?').join(', ');
            const values = fields.map(key => cadre[key]);
            
            await db.runAsync(`
              INSERT INTO cadres (${fields.join(', ')}) 
              VALUES (${placeholders})
            `, values);
          }
        }
        
        // 恢复其他数据...
      });
      
      console.log('数据恢复成功');
    } catch (error) {
      console.error('恢复数据失败:', error);
      throw new Error(`恢复数据失败: ${error}`);
    }
  }
}
