console.log('🔍 分析申丽丽女性退休预警错误...\n');

// 申丽丽基本信息
const name = '申丽丽';
const birthDate = new Date('1971-01-01');
const gender = '女';

console.log(`👤 申丽丽基本信息：`);
console.log(`姓名: ${name}`);
console.log(`出生日期: ${birthDate.toLocaleDateString()}`);
console.log(`性别: ${gender}`);

// 计算当前年龄
const today = new Date();
const age = today.getFullYear() - birthDate.getFullYear();
const monthDiff = today.getMonth() - birthDate.getMonth();
const dayDiff = today.getDate() - birthDate.getDate();

let exactAge = age;
if (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)) {
  exactAge--;
}

console.log(`\n📅 年龄计算：`);
console.log(`当前年龄: ${exactAge}岁`);
console.log(`详细年龄: ${exactAge}岁${monthDiff >= 0 ? monthDiff : 12 + monthDiff}个月${dayDiff >= 0 ? dayDiff : ''}天`);

// 女性退休年龄应该是55岁
const correctRetirementAge = 55; // 女性55岁退休
const wrongRetirementAge = 60; // 系统错误地使用了60岁

console.log(`\n🎯 退休年龄分析：`);
console.log(`正确的女性退休年龄: ${correctRetirementAge}岁`);
console.log(`系统错误使用的年龄: ${wrongRetirementAge}岁`);

// 计算延迟退休信息（1971年出生）
const birthYear = birthDate.getFullYear();
console.log(`\n🕐 延迟退休计算：`);
console.log(`出生年份: ${birthYear}`);

// 1971年：基础3个月 + 每年3个月
let totalDelayMonths = 0;
const baseDelayMonths = 3;
const yearsAfter1965 = birthYear - 1965;
const additionalDelayMonths = Math.min(yearsAfter1965 * 3, 36 - baseDelayMonths);
totalDelayMonths = baseDelayMonths + additionalDelayMonths;

console.log(`延迟规则: 1971-1980年，基础3个月 + 每年3个月`);
console.log(`超出1965年: ${yearsAfter1965}年`);
console.log(`额外延迟: ${additionalDelayMonths}个月`);
console.log(`总延迟月数: ${totalDelayMonths}个月`);

const delayYears = Math.floor(totalDelayMonths / 12);
const delayMonths = totalDelayMonths % 12;

console.log(`延迟时间: ${delayYears}年${delayMonths}个月`);

// 正确的计算（女性55岁 + 延迟）
const correctActualRetirementAge = correctRetirementAge + (totalDelayMonths / 12);
const correctRetirementDate = new Date(birthDate);
correctRetirementDate.setFullYear(birthDate.getFullYear() + Math.floor(correctActualRetirementAge));
const correctDelayMonthsForDate = Math.round((correctActualRetirementAge - Math.floor(correctActualRetirementAge)) * 12);
correctRetirementDate.setMonth(correctRetirementDate.getMonth() + correctDelayMonthsForDate);

const correctDiffTime = correctRetirementDate.getTime() - today.getTime();
const correctDaysUntilRetirement = Math.ceil(correctDiffTime / (1000 * 60 * 60 * 24));

console.log(`\n✅ 正确的计算结果：`);
console.log(`实际退休年龄: ${correctActualRetirementAge}岁`);
console.log(`退休日期: ${correctRetirementDate.toLocaleDateString()}`);
console.log(`剩余天数: ${correctDaysUntilRetirement}天`);

// 错误的计算（男性60岁 + 延迟）
const wrongActualRetirementAge = wrongRetirementAge + (totalDelayMonths / 12);
const wrongRetirementDate = new Date(birthDate);
wrongRetirementDate.setFullYear(birthDate.getFullYear() + Math.floor(wrongActualRetirementAge));
const wrongDelayMonthsForDate = Math.round((wrongActualRetirementAge - Math.floor(wrongActualRetirementAge)) * 12);
wrongRetirementDate.setMonth(wrongRetirementDate.getMonth() + wrongDelayMonthsForDate);

const wrongDiffTime = wrongRetirementDate.getTime() - today.getTime();
const wrongDaysUntilRetirement = Math.ceil(wrongDiffTime / (1000 * 60 * 60 * 24));

console.log(`\n❌ 错误的计算结果（系统当前显示）：`);
console.log(`实际退休年龄: ${wrongActualRetirementAge}岁`);
console.log(`退休日期: ${wrongRetirementDate.toLocaleDateString()}`);
console.log(`剩余天数: ${wrongDaysUntilRetirement}天`);

console.log(`\n📊 对比分析：`);
console.log(`用户期望剩余天数: 334天`);
console.log(`正确计算剩余天数: ${correctDaysUntilRetirement}天`);
console.log(`系统错误显示天数: ${wrongDaysUntilRetirement}天`);
console.log(`系统显示的天数: 835天`);

console.log(`\n🔍 问题分析：`);
if (Math.abs(correctDaysUntilRetirement - 334) < 30) {
  console.log(`✅ 正确计算结果与用户期望基本一致（差异在合理范围内）`);
} else {
  console.log(`⚠️ 正确计算结果与用户期望有差异，可能需要进一步检查`);
}

if (Math.abs(wrongDaysUntilRetirement - 835) < 30) {
  console.log(`✅ 错误计算结果与系统显示基本一致，确认问题所在`);
} else {
  console.log(`⚠️ 错误计算结果与系统显示有差异，可能还有其他问题`);
}

console.log(`\n🎯 问题根源：`);
console.log(`系统在计算女性退休时间时，错误地使用了男性的60岁退休年龄，`);
console.log(`而不是女性的55岁退休年龄，导致计算结果错误。`);

console.log(`\n🔧 需要修复的地方：`);
console.log(`1. 延迟退休计算时需要根据性别确定基础退休年龄`);
console.log(`2. 女性应该使用55岁作为基础退休年龄`);
console.log(`3. 男性使用60岁作为基础退休年龄`);
console.log(`4. 确保所有相关计算都正确使用性别差异`);

console.log(`\n📋 修复后的预期效果：`);
console.log(`申丽丽（女，1971年1月1日出生）：`);
console.log(`- 基础退休年龄：55岁`);
console.log(`- 延迟时间：${delayYears}年${delayMonths}个月`);
console.log(`- 实际退休年龄：${correctActualRetirementAge}岁`);
console.log(`- 退休日期：${correctRetirementDate.toLocaleDateString()}`);
console.log(`- 剩余天数：约${correctDaysUntilRetirement}天`);

console.log(`\n✅ 申丽丽退休预警错误分析完成！`);
