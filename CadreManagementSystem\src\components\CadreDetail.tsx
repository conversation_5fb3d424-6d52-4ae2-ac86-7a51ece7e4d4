import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Image,
  Modal,
  Dimensions,
  StatusBar
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Platform } from 'react-native';

// Web环境下的图片选择器模拟
const WebImagePicker = {
  MediaTypeOptions: { Images: 'Images' },
  requestMediaLibraryPermissionsAsync: async () => ({ status: 'granted' }),
  requestCameraPermissionsAsync: async () => ({ status: 'granted' }),
  launchImageLibraryAsync: async (options: any) => {
    console.log('Web环境：模拟图片库选择');
    return {
      canceled: false,
      assets: [{ uri: 'https://via.placeholder.com/150' }]
    };
  },
  launchCameraAsync: async (options: any) => {
    console.log('Web环境：模拟相机拍照');
    return {
      canceled: false,
      assets: [{ uri: 'https://via.placeholder.com/150' }]
    };
  }
};

// 跨平台适配器
let ImagePicker: any;
if (Platform.OS === 'web') {
  ImagePicker = WebImagePicker;
} else {
  ImagePicker = require('expo-image-picker');
}
import { CadreInfo, ValidationError } from '../types';
import { CadreDao } from '../database/cadreDao';
import { IdCardValidator } from '../utils/idCardValidator';

const { width, height } = Dimensions.get('window');
const isSmallScreen = width < 375;
const isMediumScreen = width >= 375 && width < 414;
const isLargeScreen = width >= 414;
// import { RetirementCalculator } from '../utils/retirementCalculator';

interface CadreDetailProps {
  cadreId?: number;
  mode: 'view' | 'edit' | 'add';
  onSave: (cadre: CadreInfo) => void;
  onCancel: () => void;
}

export const CadreDetail: React.FC<CadreDetailProps> = ({
  cadreId,
  mode,
  onSave,
  onCancel
}) => {
  const [cadre, setCadre] = useState<CadreInfo>({
    姓名: '',
    退休状态: 'active'
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<ValidationError[]>([]);
  const [showImagePicker, setShowImagePicker] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState<string | null>(null);

  // 移动端优化的字段分类配置 - 在一个页面上滑动查看
  const fieldSections = [
    {
      id: 'basic',
      title: '基本信息',
      icon: 'person-outline',
      color: '#007AFF',
      bgColor: '#F0F8FF',
      fields: [
        { key: '序号', label: '序号', type: 'text', placeholder: '自动生成', disabled: true },
        { key: '姓名', label: '姓名', type: 'text', required: true, placeholder: '请输入姓名' },
        { key: '性别', label: '性别', type: 'select', options: ['男', '女'], required: true },
        { key: '民族', label: '民族', type: 'text', placeholder: '如：汉族' },
        { key: '出生日期', label: '出生日期', type: 'date', required: true },
        { key: '身份证号', label: '身份证号', type: 'idcard', required: true, placeholder: '18位身份证号码' },
        { key: '籍贯', label: '籍贯', type: 'text', placeholder: '如：山西省晋城市' },
        { key: '联系方式', label: '联系方式', type: 'phone', placeholder: '手机号码' }
      ]
    },
    {
      id: 'work',
      title: '工作信息',
      icon: 'briefcase-outline',
      color: '#34C759',
      bgColor: '#F0FFF0',
      fields: [
        { key: '单位', label: '所在单位', type: 'text', required: true, placeholder: '请输入工作单位' },
        { key: '职务', label: '职务', type: 'text', placeholder: '如：党委书记、董事长' },
        { key: '现职级', label: '现职级', type: 'text', required: true, placeholder: '如：正处级' },
        { key: '参加工作时间', label: '参加工作时间', type: 'date' },
        { key: '任现职时间', label: '任现职时间', type: 'date' },
        { key: '任现职级时间', label: '任现职级时间', type: 'date' },
        { key: '专业技术职务', label: '专业技术职务', type: 'text', placeholder: '如：高级工程师' }
      ]
    },
    {
      id: 'political',
      title: '政治面貌',
      icon: 'flag-outline',
      color: '#FF3B30',
      bgColor: '#FFF5F5',
      fields: [
        { key: '政治面貌', label: '政治面貌', type: 'select', options: ['中共党员', '中共预备党员', '共青团员', '民革党员', '民盟盟员', '民建会员', '民进会员', '农工党党员', '致公党党员', '九三学社社员', '台盟盟员', '无党派人士', '群众'] },
        { key: '入党时间', label: '入党时间', type: 'date' }
      ]
    },
    {
      id: 'education',
      title: '教育背景',
      icon: 'school-outline',
      color: '#FF9500',
      bgColor: '#FFFAF0',
      fields: [
        { key: '全日制教育', label: '全日制教育', type: 'select', options: ['博士研究生', '硕士研究生', '大学本科', '大学专科', '中专', '高中', '初中', '小学'] },
        { key: '毕业院校系及专业', label: '毕业院校系及专业', type: 'text', placeholder: '如：清华大学机械工程系机械设计专业' },
        { key: '在职教育', label: '在职教育', type: 'select', options: ['博士研究生', '硕士研究生', '大学本科', '大学专科', '无'] },
        { key: '毕业院校系及专业_在职', label: '在职毕业院校系及专业', type: 'text', placeholder: '在职教育的毕业院校和专业' }
      ]
    },
    {
      id: 'experience',
      title: '履历档案',
      icon: 'document-text-outline',
      color: '#5856D6',
      bgColor: '#F8F8FF',
      fields: [
        { key: '工作简历', label: '工作简历', type: 'textarea', placeholder: '请详细填写工作经历，包括时间、单位、职务等' },
        { key: '获得奖励荣誉情况', label: '获得奖励荣誉情况', type: 'textarea', placeholder: '请填写获得的各类奖励和荣誉' },
        { key: '党纪政纪处分情况', label: '党纪政纪处分情况', type: 'textarea', placeholder: '如无处分请填写"无"' },
        { key: '备注', label: '备注', type: 'textarea', placeholder: '其他需要说明的情况' }
      ]
    }
  ];

  // 加载干部信息
  useEffect(() => {
    if (cadreId && mode !== 'add') {
      loadCadre();
    }
  }, [cadreId, mode]);

  const loadCadre = async () => {
    if (!cadreId) return;

    setLoading(true);
    try {
      const cadreData = await CadreDao.getCadreById(cadreId);
      if (cadreData) {
        setCadre(cadreData);
      }
    } catch (error) {
      console.error('加载干部信息失败:', error);
      Alert.alert('错误', '加载干部信息失败');
    } finally {
      setLoading(false);
    }
  };

  // 更新字段值
  const updateField = (key: keyof CadreInfo, value: string) => {
    setCadre(prev => ({ ...prev, [key]: value }));

    // 身份证号变化时自动验证和填充信息
    if (key === '身份证号' && value) {
      const validation = IdCardValidator.validate(value);
      if (validation.isValid) {
        setCadre(prev => ({
          ...prev,
          身份证号码校验正误: '正确',
          性别: validation.gender || prev.性别,
          出生日期: validation.birthDate || prev.出生日期
        }));
      } else {
        setCadre(prev => ({
          ...prev,
          身份证号码校验正误: '错误'
        }));
      }
    }

    // 清除相关错误
    setErrors(prev => prev.filter(error => error.field !== key));
  };

  // 格式化日期显示
  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;
    return date.toISOString().split('T')[0];
  };

  // 处理日期选择
  const handleDateChange = (dateString: string) => {
    if (showDatePicker) {
      updateField(showDatePicker as keyof CadreInfo, dateString);
    }
    setShowDatePicker(null);
  };

  // 验证表单
  const validateForm = (): boolean => {
    const newErrors: ValidationError[] = [];

    // 获取所有必填字段
    const allRequiredFields: any[] = [];
    fieldSections.forEach(section => {
      section.fields.forEach(field => {
        if (field.required) {
          allRequiredFields.push(field);
        }
      });
    });

    // 验证必填字段
    for (const field of allRequiredFields) {
      const value = cadre[field.key as keyof CadreInfo];
      if (!value || String(value).trim() === '') {
        newErrors.push({
          field: field.key,
          message: `${field.label}不能为空`
        });
      }
    }

    // 验证身份证号
    if (cadre.身份证号) {
      const validation = IdCardValidator.validate(cadre.身份证号);
      if (!validation.isValid) {
        newErrors.push({
          field: '身份证号',
          message: '身份证号格式不正确'
        });
      }
    }

    // 验证手机号
    if (cadre.联系方式 && cadre.联系方式.length > 0) {
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(cadre.联系方式)) {
        newErrors.push({
          field: '联系方式',
          message: '请输入正确的手机号码'
        });
      }
    }

    setErrors(newErrors);
    return newErrors.length === 0;
  };

  // 保存干部信息
  const handleSave = async () => {
    if (!validateForm()) {
      Alert.alert('验证失败', '请检查表单中的错误信息');
      return;
    }

    setLoading(true);
    try {
      if (mode === 'add') {
        const newId = await CadreDao.addCadre(cadre);
        onSave({ ...cadre, id: newId });
      } else {
        if (cadre.id) {
          await CadreDao.updateCadre(cadre.id, cadre);
          onSave(cadre);
        }
      }
      Alert.alert('成功', mode === 'add' ? '添加成功' : '保存成功');
    } catch (error) {
      console.error('保存失败:', error);
      Alert.alert('错误', '保存失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 选择照片
  const pickImage = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('权限不足', '需要访问相册权限');
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [3, 4],
      quality: 0.8,
    });

    if (!result.canceled) {
      setCadre(prev => ({ ...prev, 照片路径: result.assets[0].uri }));
      setShowImagePicker(false);
    }
  };

  // 拍照
  const takePhoto = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('权限不足', '需要访问相机权限');
      return;
    }

    const result = await ImagePicker.launchCameraAsync({
      allowsEditing: true,
      aspect: [3, 4],
      quality: 0.8,
    });

    if (!result.canceled) {
      setCadre(prev => ({ ...prev, 照片路径: result.assets[0].uri }));
      setShowImagePicker(false);
    }
  };

  // 获取字段错误信息
  const getFieldError = (fieldKey: string) => {
    return errors.find(error => error.field === fieldKey)?.message;
  };

  // 渲染表单字段 - 移动端优化
  const renderField = (field: any, sectionColor: string) => {
    const value = cadre[field.key as keyof CadreInfo] || '';
    const error = getFieldError(field.key);
    const isEditable = mode !== 'view' && !field.disabled;

    return (
      <View key={field.key} style={[
        styles.fieldContainer,
        { marginBottom: isSmallScreen ? 16 : 20 }
      ]}>
        <Text style={[
          styles.fieldLabel,
          {
            color: sectionColor,
            fontSize: isSmallScreen ? 14 : 16
          }
        ]}>
          {field.label}
          {field.required && <Text style={styles.required}> *</Text>}
        </Text>

        {field.type === 'textarea' ? (
          <TextInput
            style={[
              styles.textArea,
              error && styles.fieldError,
              { minHeight: isSmallScreen ? 80 : 100 }
            ]}
            value={String(value)}
            onChangeText={(text) => updateField(field.key, text)}
            placeholder={field.placeholder || `请输入${field.label}`}
            multiline
            numberOfLines={isSmallScreen ? 3 : 4}
            editable={isEditable}
            placeholderTextColor="#999"
          />
        ) : field.type === 'select' ? (
          isEditable ? (
            <View style={styles.selectContainer}>
              {field.options.map((option: string) => (
                <TouchableOpacity
                  key={option}
                  style={[
                    styles.selectOption,
                    value === option && { ...styles.selectOptionActive, backgroundColor: sectionColor },
                    {
                      paddingHorizontal: isSmallScreen ? 12 : 16,
                      paddingVertical: isSmallScreen ? 8 : 10,
                      marginRight: isSmallScreen ? 8 : 10,
                      marginBottom: isSmallScreen ? 8 : 10
                    }
                  ]}
                  onPress={() => updateField(field.key, option)}
                  activeOpacity={0.7}
                >
                  <Text style={[
                    styles.selectOptionText,
                    value === option && styles.selectOptionTextActive,
                    { fontSize: isSmallScreen ? 13 : 14 }
                  ]}>
                    {option}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          ) : (
            // 查看模式下直接显示文本值
            <View style={[
              styles.textDisplayContainer,
              { height: isSmallScreen ? 44 : 48 }
            ]}>
              <Text style={[
                styles.textDisplayValue,
                {
                  fontSize: isSmallScreen ? 14 : 16,
                  color: value ? '#1D1D1F' : '#999'
                }
              ]}>
                {value || field.placeholder || `暂无${field.label}`}
              </Text>
            </View>
          )
        ) : field.type === 'date' ? (
          <TouchableOpacity
            style={[
              styles.dateInput,
              error && styles.fieldError,
              { height: isSmallScreen ? 44 : 48 }
            ]}
            onPress={() => {
              if (isEditable) {
                if (Platform.OS === 'web') {
                  const input = document.createElement('input');
                  input.type = 'date';
                  input.value = value ? formatDate(String(value)) : '';
                  input.onchange = (e: any) => {
                    handleDateChange(e.target.value);
                  };
                  input.click();
                } else {
                  setShowDatePicker(field.key);
                }
              }
            }}
            disabled={!isEditable}
            activeOpacity={0.7}
          >
            <Text style={[
              styles.dateText,
              !value && styles.placeholderText,
              { fontSize: isSmallScreen ? 14 : 16 }
            ]}>
              {value ? formatDate(String(value)) : field.placeholder || `请选择${field.label}`}
            </Text>
            <Ionicons
              name="calendar-outline"
              size={isSmallScreen ? 18 : 20}
              color={sectionColor}
            />
          </TouchableOpacity>
        ) : field.type === 'idcard' ? (
          <View>
            <TextInput
              style={[
                styles.textInput,
                error && styles.fieldError,
                field.disabled && styles.disabledInput,
                { height: isSmallScreen ? 44 : 48 }
              ]}
              value={String(value)}
              onChangeText={(text) => updateField(field.key, text)}
              placeholder={field.placeholder || `请输入${field.label}`}
              editable={isEditable}
              maxLength={18}
              placeholderTextColor="#999"
            />
            {value && cadre.身份证号码校验正误 && (
              <View style={styles.validationContainer}>
                <Ionicons
                  name={cadre.身份证号码校验正误 === '正确' ? 'checkmark-circle' : 'close-circle'}
                  size={isSmallScreen ? 14 : 16}
                  color={cadre.身份证号码校验正误 === '正确' ? '#34C759' : '#FF3B30'}
                />
                <Text style={[
                  styles.validationText,
                  {
                    color: cadre.身份证号码校验正误 === '正确' ? '#34C759' : '#FF3B30',
                    fontSize: isSmallScreen ? 12 : 14
                  }
                ]}>
                  身份证号{cadre.身份证号码校验正误}
                </Text>
              </View>
            )}
          </View>
        ) : field.type === 'phone' ? (
          <TextInput
            style={[
              styles.textInput,
              error && styles.fieldError,
              { height: isSmallScreen ? 44 : 48 }
            ]}
            value={String(value)}
            onChangeText={(text) => updateField(field.key, text)}
            placeholder={field.placeholder || `请输入${field.label}`}
            editable={isEditable}
            keyboardType="phone-pad"
            maxLength={11}
            placeholderTextColor="#999"
          />
        ) : (
          <TextInput
            style={[
              styles.textInput,
              error && styles.fieldError,
              field.disabled && styles.disabledInput,
              { height: isSmallScreen ? 44 : 48 }
            ]}
            value={String(value)}
            onChangeText={(text) => updateField(field.key, text)}
            placeholder={field.placeholder || `请输入${field.label}`}
            editable={isEditable}
            placeholderTextColor="#999"
          />
        )}

        {error && (
          <Text style={[
            styles.errorText,
            { fontSize: isSmallScreen ? 12 : 14 }
          ]}>
            {error}
          </Text>
        )}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFF" />

      {/* 固定头部 */}
      <View style={styles.header}>
        <TouchableOpacity
          onPress={onCancel}
          style={styles.headerButton}
          activeOpacity={0.7}
        >
          <Ionicons
            name="arrow-back"
            size={isSmallScreen ? 22 : 24}
            color="#007AFF"
          />
        </TouchableOpacity>

        <Text style={[
          styles.headerTitle,
          { fontSize: isSmallScreen ? 16 : 18 }
        ]}>
          {mode === 'add' ? '添加干部' : mode === 'edit' ? '编辑干部' : '干部详情'}
        </Text>

        {mode !== 'view' ? (
          <TouchableOpacity
            onPress={handleSave}
            disabled={loading}
            style={[
              styles.saveButtonContainer,
              {
                paddingHorizontal: isSmallScreen ? 12 : 16,
                paddingVertical: isSmallScreen ? 6 : 8
              }
            ]}
            activeOpacity={0.8}
          >
            <Text style={[
              styles.saveButton,
              loading && styles.saveButtonDisabled,
              { fontSize: isSmallScreen ? 13 : 14 }
            ]}>
              {loading ? '保存中...' : '保存'}
            </Text>
          </TouchableOpacity>
        ) : (
          <View style={styles.headerButton} />
        )}
      </View>

      {/* 主要内容区域 - 单页滑动 */}
      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* 照片和基本信息卡片 */}
        <View style={[
          styles.photoSection,
          { paddingVertical: isSmallScreen ? 16 : 20 }
        ]}>
          <TouchableOpacity
            style={[
              styles.photoContainer,
              {
                width: isSmallScreen ? 100 : 120,
                height: isSmallScreen ? 130 : 160
              }
            ]}
            onPress={() => mode !== 'view' && setShowImagePicker(true)}
            activeOpacity={0.8}
          >
            {cadre.照片路径 ? (
              <Image source={{ uri: cadre.照片路径 }} style={styles.photo} />
            ) : (
              <View style={styles.photoPlaceholder}>
                <Ionicons
                  name="person"
                  size={isSmallScreen ? 40 : 50}
                  color="#007AFF"
                />
                <Text style={[
                  styles.photoPlaceholderText,
                  { fontSize: isSmallScreen ? 12 : 14 }
                ]}>
                  {mode === 'view' ? '暂无照片' : '点击添加照片'}
                </Text>
              </View>
            )}
          </TouchableOpacity>

          {/* 基本信息卡片 */}
          {cadre.姓名 && (
            <View style={[
              styles.infoCard,
              {
                marginTop: isSmallScreen ? 12 : 16,
                paddingHorizontal: isSmallScreen ? 12 : 16,
                paddingVertical: isSmallScreen ? 10 : 12
              }
            ]}>
              <Text style={[
                styles.nameText,
                { fontSize: isSmallScreen ? 18 : 20 }
              ]}>
                {cadre.姓名}
              </Text>
              {cadre.职务 && (
                <Text style={[
                  styles.positionText,
                  { fontSize: isSmallScreen ? 14 : 16 }
                ]}>
                  {cadre.职务}
                </Text>
              )}
              {cadre.单位 && (
                <Text style={[
                  styles.unitText,
                  { fontSize: isSmallScreen ? 12 : 14 }
                ]}>
                  {cadre.单位}
                </Text>
              )}
            </View>
          )}
        </View>

        {/* 所有分类的表单字段 - 在一个页面上滑动查看 */}
        {fieldSections.map((section, index) => (
          <View key={section.id} style={[
            styles.sectionContainer,
            {
              marginHorizontal: isSmallScreen ? 12 : 16,
              marginBottom: isSmallScreen ? 16 : 20
            }
          ]}>
            {/* 分类标题 */}
            <View style={[
              styles.sectionHeader,
              {
                backgroundColor: section.color,
                paddingHorizontal: isSmallScreen ? 16 : 20,
                paddingVertical: isSmallScreen ? 12 : 16
              }
            ]}>
              <Ionicons
                name={section.icon as any}
                size={isSmallScreen ? 20 : 24}
                color="#FFF"
              />
              <Text style={[
                styles.sectionTitle,
                { fontSize: isSmallScreen ? 16 : 18 }
              ]}>
                {section.title}
              </Text>
            </View>

            {/* 分类背景色区域 */}
            <View style={[
              styles.sectionContent,
              {
                backgroundColor: section.bgColor,
                paddingHorizontal: isSmallScreen ? 12 : 16,
                paddingVertical: isSmallScreen ? 16 : 20
              }
            ]}>
              {section.fields.map(field =>
                renderField(field, section.color)
              )}
            </View>
          </View>
        ))}

        {/* 底部安全区域 */}
        <View style={{ height: isSmallScreen ? 20 : 30 }} />
      </ScrollView>

      {/* 照片选择模态框 */}
      <Modal
        visible={showImagePicker}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowImagePicker(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>选择照片</Text>
            <TouchableOpacity style={styles.modalOption} onPress={takePhoto}>
              <Ionicons name="camera" size={24} color="#007AFF" />
              <Text style={styles.modalOptionText}>拍照</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.modalOption} onPress={pickImage}>
              <Ionicons name="image" size={24} color="#007AFF" />
              <Text style={styles.modalOptionText}>从相册选择</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.modalCancel}
              onPress={() => setShowImagePicker(false)}
            >
              <Text style={styles.modalCancelText}>取消</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* 日期选择器模态框 - 仅在非Web环境下显示 */}
      {showDatePicker && Platform.OS !== 'web' && (
        <Modal
          visible={true}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowDatePicker(null)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <Text style={styles.modalTitle}>选择日期</Text>
              <TextInput
                style={styles.textInput}
                placeholder="YYYY-MM-DD"
                value={cadre[showDatePicker as keyof CadreInfo] as string || ''}
                onChangeText={(text) => handleDateChange(text)}
              />
              <TouchableOpacity
                style={styles.modalCancel}
                onPress={() => setShowDatePicker(null)}
              >
                <Text style={styles.modalCancelText}>确定</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA'
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: isSmallScreen ? 16 : 20,
    paddingVertical: isSmallScreen ? 12 : 16,
    paddingTop: Platform.OS === 'ios' ? (isSmallScreen ? 44 : 50) : (isSmallScreen ? 12 : 16),
    backgroundColor: '#FFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3
  },
  headerButton: {
    width: isSmallScreen ? 50 : 60,
    height: isSmallScreen ? 40 : 44,
    alignItems: 'center',
    justifyContent: 'center'
  },
  headerTitle: {
    fontWeight: '700',
    color: '#1D1D1F',
    flex: 1,
    textAlign: 'center'
  },
  saveButtonContainer: {
    backgroundColor: '#007AFF',
    borderRadius: isSmallScreen ? 18 : 20,
    minWidth: isSmallScreen ? 50 : 60,
    alignItems: 'center',
    justifyContent: 'center'
  },
  saveButton: {
    color: '#FFF',
    fontWeight: '600',
    textAlign: 'center'
  },
  saveButtonDisabled: {
    color: '#8E8E93'
  },
  content: {
    flex: 1
  },
  scrollContent: {
    paddingBottom: isSmallScreen ? 20 : 30
  },
  photoSection: {
    alignItems: 'center',
    backgroundColor: '#FFF',
    marginBottom: isSmallScreen ? 12 : 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2
  },
  photoContainer: {
    borderRadius: isSmallScreen ? 12 : 16,
    overflow: 'hidden',
    backgroundColor: '#F8F9FA',
    borderWidth: 2,
    borderColor: '#E5E5EA',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3
  },
  photo: {
    width: '100%',
    height: '100%'
  },
  photoPlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F8F9FA'
  },
  photoPlaceholderText: {
    marginTop: isSmallScreen ? 8 : 12,
    fontWeight: '500',
    textAlign: 'center',
    color: '#007AFF'
  },
  infoCard: {
    backgroundColor: '#FFF',
    borderRadius: isSmallScreen ? 10 : 12,
    borderLeftWidth: 4,
    borderLeftColor: '#007AFF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    minWidth: width * (isSmallScreen ? 0.85 : 0.8),
    maxWidth: width * 0.9
  },
  nameText: {
    fontWeight: '700',
    color: '#1D1D1F',
    marginBottom: 4
  },
  positionText: {
    fontWeight: '500',
    color: '#007AFF',
    marginBottom: 4
  },
  unitText: {
    color: '#666',
    fontWeight: '400'
  },
  sectionContainer: {
    backgroundColor: '#FFF',
    borderRadius: isSmallScreen ? 12 : 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 6,
    elevation: 3
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  sectionTitle: {
    marginLeft: isSmallScreen ? 10 : 12,
    fontWeight: '600',
    color: '#FFF'
  },
  sectionContent: {
    // 分类内容区域的背景色在组件中动态设置
  },
  fieldContainer: {
    // 字段容器的边距在组件中动态设置
  },
  fieldLabel: {
    fontWeight: '600',
    marginBottom: isSmallScreen ? 6 : 8
  },
  required: {
    color: '#FF3B30'
  },
  textInput: {
    borderWidth: 1.5,
    borderColor: '#E5E5EA',
    borderRadius: isSmallScreen ? 10 : 12,
    paddingHorizontal: isSmallScreen ? 12 : 16,
    paddingVertical: isSmallScreen ? 10 : 14,
    fontSize: isSmallScreen ? 14 : 16,
    backgroundColor: '#FFF',
    color: '#1D1D1F'
  },
  disabledInput: {
    backgroundColor: '#F5F5F5',
    color: '#999'
  },
  textArea: {
    borderWidth: 1.5,
    borderColor: '#E5E5EA',
    borderRadius: isSmallScreen ? 10 : 12,
    paddingHorizontal: isSmallScreen ? 12 : 16,
    paddingVertical: isSmallScreen ? 10 : 14,
    fontSize: isSmallScreen ? 14 : 16,
    backgroundColor: '#FFF',
    textAlignVertical: 'top',
    color: '#1D1D1F'
  },
  dateInput: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1.5,
    borderColor: '#E5E5EA',
    borderRadius: isSmallScreen ? 10 : 12,
    paddingHorizontal: isSmallScreen ? 12 : 16,
    backgroundColor: '#FFF'
  },
  dateText: {
    color: '#1D1D1F'
  },
  placeholderText: {
    color: '#999'
  },
  validationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: isSmallScreen ? 6 : 8
  },
  validationText: {
    marginLeft: 6,
    fontWeight: '500'
  },
  selectContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 4
  },
  selectOption: {
    borderRadius: isSmallScreen ? 20 : 25,
    borderWidth: 1.5,
    borderColor: '#E5E5EA',
    backgroundColor: '#FFF'
  },
  selectOptionActive: {
    borderColor: 'transparent'
  },
  selectOptionDisabled: {
    opacity: 0.6
  },
  selectOptionText: {
    fontWeight: '500',
    color: '#666'
  },
  selectOptionTextActive: {
    color: '#FFF',
    fontWeight: '600'
  },
  textDisplayContainer: {
    justifyContent: 'center',
    borderWidth: 1.5,
    borderColor: '#E5E5EA',
    borderRadius: isSmallScreen ? 10 : 12,
    paddingHorizontal: isSmallScreen ? 12 : 16,
    backgroundColor: '#F8F9FA'
  },
  textDisplayValue: {
    fontWeight: '500'
  },
  fieldError: {
    borderColor: '#FF3B30',
    backgroundColor: '#FFF5F5'
  },
  errorText: {
    marginTop: isSmallScreen ? 6 : 8,
    color: '#FF3B30',
    fontWeight: '500'
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'flex-end'
  },
  modalContent: {
    backgroundColor: '#FFF',
    borderTopLeftRadius: isSmallScreen ? 20 : 24,
    borderTopRightRadius: isSmallScreen ? 20 : 24,
    paddingHorizontal: isSmallScreen ? 20 : 24,
    paddingBottom: isSmallScreen ? 30 : 40
  },
  modalTitle: {
    fontSize: isSmallScreen ? 18 : 20,
    fontWeight: '700',
    color: '#1D1D1F',
    textAlign: 'center',
    paddingVertical: isSmallScreen ? 20 : 24,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA'
  },
  modalOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: isSmallScreen ? 16 : 20,
    borderBottomWidth: 1,
    borderBottomColor: '#F2F2F7'
  },
  modalOptionText: {
    marginLeft: isSmallScreen ? 12 : 16,
    fontSize: isSmallScreen ? 16 : 18,
    fontWeight: '500',
    color: '#1D1D1F'
  },
  modalCancel: {
    paddingVertical: isSmallScreen ? 16 : 20,
    alignItems: 'center'
  },
  modalCancelText: {
    fontSize: isSmallScreen ? 16 : 18,
    color: '#FF3B30',
    fontWeight: '600'
  }
});
