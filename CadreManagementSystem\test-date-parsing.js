/**
 * 测试日期解析功能
 */

// 模拟日期解析函数
function parseBirthDate(birthDateStr) {
  if (!birthDateStr && birthDateStr !== 0) {
    console.log('❌ 出生日期为空');
    return null;
  }
  
  // 处理Excel日期序列号
  if (typeof birthDateStr === 'number') {
    console.log(`🔍 解析Excel日期序列号：${birthDateStr}`);
    try {
      // Excel日期序列号转换为JavaScript日期
      // Excel的日期基准是1900年1月1日，但实际上是1899年12月30日
      const excelDate = new Date((birthDateStr - 25569) * 86400 * 1000);
      if (!isNaN(excelDate.getTime()) && excelDate.getFullYear() > 1900 && excelDate.getFullYear() < 2100) {
        console.log(`✅ Excel日期解析成功：${excelDate.toLocaleDateString()}`);
        return excelDate;
      }
    } catch (e) {
      console.log(`❌ Excel日期解析失败：${e}`);
    }
  }
  
  // 清理字符串
  const cleanStr = birthDateStr.toString().trim();
  console.log(`🔍 解析出生日期字符串：'${cleanStr}'`);
  
  // 支持多种日期格式
  const formats = [
    /^(\d{4})-(\d{1,2})-(\d{1,2})$/,     // 1975-08-15
    /^(\d{4})\/(\d{1,2})\/(\d{1,2})$/,   // 1975/08/15
    /^(\d{4})\.(\d{1,2})\.(\d{1,2})$/,   // 1975.08.15
    /^(\d{4})年(\d{1,2})月(\d{1,2})日$/,  // 1975年08月15日
    /^(\d{1,2})-(\d{1,2})-(\d{4})$/,     // 15-08-1975
    /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/,   // 15/08/1975
    /^(\d{8})$/                          // 19750815
  ];
  
  for (let i = 0; i < formats.length; i++) {
    const format = formats[i];
    const match = cleanStr.match(format);
    if (match) {
      let year, month, day;
      
      if (i < 4) {
        // 年-月-日格式
        year = parseInt(match[1]);
        month = parseInt(match[2]) - 1; // JavaScript月份从0开始
        day = parseInt(match[3]);
      } else if (i < 6) {
        // 日-月-年格式
        day = parseInt(match[1]);
        month = parseInt(match[2]) - 1;
        year = parseInt(match[3]);
      } else {
        // 8位数字格式 YYYYMMDD
        const str = match[1];
        year = parseInt(str.substring(0, 4));
        month = parseInt(str.substring(4, 6)) - 1;
        day = parseInt(str.substring(6, 8));
      }
      
      const date = new Date(year, month, day);
      if (!isNaN(date.getTime()) && year > 1900 && year < 2100) {
        console.log(`✅ 解析成功：${date.toLocaleDateString()}`);
        return date;
      }
    }
  }
  
  // 尝试直接解析
  try {
    const date = new Date(cleanStr);
    if (!isNaN(date.getTime()) && date.getFullYear() > 1900 && date.getFullYear() < 2100) {
      console.log(`✅ 直接解析成功：${date.toLocaleDateString()}`);
      return date;
    }
  } catch (e) {
    // 忽略错误
  }
  
  console.log(`❌ 无法解析出生日期：'${cleanStr}'`);
  return null;
}

console.log('🧪 测试日期解析功能...\n');

// 测试Excel中的实际数据
const testCases = [
  ['王四顿', 23853],  // Excel日期序列号
  ['许国泰', 24368],  // Excel日期序列号
  ['测试1', '1964-03-15'],
  ['测试2', '1969-08-20'],
  ['测试3', '1964/3/15'],
  ['测试4', '1969/8/20']
];

testCases.forEach(([name, birthDate]) => {
  console.log(`\n👤 ${name}:`);
  const date = parseBirthDate(birthDate);
  if (date) {
    const today = new Date();
    const age = today.getFullYear() - date.getFullYear();
    const monthDiff = today.getMonth() - date.getMonth();
    
    let currentAge = age;
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < date.getDate())) {
      currentAge--;
    }
    
    console.log(`   当前年龄: ${currentAge}岁`);
    
    // 简单的退休预警判断
    const retirementAge = 60; // 简化为60岁
    const yearsToRetirement = retirementAge - currentAge;
    
    if (yearsToRetirement <= 0) {
      console.log(`   🚨 已达到退休年龄`);
    } else if (yearsToRetirement <= 5) {
      console.log(`   ⚠️ 退休预警 (${yearsToRetirement}年后退休)`);
    } else {
      console.log(`   ✅ 正常 (${yearsToRetirement}年后退休)`);
    }
  }
});

console.log('\n✅ 测试完成！');
