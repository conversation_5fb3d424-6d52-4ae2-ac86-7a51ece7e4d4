/**
 * 退休规则配置类型定义
 */

// 退居二线规则配置
export interface SecondLineRule {
  id: string;
  name: string;                    // 规则名称
  positionLevels: string[];        // 适用职级列表
  minAge: number;                  // 最小年龄
  maxAge: number;                  // 最大年龄
  description: string;             // 规则描述
  enabled: boolean;                // 是否启用
  createdAt: string;
  updatedAt: string;
}

// 退休预警规则配置
export interface RetirementRule {
  id: string;
  name: string;                    // 规则名称
  minAge: number;                  // 最小年龄
  maxAge: number;                  // 最大年龄
  description: string;             // 规则描述
  enabled: boolean;                // 是否启用
  createdAt: string;
  updatedAt: string;
}

// 已退休规则配置
export interface RetiredRule {
  id: string;
  name: string;                    // 规则名称
  minAge: number;                  // 最小年龄（通常是60岁以上）
  description: string;             // 规则描述
  enabled: boolean;                // 是否启用
  createdAt: string;
  updatedAt: string;
}

// 延迟退休配置
export interface DelayRetirementConfig {
  id: string;
  birthYearStart: number;          // 出生年份开始
  birthYearEnd: number;            // 出生年份结束
  delayMonthsPerYear: number;      // 每年延迟月数
  maxDelayMonths: number;          // 最大延迟月数
  description: string;             // 配置描述
  enabled: boolean;                // 是否启用
  createdAt: string;
  updatedAt: string;
}

// 完整的退休配置
export interface RetirementConfig {
  secondLineRules: SecondLineRule[];
  retirementRules: RetirementRule[];
  retiredRules: RetiredRule[];
  delayConfigs: DelayRetirementConfig[];
  lastUpdated: string;
}

// 预警类型（简化版）
export type WarningType =
  | 'near_retirement'        // 近两年退休预警
  | 'near_second_line'       // 近两年退居二线
  | 'already_second_line'    // 已退居二线
  | 'retired';               // 已退休

// 预警信息
export interface RetirementWarningInfo {
  cadre: any;                      // 干部信息
  warningType: WarningType;        // 预警类型
  urgencyLevel: 'critical' | 'urgent' | 'normal' | 'info';
  description: string;             // 预警描述
  daysUntilRetirement: number;     // 距离退休天数
  delayInfo?: {                    // 延迟退休信息
    delayYears: number;
    delayMonths: number;
    originalRetirementAge: number;
    actualRetirementAge: number;
  };
  matchedRule: {                   // 匹配的规则
    ruleId: string;
    ruleName: string;
    ruleType: 'second_line' | 'retirement' | 'retired';
  };
}

// 默认配置
export const DEFAULT_RETIREMENT_CONFIG: RetirementConfig = {
  secondLineRules: [
    {
      id: 'default_second_line',
      name: '中层干部退居二线规则',
      positionLevels: ['中层正职', '中层副职', '正处', '副处', '处级', '科级'],
      minAge: 56,
      maxAge: 58,
      description: '中层正职、中层副职、正处、副处等职务人员56-58岁退居二线预警',
      enabled: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  ],
  retirementRules: [
    {
      id: 'default_retirement',
      name: '退休预警规则',
      minAge: 58,
      maxAge: 60,
      description: '58-60岁人员退休预警，结合延迟退休计算',
      enabled: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  ],
  retiredRules: [
    {
      id: 'default_retired',
      name: '已退休规则',
      minAge: 60,
      description: '大于60岁人员视为已退休',
      enabled: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  ],
  delayConfigs: [
    {
      id: 'delay_1965_before',
      birthYearStart: 0,
      birthYearEnd: 1965,
      delayMonthsPerYear: 0,
      maxDelayMonths: 0,
      description: '1965年及以前出生，不延迟退休',
      enabled: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 'delay_1966_1970',
      birthYearStart: 1966,
      birthYearEnd: 1970,
      delayMonthsPerYear: 2,
      maxDelayMonths: 12,
      description: '1966-1970年出生，每年延迟2个月，最多1年',
      enabled: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 'delay_1971_1980',
      birthYearStart: 1971,
      birthYearEnd: 1980,
      delayMonthsPerYear: 3,
      maxDelayMonths: 36,
      description: '1971-1980年出生，每年延迟3个月，最多3年',
      enabled: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 'delay_1981_after',
      birthYearStart: 1981,
      birthYearEnd: 9999,
      delayMonthsPerYear: 4,
      maxDelayMonths: 60,
      description: '1981年及以后出生，每年延迟4个月，最多5年',
      enabled: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  ],
  lastUpdated: new Date().toISOString()
};
