import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Platform } from 'react-native';
import { Theme, themes, defaultTheme } from '../themes';

// 跨平台存储适配器
const Storage = {
  async getItem(key: string): Promise<string | null> {
    if (Platform.OS === 'web') {
      try {
        return localStorage.getItem(key);
      } catch (error) {
        console.warn('localStorage not available:', error);
        return null;
      }
    } else {
      try {
        const AsyncStorage = require('@react-native-async-storage/async-storage').default;
        return await AsyncStorage.getItem(key);
      } catch (error) {
        console.warn('AsyncStorage not available:', error);
        return null;
      }
    }
  },
  async setItem(key: string, value: string): Promise<void> {
    if (Platform.OS === 'web') {
      try {
        localStorage.setItem(key, value);
      } catch (error) {
        console.warn('localStorage not available:', error);
      }
    } else {
      try {
        const AsyncStorage = require('@react-native-async-storage/async-storage').default;
        await AsyncStorage.setItem(key, value);
      } catch (error) {
        console.warn('AsyncStorage not available:', error);
      }
    }
  }
};

interface ThemeContextType {
  theme: Theme;
  themeName: string;
  setTheme: (themeName: string) => void;
  availableThemes: { name: string; displayName: string }[];
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [currentTheme, setCurrentTheme] = useState<Theme>(defaultTheme);
  const [currentThemeName, setCurrentThemeName] = useState<string>('business');

  // 可用主题列表
  const availableThemes = Object.entries(themes).map(([name, theme]) => ({
    name,
    displayName: theme.displayName,
  }));

  // 加载保存的主题
  useEffect(() => {
    loadSavedTheme();
  }, []);

  const loadSavedTheme = async () => {
    try {
      const savedTheme = await Storage.getItem('selectedTheme');
      if (savedTheme && themes[savedTheme as keyof typeof themes]) {
        setTheme(savedTheme);
      }
    } catch (error) {
      console.error('加载主题失败:', error);
    }
  };

  const setTheme = async (themeName: string) => {
    try {
      const theme = themes[themeName as keyof typeof themes];
      if (theme) {
        setCurrentTheme(theme);
        setCurrentThemeName(themeName);
        await Storage.setItem('selectedTheme', themeName);
      }
    } catch (error) {
      console.error('保存主题失败:', error);
    }
  };

  const value: ThemeContextType = {
    theme: currentTheme,
    themeName: currentThemeName,
    setTheme,
    availableThemes,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
