/**
 * 检查应用实际数据库结构和数据
 */
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, 'cadre_management.db');

console.log('🔍 检查应用实际数据库...');
console.log('数据库路径:', dbPath);

const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ 连接数据库失败:', err.message);
    return;
  }
  console.log('✅ 已连接到应用数据库');
});

// 检查所有表
db.all("SELECT name FROM sqlite_master WHERE type='table'", [], (err, tables) => {
  if (err) {
    console.error('❌ 查询表失败:', err.message);
    return;
  }
  
  console.log('\n📋 数据库中的表:');
  tables.forEach(table => {
    console.log(`- ${table.name}`);
  });
  
  // 检查每个表的结构和数据
  let tableIndex = 0;
  function checkNextTable() {
    if (tableIndex >= tables.length) {
      db.close();
      return;
    }
    
    const tableName = tables[tableIndex].name;
    console.log(`\n🔍 检查表: ${tableName}`);
    console.log('='.repeat(50));
    
    // 获取表结构
    db.all(`PRAGMA table_info(${tableName})`, [], (err, columns) => {
      if (err) {
        console.error(`❌ 获取表结构失败:`, err.message);
        tableIndex++;
        checkNextTable();
        return;
      }
      
      console.log('📋 表结构:');
      columns.forEach(col => {
        console.log(`  ${col.name} (${col.type})`);
      });
      
      // 获取数据数量
      db.get(`SELECT COUNT(*) as count FROM ${tableName}`, [], (err, result) => {
        if (err) {
          console.error(`❌ 查询数据数量失败:`, err.message);
          tableIndex++;
          checkNextTable();
          return;
        }
        
        const count = result.count;
        console.log(`📊 数据数量: ${count} 条`);
        
        if (count > 0 && count <= 10) {
          // 显示所有数据
          db.all(`SELECT * FROM ${tableName} LIMIT 10`, [], (err, rows) => {
            if (err) {
              console.error(`❌ 查询数据失败:`, err.message);
            } else {
              console.log('📋 数据内容:');
              rows.forEach((row, index) => {
                console.log(`  ${index + 1}. ${JSON.stringify(row)}`);
              });
            }
            
            tableIndex++;
            checkNextTable();
          });
        } else if (count > 10) {
          // 只显示前5条
          db.all(`SELECT * FROM ${tableName} LIMIT 5`, [], (err, rows) => {
            if (err) {
              console.error(`❌ 查询数据失败:`, err.message);
            } else {
              console.log('📋 前5条数据:');
              rows.forEach((row, index) => {
                console.log(`  ${index + 1}. ${JSON.stringify(row)}`);
              });
            }
            
            tableIndex++;
            checkNextTable();
          });
        } else {
          tableIndex++;
          checkNextTable();
        }
      });
    });
  }
  
  checkNextTable();
});
