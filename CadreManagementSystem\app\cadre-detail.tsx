import React from 'react';
import { View, StyleSheet } from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { CadreDetail } from '../src/components/CadreDetail';
import { CadreInfo } from '../src/types';

export default function CadreDetailScreen() {
  const params = useLocalSearchParams();
  const cadreId = params.id ? parseInt(params.id as string) : undefined;
  const mode = (params.mode as 'view' | 'edit' | 'add') || 'view';

  const handleSave = (cadre: CadreInfo) => {
    // 保存成功后返回上一页
    router.back();
  };

  const handleCancel = () => {
    router.back();
  };

  return (
    <View style={styles.container}>
      <CadreDetail
        cadreId={cadreId}
        mode={mode}
        onSave={handleSave}
        onCancel={handleCancel}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7'
  }
});
