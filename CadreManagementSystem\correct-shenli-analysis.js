console.log('🔍 正确分析申丽丽的退休情况...\n');

// 从身份证号解析出生日期
const idCard = '140525197101230022';
console.log(`📋 申丽丽身份证号: ${idCard}`);

// 从身份证号提取出生日期
const birthYearStr = idCard.substring(6, 10);
const birthMonthStr = idCard.substring(10, 12);
const birthDayStr = idCard.substring(12, 14);

const birthYear = parseInt(birthYearStr);
const birthMonth = parseInt(birthMonthStr);
const birthDay = parseInt(birthDayStr);

console.log(`📅 从身份证号解析的出生日期：`);
console.log(`出生年份: ${birthYear}年`);
console.log(`出生月份: ${birthMonth}月`);
console.log(`出生日期: ${birthDay}日`);
console.log(`完整出生日期: ${birthYear}年${birthMonth}月${birthDay}日`);

// 创建出生日期对象
const birthDate = new Date(birthYear, birthMonth - 1, birthDay); // 月份从0开始
console.log(`解析后的日期对象: ${birthDate.toLocaleDateString()}`);

// 计算当前年龄
const today = new Date();
let age = today.getFullYear() - birthDate.getFullYear();
const monthDiff = today.getMonth() - birthDate.getMonth();

if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
  age--;
}

console.log(`\n👤 年龄计算：`);
console.log(`当前年龄: ${age}岁`);
console.log(`性别: 女`);
console.log(`法定退休年龄: 55岁（女性）`);

// 计算延迟退休
function calculateDelayRetirement(birthYear) {
  let totalDelayMonths = 3; // 基础延迟3个月
  
  if (birthYear >= 1966 && birthYear <= 1970) {
    const extraYears = birthYear - 1965;
    const extraMonths = Math.min(extraYears * 2, 12); // 每年2个月，最多1年
    totalDelayMonths += extraMonths;
  } else if (birthYear >= 1971 && birthYear <= 1980) {
    totalDelayMonths += 12; // 先加满1年
    const extraYears = birthYear - 1970;
    const extraMonths = Math.min(extraYears * 3, 24); // 每年3个月，最多2年
    totalDelayMonths += extraMonths;
  } else if (birthYear >= 1981) {
    totalDelayMonths += 36; // 先加满3年
    const extraYears = birthYear - 1980;
    const extraMonths = Math.min(extraYears * 4, 24); // 每年4个月，最多2年
    totalDelayMonths += extraMonths;
  }
  
  const maxDelayMonths = 60; // 最多延迟5年
  totalDelayMonths = Math.min(totalDelayMonths, maxDelayMonths);
  
  return {
    totalDelayMonths,
    delayYears: Math.floor(totalDelayMonths / 12),
    delayMonths: totalDelayMonths % 12
  };
}

const delayInfo = calculateDelayRetirement(birthYear);
console.log(`\n⏰ 延迟退休计算：`);
console.log(`出生年份: ${birthYear}年`);
console.log(`适用规则: 1971-1980年（基础3个月 + 1年 + 每年3个月）`);
console.log(`计算过程: 基础3个月 + 12个月 + (${birthYear} - 1970) × 3个月 = 15 + ${(birthYear - 1970) * 3} = ${delayInfo.totalDelayMonths}个月`);
console.log(`延迟月数: ${delayInfo.totalDelayMonths}个月`);
console.log(`延迟格式: ${delayInfo.delayYears > 0 ? `${delayInfo.delayYears}年` : ''}${delayInfo.delayMonths}个月`);

// 计算实际退休年龄和日期
const baseRetirementAge = 55; // 女性55岁
const actualRetirementAge = baseRetirementAge + (delayInfo.totalDelayMonths / 12);

console.log(`\n🎯 实际退休计算：`);
console.log(`法定退休年龄: ${baseRetirementAge}岁`);
console.log(`延迟月数: ${delayInfo.totalDelayMonths}个月`);
console.log(`实际退休年龄: ${baseRetirementAge} + ${delayInfo.totalDelayMonths}/12 = ${actualRetirementAge.toFixed(2)}岁`);

// 计算实际退休日期
const legalRetirementDate = new Date(birthDate);
legalRetirementDate.setFullYear(birthDate.getFullYear() + baseRetirementAge);

const actualRetirementDate = new Date(legalRetirementDate);
actualRetirementDate.setMonth(actualRetirementDate.getMonth() + delayInfo.totalDelayMonths);

console.log(`\n📅 退休日期计算：`);
console.log(`法定退休日期: ${legalRetirementDate.getFullYear()}年${legalRetirementDate.getMonth() + 1}月${legalRetirementDate.getDate()}日`);
console.log(`实际退休日期: ${actualRetirementDate.getFullYear()}年${actualRetirementDate.getMonth() + 1}月${actualRetirementDate.getDate()}日`);

// 计算距离退休的天数
const daysToLegal = Math.ceil((legalRetirementDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
const daysToActual = Math.ceil((actualRetirementDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

console.log(`\n⏳ 倒计时计算：`);
console.log(`距离法定退休: ${daysToLegal}天`);
console.log(`距离实际退休: ${daysToActual}天`);

// 判断退休状态
console.log(`\n📊 退休状态判断：`);
if (age < baseRetirementAge) {
  console.log(`✅ 未到法定退休年龄（${age}岁 < ${baseRetirementAge}岁）`);
  if (daysToActual > 0) {
    console.log(`✅ 未到实际退休日期（还剩${daysToActual}天）`);
    console.log(`📋 应归类为: 近两年退休预警`);
  } else {
    console.log(`❌ 已超过实际退休日期（超过${Math.abs(daysToActual)}天）`);
    console.log(`📋 应归类为: 已退休`);
  }
} else {
  console.log(`⚠️ 已达到法定退休年龄（${age}岁 >= ${baseRetirementAge}岁）`);
  if (daysToActual > 0) {
    console.log(`✅ 但未到实际退休日期（还剩${daysToActual}天）`);
    console.log(`📋 应归类为: 已超过法定退休年龄，但未到实际退休`);
  } else {
    console.log(`❌ 已超过实际退休日期（超过${Math.abs(daysToActual)}天）`);
    console.log(`📋 应归类为: 已退休`);
  }
}

console.log(`\n🎯 最终正确结论：`);
console.log(`申丽丽（女，${birthYear}年${birthMonth}月${birthDay}日）：`);
console.log(`- 当前年龄: ${age}岁`);
console.log(`- 延迟月数: ${delayInfo.totalDelayMonths}个月（${delayInfo.delayYears}年${delayInfo.delayMonths}个月）`);
console.log(`- 距离法定退休: ${daysToLegal}天`);
console.log(`- 距离实际退休: ${daysToActual}天`);
console.log(`- 状态: ${age < baseRetirementAge ? '未到法定退休年龄' : '已超过法定退休年龄'}，${daysToActual > 0 ? '未到实际退休日期' : '已超过实际退休日期'}`);

console.log(`\n🔧 需要修复的问题：`);
if (age < baseRetirementAge && daysToActual > 0) {
  console.log(`✅ 申丽丽应该在"近两年退休预警"列表中，而不是"已退休"列表`);
  console.log(`✅ 这证实了我们的修复是正确的`);
} else if (age >= baseRetirementAge && daysToActual > 0) {
  console.log(`⚠️ 申丽丽已超过法定退休年龄但未到实际退休日期`);
  console.log(`⚠️ 应该显示为"已超过法定退休年龄，距离实际退休还剩X天"`);
} else {
  console.log(`❌ 申丽丽确实已经退休`);
}

console.log(`\n📋 女性干部退休预警修复要点：`);
console.log(`1. 正确使用55岁作为女性法定退休年龄`);
console.log(`2. 1971年出生应延迟18个月（1年6个月）`);
console.log(`3. 实际退休年龄应为56.5岁`);
console.log(`4. 只有实际退休日期已过才算"已退休"`);
console.log(`5. 确保所有女性干部计算逻辑一致`);

console.log('\n🔍 申丽丽正确分析完成！');
