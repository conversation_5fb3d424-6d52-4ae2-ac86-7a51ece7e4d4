/**
 * 测试精准时间计算和智能显示格式
 */

console.log('🎯 测试精准时间计算和智能显示格式...\n');

// 智能格式化时间显示
function formatTimeRemaining(totalDays, context = '') {
  if (totalDays <= 0) {
    return {
      text: `已超过${context}`,
      daysText: '0天',
      showDays: true
    };
  }

  const years = Math.floor(totalDays / 365);
  const remainingAfterYears = totalDays % 365;
  const months = Math.floor(remainingAfterYears / 30);
  const days = remainingAfterYears % 30;

  let timeText = '';
  let showDays = false;

  if (years > 0) {
    // 大于1年：显示X年X月
    timeText = `${years}年${months}个月`;
    showDays = true;
  } else if (months > 0) {
    // 大于1个月：显示X个月
    timeText = `${months}个月`;
    showDays = true;
  } else {
    // 不足1个月：仅显示X天
    timeText = '';
    showDays = true;
  }

  return {
    text: timeText,
    daysText: `${totalDays}天`,
    showDays: showDays
  };
}

// 计算距离指定年龄的剩余时间
function calculateTimeToAge(birthDate, targetAge) {
  const targetDate = new Date(birthDate);
  targetDate.setFullYear(birthDate.getFullYear() + targetAge);
  
  const today = new Date();
  const diffTime = targetDate.getTime() - today.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  return {
    totalDays: Math.max(0, diffDays),
    years: Math.max(0, Math.floor(diffDays / 365)),
    months: Math.max(0, Math.floor((diffDays % 365) / 30)),
    days: Math.max(0, diffDays)
  };
}

// 解析Excel日期序列号
function parseBirthDate(birthDateStr) {
  if (typeof birthDateStr === 'number') {
    try {
      const excelDate = new Date((birthDateStr - 25569) * 86400 * 1000);
      if (!isNaN(excelDate.getTime()) && excelDate.getFullYear() > 1900 && excelDate.getFullYear() < 2100) {
        return excelDate;
      }
    } catch (e) {
      // 忽略错误
    }
  }
  return null;
}

// 测试不同剩余天数的显示格式
const testCases = [
  { name: '吴小方（仅剩6天）', days: 6, context: '退居二线' },
  { name: '测试25天', days: 25, context: '退居二线' },
  { name: '测试45天（1个月多）', days: 45, context: '退居二线' },
  { name: '测试90天（3个月）', days: 90, context: '法定退休年龄' },
  { name: '测试180天（6个月）', days: 180, context: '法定退休年龄' },
  { name: '测试400天（1年多）', days: 400, context: '法定退休年龄' },
  { name: '测试800天（2年多）', days: 800, context: '法定退休年龄' },
  { name: '已超过（-30天）', days: -30, context: '退休年龄' }
];

console.log('📊 智能显示格式测试：\n');

testCases.forEach((testCase, index) => {
  const format = formatTimeRemaining(testCase.days, testCase.context);
  
  console.log(`${index + 1}. ${testCase.name}:`);
  console.log(`   剩余天数: ${testCase.days}天`);
  
  if (testCase.days > 0) {
    if (format.text) {
      console.log(`   📱 显示格式: 距${testCase.context}剩余${format.text}（剩余[红色]${format.daysText}[/红色]）`);
    } else {
      console.log(`   📱 显示格式: 距${testCase.context}剩余[红色]${format.daysText}[/红色]`);
    }
  } else {
    console.log(`   📱 显示格式: ${format.text}[红色]${Math.abs(testCase.days)}天[/红色]`);
  }
  
  // 显示逻辑说明
  if (testCase.days > 365) {
    console.log(`   💡 逻辑: 大于1年，显示年月 + 突出天数`);
  } else if (testCase.days > 30) {
    console.log(`   💡 逻辑: 大于1个月，显示月份 + 突出天数`);
  } else if (testCase.days > 0) {
    console.log(`   💡 逻辑: 不足1个月，仅突出显示天数`);
  } else {
    console.log(`   💡 逻辑: 已超过，突出显示超过天数`);
  }
  
  console.log('');
});

// 测试真实人员数据
console.log('👥 真实人员测试：\n');

const realPersons = [
  {
    name: '吴小方',
    birthDate: new Date('1967-12-25'), // 假设生日，使其距58岁仅剩6天
    targetAge: 58,
    context: '退居二线'
  },
  {
    name: '韦树林',
    birthDate: new Date('1966-09-18'),
    targetAge: 60,
    context: '法定退休年龄'
  }
];

realPersons.forEach((person, index) => {
  const timeInfo = calculateTimeToAge(person.birthDate, person.targetAge);
  const format = formatTimeRemaining(timeInfo.totalDays, person.context);
  
  console.log(`${index + 1}. ${person.name}:`);
  console.log(`   出生日期: ${person.birthDate.toLocaleDateString()}`);
  console.log(`   目标年龄: ${person.targetAge}岁`);
  console.log(`   剩余天数: ${timeInfo.totalDays}天`);
  
  if (format.text) {
    console.log(`   📱 显示: 距${person.context}剩余${format.text}（剩余[红色]${format.daysText}[/红色]）`);
  } else {
    console.log(`   📱 显示: 距${person.context}剩余[红色]${format.daysText}[/红色]`);
  }
  
  console.log('');
});

console.log('✅ 测试完成！');
console.log('\n🎨 显示特点：');
console.log('1. 智能格式：根据剩余时间长短自动选择最合适的显示方式');
console.log('2. 红色突出：所有剩余天数都用红色背景高亮显示');
console.log('3. 精准计算：基于实际日期差计算，避免月份天数误差');
console.log('4. 行内布局：文字和高亮天数在同一行显示');

console.log('\n📐 格式规则：');
console.log('- 大于1年：X年X月（剩余[红色]X天[/红色]）');
console.log('- 大于1个月：X个月（剩余[红色]X天[/红色]）');
console.log('- 不足1个月：距XX剩余[红色]X天[/红色]');
console.log('- 已超过：已超过XX[红色]X天[/红色]');
